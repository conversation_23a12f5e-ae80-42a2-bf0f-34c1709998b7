# 6）Docker Swarm 监控和运维方案

## 📊 监控架构设计

基于您选择的监控运维服务栈，设计了完整的监控体系：

```mermaid
graph TB
    subgraph "数据收集层"
        A[Node Exporter<br/>主机监控]
        B[cAdvisor<br/>容器监控]
        C[Blackbox Exporter<br/>网络探测]
        D[Application Metrics<br/>应用指标]
    end
    
    subgraph "数据存储层"
        E[Prometheus<br/>指标存储]
        F[AlertManager<br/>告警管理]
    end
    
    subgraph "数据展示层"
        G[Grafana<br/>可视化面板]
        H[Portainer<br/>容器管理]
    end
    
    subgraph "告警通知层"
        I[钉钉告警<br/>即时通知]
        J[邮件告警<br/>详细报告]
        K[短信告警<br/>紧急通知]
    end
    
    A --> E
    B --> E
    C --> E
    D --> E
    E --> F
    E --> G
    F --> I
    F --> J
    F --> K
    G --> H
```

## 🎯 监控指标体系

### 基础设施监控

#### 主机监控指标
```yaml
# Node Exporter 监控指标
host_metrics:
  cpu:
    - cpu_usage_percent          # CPU 使用率
    - cpu_load_average          # 系统负载
    - cpu_context_switches      # 上下文切换
    - cpu_interrupts           # 中断次数
  
  memory:
    - memory_usage_percent      # 内存使用率
    - memory_available_bytes    # 可用内存
    - memory_swap_usage        # 交换分区使用
    - memory_cache_buffers     # 缓存和缓冲区
  
  disk:
    - disk_usage_percent       # 磁盘使用率
    - disk_io_read_bytes       # 磁盘读取字节
    - disk_io_write_bytes      # 磁盘写入字节
    - disk_io_operations       # 磁盘 I/O 操作数
  
  network:
    - network_receive_bytes    # 网络接收字节
    - network_transmit_bytes   # 网络发送字节
    - network_receive_packets  # 网络接收包数
    - network_transmit_packets # 网络发送包数
    - network_errors          # 网络错误数
```

#### 容器监控指标
```yaml
# cAdvisor 监控指标
container_metrics:
  resource_usage:
    - container_cpu_usage_percent     # 容器 CPU 使用率
    - container_memory_usage_bytes    # 容器内存使用量
    - container_memory_limit_bytes    # 容器内存限制
    - container_network_io_bytes      # 容器网络 I/O
    - container_fs_io_bytes          # 容器文件系统 I/O
  
  container_state:
    - container_start_time           # 容器启动时间
    - container_restart_count        # 容器重启次数
    - container_oom_kills           # OOM 杀死次数
    - container_exit_code           # 容器退出码
```

### 应用服务监控

#### 数据库监控
```yaml
# PostgreSQL 监控指标
postgres_metrics:
  connections:
    - pg_stat_database_numbackends   # 活跃连接数
    - pg_settings_max_connections    # 最大连接数
    - pg_stat_activity_count         # 活动查询数
  
  performance:
    - pg_stat_database_tup_returned  # 返回行数
    - pg_stat_database_tup_fetched   # 获取行数
    - pg_stat_database_tup_inserted  # 插入行数
    - pg_stat_database_tup_updated   # 更新行数
    - pg_stat_database_tup_deleted   # 删除行数
  
  locks:
    - pg_locks_count                 # 锁数量
    - pg_stat_database_deadlocks     # 死锁数量

# Redis 监控指标
redis_metrics:
  memory:
    - redis_memory_used_bytes        # 已使用内存
    - redis_memory_max_bytes         # 最大内存
    - redis_memory_fragmentation_ratio # 内存碎片率
  
  operations:
    - redis_commands_processed_total # 处理命令总数
    - redis_keyspace_hits_total      # 键空间命中数
    - redis_keyspace_misses_total    # 键空间未命中数
    - redis_connected_clients        # 连接客户端数

# TDengine 监控指标
tdengine_metrics:
  database:
    - td_database_count              # 数据库数量
    - td_table_count                 # 表数量
    - td_vnode_count                 # 虚拟节点数量
  
  performance:
    - td_insert_rate                 # 插入速率
    - td_query_rate                  # 查询速率
    - td_disk_usage                  # 磁盘使用量
```

#### 业务应用监控
```yaml
# 应用服务监控指标
application_metrics:
  http_requests:
    - http_requests_total            # HTTP 请求总数
    - http_request_duration_seconds  # 请求响应时间
    - http_requests_per_second       # 每秒请求数
    - http_response_status_codes     # 响应状态码分布
  
  business_metrics:
    - iot_data_ingestion_rate        # IoT 数据接入速率
    - iot_data_processing_latency    # 数据处理延迟
    - user_active_sessions           # 活跃用户会话
    - system_error_rate              # 系统错误率
    - data_storage_growth_rate       # 数据存储增长率
```

## 🚨 告警规则配置

### 基础设施告警
```yaml
# prometheus/alert.rules.yml
groups:
  - name: infrastructure_alerts
    rules:
      # CPU 使用率告警
      - alert: HighCPUUsage
        expr: 100 - (avg by(instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
        for: 5m
        labels:
          severity: warning
          category: infrastructure
        annotations:
          summary: "主机 CPU 使用率过高"
          description: "主机 {{ $labels.instance }} CPU 使用率为 {{ $value }}%，超过 80% 阈值"
      
      # 内存使用率告警
      - alert: HighMemoryUsage
        expr: (1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100 > 85
        for: 5m
        labels:
          severity: warning
          category: infrastructure
        annotations:
          summary: "主机内存使用率过高"
          description: "主机 {{ $labels.instance }} 内存使用率为 {{ $value }}%，超过 85% 阈值"
      
      # 磁盘使用率告警
      - alert: HighDiskUsage
        expr: (1 - (node_filesystem_avail_bytes / node_filesystem_size_bytes)) * 100 > 90
        for: 5m
        labels:
          severity: critical
          category: infrastructure
        annotations:
          summary: "磁盘空间不足"
          description: "主机 {{ $labels.instance }} 磁盘 {{ $labels.mountpoint }} 使用率为 {{ $value }}%，超过 90% 阈值"
      
      # 服务不可用告警
      - alert: ServiceDown
        expr: up == 0
        for: 1m
        labels:
          severity: critical
          category: service
        annotations:
          summary: "服务不可用"
          description: "服务 {{ $labels.job }} 实例 {{ $labels.instance }} 已停止响应"

  - name: database_alerts
    rules:
      # PostgreSQL 连接数告警
      - alert: PostgreSQLHighConnections
        expr: pg_stat_database_numbackends / pg_settings_max_connections * 100 > 80
        for: 5m
        labels:
          severity: warning
          category: database
        annotations:
          summary: "PostgreSQL 连接数过高"
          description: "数据库 {{ $labels.datname }} 连接数使用率为 {{ $value }}%，超过 80% 阈值"
      
      # Redis 内存使用告警
      - alert: RedisHighMemoryUsage
        expr: redis_memory_used_bytes / redis_memory_max_bytes * 100 > 90
        for: 5m
        labels:
          severity: warning
          category: database
        annotations:
          summary: "Redis 内存使用率过高"
          description: "Redis 实例 {{ $labels.instance }} 内存使用率为 {{ $value }}%，超过 90% 阈值"

  - name: application_alerts
    rules:
      # HTTP 错误率告警
      - alert: HighHTTPErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) / rate(http_requests_total[5m]) * 100 > 5
        for: 5m
        labels:
          severity: warning
          category: application
        annotations:
          summary: "HTTP 错误率过高"
          description: "服务 {{ $labels.service }} HTTP 5xx 错误率为 {{ $value }}%，超过 5% 阈值"
      
      # 响应时间告警
      - alert: HighResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 2
        for: 5m
        labels:
          severity: warning
          category: application
        annotations:
          summary: "响应时间过长"
          description: "服务 {{ $labels.service }} 95% 响应时间为 {{ $value }}s，超过 2s 阈值"
```

### 告警通知配置
```yaml
# prometheus/alertmanager.yml
global:
  smtp_smarthost: 'smtp.example.com:587'
  smtp_from: '<EMAIL>'
  smtp_auth_username: '<EMAIL>'
  smtp_auth_password: 'password'

route:
  group_by: ['alertname', 'category']
  group_wait: 10s
  group_interval: 10s
  repeat_interval: 1h
  receiver: 'default'
  routes:
    # 关键告警立即通知
    - match:
        severity: critical
      receiver: 'critical-alerts'
      group_wait: 0s
      repeat_interval: 5m
    
    # 基础设施告警
    - match:
        category: infrastructure
      receiver: 'infrastructure-team'
    
    # 数据库告警
    - match:
        category: database
      receiver: 'database-team'
    
    # 应用告警
    - match:
        category: application
      receiver: 'application-team'

receivers:
  - name: 'default'
    webhook_configs:
      - url: 'http://alone-alert:5000/webhook'
        send_resolved: true

  - name: 'critical-alerts'
    webhook_configs:
      - url: 'http://alone-alert:5000/webhook'
        send_resolved: true
    email_configs:
      - to: '<EMAIL>'
        subject: '🚨 紧急告警: {{ .GroupLabels.alertname }}'
        body: |
          告警详情:
          {{ range .Alerts }}
          - 告警: {{ .Annotations.summary }}
          - 描述: {{ .Annotations.description }}
          - 时间: {{ .StartsAt }}
          {{ end }}

  - name: 'infrastructure-team'
    email_configs:
      - to: '<EMAIL>'
        subject: '⚠️ 基础设施告警: {{ .GroupLabels.alertname }}'

  - name: 'database-team'
    email_configs:
      - to: '<EMAIL>'
        subject: '🗄️ 数据库告警: {{ .GroupLabels.alertname }}'

  - name: 'application-team'
    email_configs:
      - to: '<EMAIL>'
        subject: '📱 应用告警: {{ .GroupLabels.alertname }}'
```

## 📈 Grafana 仪表板配置

### 基础设施监控面板
```json
{
  "dashboard": {
    "title": "基础设施监控",
    "panels": [
      {
        "title": "CPU 使用率",
        "type": "stat",
        "targets": [
          {
            "expr": "100 - (avg by(instance) (irate(node_cpu_seconds_total{mode=\"idle\"}[5m])) * 100)",
            "legendFormat": "{{ instance }}"
          }
        ],
        "fieldConfig": {
          "defaults": {
            "thresholds": {
              "steps": [
                {"color": "green", "value": 0},
                {"color": "yellow", "value": 70},
                {"color": "red", "value": 90}
              ]
            }
          }
        }
      },
      {
        "title": "内存使用率",
        "type": "stat",
        "targets": [
          {
            "expr": "(1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100",
            "legendFormat": "{{ instance }}"
          }
        ]
      },
      {
        "title": "磁盘使用率",
        "type": "bargauge",
        "targets": [
          {
            "expr": "(1 - (node_filesystem_avail_bytes / node_filesystem_size_bytes)) * 100",
            "legendFormat": "{{ instance }}:{{ mountpoint }}"
          }
        ]
      },
      {
        "title": "网络流量",
        "type": "timeseries",
        "targets": [
          {
            "expr": "irate(node_network_receive_bytes_total[5m])",
            "legendFormat": "{{ instance }} 接收"
          },
          {
            "expr": "irate(node_network_transmit_bytes_total[5m])",
            "legendFormat": "{{ instance }} 发送"
          }
        ]
      }
    ]
  }
}
```

### 应用服务监控面板
```json
{
  "dashboard": {
    "title": "应用服务监控",
    "panels": [
      {
        "title": "服务状态",
        "type": "stat",
        "targets": [
          {
            "expr": "up",
            "legendFormat": "{{ job }}"
          }
        ],
        "fieldConfig": {
          "defaults": {
            "mappings": [
              {"options": {"0": {"text": "离线", "color": "red"}}, "type": "value"},
              {"options": {"1": {"text": "在线", "color": "green"}}, "type": "value"}
            ]
          }
        }
      },
      {
        "title": "HTTP 请求速率",
        "type": "timeseries",
        "targets": [
          {
            "expr": "rate(http_requests_total[5m])",
            "legendFormat": "{{ service }} - {{ method }}"
          }
        ]
      },
      {
        "title": "HTTP 响应时间",
        "type": "timeseries",
        "targets": [
          {
            "expr": "histogram_quantile(0.50, rate(http_request_duration_seconds_bucket[5m]))",
            "legendFormat": "50% - {{ service }}"
          },
          {
            "expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))",
            "legendFormat": "95% - {{ service }}"
          }
        ]
      },
      {
        "title": "错误率",
        "type": "timeseries",
        "targets": [
          {
            "expr": "rate(http_requests_total{status=~\"5..\"}[5m]) / rate(http_requests_total[5m]) * 100",
            "legendFormat": "{{ service }} 错误率"
          }
        ]
      }
    ]
  }
}
```

### 数据库监控面板
```json
{
  "dashboard": {
    "title": "数据库监控",
    "panels": [
      {
        "title": "PostgreSQL 连接数",
        "type": "timeseries",
        "targets": [
          {
            "expr": "pg_stat_database_numbackends",
            "legendFormat": "{{ datname }} 活跃连接"
          },
          {
            "expr": "pg_settings_max_connections",
            "legendFormat": "最大连接数"
          }
        ]
      },
      {
        "title": "Redis 内存使用",
        "type": "timeseries",
        "targets": [
          {
            "expr": "redis_memory_used_bytes",
            "legendFormat": "已使用内存"
          },
          {
            "expr": "redis_memory_max_bytes",
            "legendFormat": "最大内存"
          }
        ]
      },
      {
        "title": "TDengine 数据写入速率",
        "type": "timeseries",
        "targets": [
          {
            "expr": "rate(td_insert_total[5m])",
            "legendFormat": "写入速率"
          }
        ]
      }
    ]
  }
}
```

## 🔧 运维自动化

### 自动扩缩容脚本
```bash
#!/bin/bash
# auto_scaling.sh - 自动扩缩容脚本

set -e

# 配置参数
CPU_SCALE_UP_THRESHOLD=80
CPU_SCALE_DOWN_THRESHOLD=30
MEMORY_SCALE_UP_THRESHOLD=85
MEMORY_SCALE_DOWN_THRESHOLD=40
MIN_REPLICAS=1
MAX_REPLICAS=5

# 获取服务当前副本数
get_current_replicas() {
    local service_name=$1
    docker service inspect "$service_name" --format '{{.Spec.Mode.Replicated.Replicas}}'
}

# 获取服务 CPU 使用率
get_service_cpu_usage() {
    local service_name=$1
    # 通过 Prometheus API 获取 CPU 使用率
    curl -s "http://prometheus:9090/api/v1/query?query=avg(rate(container_cpu_usage_seconds_total{container_label_com_docker_swarm_service_name=\"$service_name\"}[5m]))*100" | \
    jq -r '.data.result[0].value[1]' 2>/dev/null || echo "0"
}

# 获取服务内存使用率
get_service_memory_usage() {
    local service_name=$1
    # 通过 Prometheus API 获取内存使用率
    curl -s "http://prometheus:9090/api/v1/query?query=avg(container_memory_usage_bytes{container_label_com_docker_swarm_service_name=\"$service_name\"}/container_spec_memory_limit_bytes{container_label_com_docker_swarm_service_name=\"$service_name\"})*100" | \
    jq -r '.data.result[0].value[1]' 2>/dev/null || echo "0"
}

# 扩展服务
scale_service() {
    local service_name=$1
    local new_replicas=$2
    local current_replicas=$(get_current_replicas "$service_name")
    
    if [ "$new_replicas" -ne "$current_replicas" ]; then
        echo "扩展服务 $service_name: $current_replicas -> $new_replicas"
        docker service scale "$service_name=$new_replicas"
        
        # 记录扩缩容事件
        echo "$(date): 服务 $service_name 从 $current_replicas 扩展到 $new_replicas" >> /var/log/auto_scaling.log
    fi
}

# 检查并执行自动扩缩容
check_and_scale() {
    local service_name=$1
    local current_replicas=$(get_current_replicas "$service_name")
    local cpu_usage=$(get_service_cpu_usage "$service_name")
    local memory_usage=$(get_service_memory_usage "$service_name")
    
    echo "检查服务 $service_name: CPU=$cpu_usage%, Memory=$memory_usage%, Replicas=$current_replicas"
    
    # 扩容条件
    if (( $(echo "$cpu_usage > $CPU_SCALE_UP_THRESHOLD" | bc -l) )) || \
       (( $(echo "$memory_usage > $MEMORY_SCALE_UP_THRESHOLD" | bc -l) )); then
        if [ "$current_replicas" -lt "$MAX_REPLICAS" ]; then
            local new_replicas=$((current_replicas + 1))
            scale_service "$service_name" "$new_replicas"
        fi
    # 缩容条件
    elif (( $(echo "$cpu_usage < $CPU_SCALE_DOWN_THRESHOLD" | bc -l) )) && \
         (( $(echo "$memory_usage < $MEMORY_SCALE_DOWN_THRESHOLD" | bc -l) )); then
        if [ "$current_replicas" -gt "$MIN_REPLICAS" ]; then
            local new_replicas=$((current_replicas - 1))
            scale_service "$service_name" "$new_replicas"
        fi
    fi
}

# 需要自动扩缩容的服务列表
SCALABLE_SERVICES=(
    "iot_data-storage"
    "iot_data-monitor"
    "core_frontend-gateway"
)

# 执行自动扩缩容检查
for service in "${SCALABLE_SERVICES[@]}"; do
    check_and_scale "$service"
done
```

### 健康检查和自愈脚本
```bash
#!/bin/bash
# health_monitor.sh - 健康监控和自愈脚本

set -e

# 日志函数
log_info() { echo "[INFO] $(date '+%Y-%m-%d %H:%M:%S') $1"; }
log_warn() { echo "[WARN] $(date '+%Y-%m-%d %H:%M:%S') $1"; }
log_error() { echo "[ERROR] $(date '+%Y-%m-%d %H:%M:%S') $1"; }

# 检查服务健康状态
check_service_health() {
    local service_name=$1
    local replicas=$(docker service ls --filter name="$service_name" --format '{{.Replicas}}')
    
    if echo "$replicas" | grep -q "/"; then
        local current=$(echo "$replicas" | cut -d'/' -f1)
        local desired=$(echo "$replicas" | cut -d'/' -f2)
        
        if [ "$current" -ne "$desired" ]; then
            log_warn "服务 $service_name 副本数不匹配: $current/$desired"
            return 1
        fi
    fi
    
    return 0
}

# 重启不健康的服务
restart_unhealthy_service() {
    local service_name=$1
    
    log_info "重启不健康的服务: $service_name"
    
    # 获取失败的任务
    local failed_tasks=$(docker service ps "$service_name" --filter desired-state=running --format '{{.CurrentState}}' | grep -c "Failed\|Rejected" || true)
    
    if [ "$failed_tasks" -gt 0 ]; then
        log_info "发现 $failed_tasks 个失败任务，强制更新服务"
        docker service update --force "$service_name"
        
        # 等待服务恢复
        sleep 60
        
        if check_service_health "$service_name"; then
            log_info "服务 $service_name 恢复正常"
        else
            log_error "服务 $service_name 重启后仍然不健康"
            # 发送告警通知
            send_alert "服务自愈失败" "服务 $service_name 重启后仍然不健康，需要人工干预"
        fi
    fi
}

# 发送告警通知
send_alert() {
    local title=$1
    local message=$2
    
    # 发送到钉钉群
    curl -X POST "http://alone-alert:5000/webhook" \
        -H "Content-Type: application/json" \
        -d "{\"title\":\"$title\",\"message\":\"$message\",\"timestamp\":\"$(date)\"}"
}

# 检查磁盘空间
check_disk_space() {
    local threshold=90
    
    df -h | grep -vE '^Filesystem|tmpfs|cdrom' | awk '{ print $5 " " $1 }' | while read output; do
        usage=$(echo $output | awk '{ print $1}' | cut -d'%' -f1)
        partition=$(echo $output | awk '{ print $2 }')
        
        if [ $usage -ge $threshold ]; then
            log_warn "磁盘空间不足: $partition 使用率 $usage%"
            send_alert "磁盘空间告警" "分区 $partition 使用率达到 $usage%，请及时清理"
        fi
    done
}

# 清理 Docker 资源
cleanup_docker_resources() {
    log_info "清理 Docker 资源..."
    
    # 清理未使用的镜像
    docker image prune -f
    
    # 清理未使用的容器
    docker container prune -f
    
    # 清理未使用的网络
    docker network prune -f
    
    # 清理未使用的卷（谨慎操作）
    # docker volume prune -f
    
    log_info "Docker 资源清理完成"
}

# 主监控循环
main_monitor() {
    log_info "开始健康监控..."
    
    # 获取所有服务
    local services=$(docker service ls --format '{{.Name}}')
    
    for service in $services; do
        if ! check_service_health "$service"; then
            restart_unhealthy_service "$service"
        fi
    done
    
    # 检查磁盘空间
    check_disk_space
    
    # 定期清理 Docker 资源
    if [ $(date +%H) -eq 2 ]; then  # 凌晨2点执行清理
        cleanup_docker_resources
    fi
    
    log_info "健康监控完成"
}

# 执行监控
main_monitor
```

### 日志管理脚本
```bash
#!/bin/bash
# log_management.sh - 日志管理脚本

set -e

LOG_BASE_DIR="/var/log/swarm"
RETENTION_DAYS=30
MAX_LOG_SIZE="100M"

# 创建日志目录
mkdir -p "$LOG_BASE_DIR"/{services,system,audit}

# 收集服务日志
collect_service_logs() {
    local service_name=$1
    local log_file="$LOG_BASE_DIR/services/${service_name}_$(date +%Y%m%d).log"
    
    echo "收集服务日志: $service_name"
    docker service logs --since 24h --timestamps "$service_name" > "$log_file" 2>&1
}

# 收集系统日志
collect_system_logs() {
    local log_file="$LOG_BASE_DIR/system/system_$(date +%Y%m%d).log"
    
    echo "收集系统日志"
    {
        echo "=== Docker 系统信息 ==="
        docker system df
        echo
        
        echo "=== Swarm 节点状态 ==="
        docker node ls
        echo
        
        echo "=== 服务状态 ==="
        docker service ls
        echo
        
        echo "=== 系统资源使用 ==="
        free -h
        df -h
        echo
    } > "$log_file"
}

# 轮转日志文件
rotate_logs() {
    echo "轮转日志文件..."
    
    find "$LOG_BASE_DIR" -name "*.log" -size +"$MAX_LOG_SIZE" -exec gzip {} \;
    find "$LOG_BASE_DIR" -name "*.gz" -mtime +$RETENTION_DAYS -delete
    
    echo "日志轮转完成"
}

# 分析错误日志
analyze_error_logs() {
    echo "分析错误日志..."
    
    local error_summary="$LOG_BASE_DIR/error_summary_$(date +%Y%m%d).txt"
    
    {
        echo "=== 错误日志汇总 $(date) ==="
        echo
        
        # 统计各类错误
        echo "Docker 服务错误:"
        find "$LOG_BASE_DIR/services" -name "*.log" -mtime -1 -exec grep -i "error\|failed\|exception" {} \; | \
        awk '{print $1}' | sort | uniq -c | sort -nr | head -10
        echo
        
        echo "系统错误:"
        journalctl --since "24 hours ago" --priority=err --no-pager | head -20
        echo
        
    } > "$error_summary"
    
    # 如果发现严重错误，发送告警
    local error_count=$(grep -c "ERROR\|CRITICAL" "$error_summary" || true)
    if [ "$error_count" -gt 10 ]; then
        send_alert "错误日志告警" "发现 $error_count 个严重
