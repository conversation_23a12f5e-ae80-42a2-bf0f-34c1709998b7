# 前端网关配置, 统一前端入口，代理所有前后端服务请求

# 全局配置
variables_hash_bucket_size 128;        # 解决 Nginx 变量名称过长的问题

#==============================================================================
# HTTPS 切换说明
#==============================================================================
# 如何启用 HTTPS：
# 1. 将下面紧邻部分的 HTTP 重定向服务的注释取消
# 2. 将主服务器的监听端口改为 443
# 3. 取消 SSL 证书配置、SSL 安全配置、HSTS 安全头的注释
# 4. 取消所有 location 中 X-Forwarded-Proto https 的注释
# 5. 确保 SSL 证书文件存在于 /etc/nginx/ssl/ 目录中
# 注意：其他 location 配置中的 #proxy_set_header X-Forwarded-Proto https; 也需要根据需要取消注释以支持 HTTPS

# HTTP 重定向服务 - 将所有 HTTP 请求重定向到 HTTPS（默认注释）
#server {
#  listen       80;
#  server_name  localhost;
#
#  # 将所有 HTTP 请求重定向到 HTTPS
#  return 301 https://${DOLLAR}server_name${DOLLAR}request_uri;
#}

# 主服务器配置
server {
  listen       80;
  # 切换到 HTTPS 时注释掉前一行, 取消注释后一行
  #listen       443 ssl http2;
  server_name  localhost;
  root         /usr/share/nginx/html;

  ## SSL 证书配置（启用 HTTPS 时取消注释）
  #ssl_certificate     /etc/nginx/ssl/server.crt;
  #ssl_certificate_key /etc/nginx/ssl/server.key;
  ## SSL 安全配置
  #ssl_protocols TLSv1.2 TLSv1.3;
  #ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE:ECDH:AES:HIGH:!NULL:!aNULL:!MD5:!ADH:!RC4;
  #ssl_prefer_server_ciphers on;
  #ssl_session_cache shared:SSL:10m;
  #ssl_session_timeout 10m;
  ## HSTS 安全头（启用 HTTPS 时取消注释）
  #add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
  #add_header X-Frame-Options DENY;
  #add_header X-Content-Type-Options nosniff;
  #add_header X-XSS-Protection "1; mode=block";

  # DNS 解析配置
  resolver 127.0.0.11 valid=10s ipv6=off;    # 使用 Docker 网络进行 DNS 解析

  # 压缩配置
  gzip on;
  gzip_proxied any;
  gzip_comp_level 6;
  gzip_types
    text/css
    text/javascript
    text/xml
    text/plain
    application/javascript
    application/x-javascript;
  proxy_cache_valid 5m;

  # 日志配置
  rewrite_log on;
  error_log /var/log/nginx/debug.log debug;

  # 二次开发自定义配置
  include /etc/nginx/conf.d/localhost.etc/*.conf;

  #==========================================================================
  # 核心业务服务代理
  #==========================================================================

  # 基座平台 - 主入口（默认路由）
  set ${DOLLAR}upstream_frontend_module_base "http://frontend-module-base";
  location / {
    proxy_pass ${DOLLAR}upstream_frontend_module_base;
    #proxy_set_header X-Forwarded-Proto https;    # 启用 HTTPS 时取消注释
  }

  # 核心后端 API - BladeX 用户权限系统
  set ${DOLLAR}upstream_backend_bladex_process_user_permission "http://backend-process-user-permission:9401";
  location /api/ {
    rewrite /api/(.+) /${DOLLAR}1 break;
    proxy_pass ${DOLLAR}upstream_backend_bladex_process_user_permission;
    #proxy_set_header X-Forwarded-Proto https;    # 启用 HTTPS 时取消注释
    client_max_body_size 1024m;
    proxy_set_header Host ${DOLLAR}proxy_host;
    proxy_set_header X-Real-IP ${DOLLAR}remote_addr;
    proxy_set_header X-Forwarded-For ${DOLLAR}proxy_add_x_forwarded_for;
    proxy_http_version 1.1;
    proxy_set_header Upgrade ${DOLLAR}http_upgrade;
    proxy_set_header Connection "upgrade";
  }

  # 平台授权服务
  set ${DOLLAR}upstream_service_authorization "http://service-authorization:22260";
  location /supa-service-auth {
    rewrite /supa-service-auth/(.*) /${DOLLAR}1 break;
    proxy_pass ${DOLLAR}upstream_service_authorization;
    #proxy_set_header X-Forwarded-Proto https;    # 启用 HTTPS 时取消注释
  }

  #==========================================================================
  # 系统管理模块
  #==========================================================================

  # 系统设置模块
  set ${DOLLAR}upstream_frontend_module_system_admin "http://frontend-module-system_admin";
  location /module-system_admin {
    rewrite /module-system_admin/(.+) /${DOLLAR}1 break;
    proxy_pass ${DOLLAR}upstream_frontend_module_system_admin;
    #proxy_set_header X-Forwarded-Proto https;    # 启用 HTTPS 时取消注释
  }

  #==========================================================================
  # 数据服务模块
  #==========================================================================

  # 数据接入模块
  set ${DOLLAR}upstream_frontend_module_data_access "http://frontend-module-data_access";
  location /module-data_access {
    rewrite /module-data_access/(.+) /${DOLLAR}1 break;
    proxy_pass ${DOLLAR}upstream_frontend_module_data_access;
    #proxy_set_header X-Forwarded-Proto https;    # 启用 HTTPS 时取消注释
  }

  # 数据视图模块
  set ${DOLLAR}upstream_frontend_module_data_view "http://frontend-module-data_view";
  location /module-data_view {
    rewrite /module-data_view/(.+) /${DOLLAR}1 break;
    proxy_pass ${DOLLAR}upstream_frontend_module_data_view;
    #proxy_set_header X-Forwarded-Proto https;    # 启用 HTTPS 时取消注释
  }

  # 数据联网模块
  set ${DOLLAR}upstream_frontend_biz_data_ims "http://frontend-biz-data_ims";
  location /base-data_ims {
    rewrite /base-data_ims/(.+) /${DOLLAR}1 break;
    proxy_pass ${DOLLAR}upstream_frontend_biz_data_ims;
    #proxy_set_header X-Forwarded-Proto https;    # 启用 HTTPS 时取消注释
  }

  # 数据接口后端服务
  set ${DOLLAR}upstream_backend_data_monitor "http://backend-data-monitor:9001";
  location /interface {
    client_max_body_size 10m;
    proxy_pass ${DOLLAR}upstream_backend_data_monitor;
    #proxy_set_header X-Forwarded-Proto https;    # 启用 HTTPS 时取消注释
  }

  # 数据存储后端服务
  set ${DOLLAR}upstream_backend_data_storage "http://backend-data-storage:9001";
  location /dataroute {
    client_max_body_size 10m;
    proxy_pass ${DOLLAR}upstream_backend_data_storage;
    proxy_http_version 1.1;
    proxy_set_header Upgrade ${DOLLAR}http_upgrade;
    proxy_set_header Connection "upgrade";
    #proxy_set_header X-Forwarded-Proto https;    # 启用 HTTPS 时取消注释
  }

  # 数据预警后端服务
  set ${DOLLAR}upstream_backend_data_warn "http://backend-data-warn:9001";
  location /api/datawarn {
    rewrite /api/(.+) /${DOLLAR}1 break;
    client_max_body_size 10m;
    proxy_pass ${DOLLAR}upstream_backend_data_warn;
    #proxy_set_header X-Forwarded-Proto https;    # 启用 HTTPS 时取消注释
  }

  # 数据网关 - Red-Node
  set ${DOLLAR}upstream_rednode "http://rednode:1880";
  location /data-gw {
    client_max_body_size 20m;
    rewrite /data-gw/(.*) /${DOLLAR}1 break;

    proxy_pass ${DOLLAR}upstream_rednode;
    proxy_http_version 1.1;
    proxy_set_header Upgrade ${DOLLAR}http_upgrade;
    proxy_set_header Connection "upgrade";
    #proxy_set_header X-Forwarded-Proto https;    # 启用 HTTPS 时取消注释
  }
  #set ${DOLLAR}upstream_rednode2 "http://rednode_1:1880";
  #location /data-gw2 {
  #  client_max_body_size 10m;
  #  rewrite /data-gw2/(.*) /${DOLLAR}1 break;
  #  proxy_pass ${DOLLAR}upstream_rednode2;
  #  proxy_http_version 1.1;
  #  proxy_set_header Upgrade ${DOLLAR}http_upgrade;
  #  proxy_set_header Connection "upgrade";
  #  #proxy_set_header X-Forwarded-Proto https;    # 启用 HTTPS 时取消注释
  #}

  # FTP 服务器
  # 反向代理 /ftp-admin 到 ftpserver 容器的 8080 端口
  set ${DOLLAR}upstream_ftpserver "http://ftpserver:8080/ftp-admin/";
  location /ftp-admin/ {
    proxy_pass ${DOLLAR}upstream_ftpserver;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;

    # WebSocket support
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection "upgrade";
    # Timeout settings
    proxy_connect_timeout 60s;
    proxy_send_timeout 60s;
    proxy_read_timeout 60s;
    #proxy_set_header X-Forwarded-Proto https;    # 启用 HTTPS 时取消注释
  }
  # 处理不带尾部斜杠的 /ftp-admin 请求
  location = /ftp-admin {
    return 301 /ftp-admin/;
  }

  #==========================================================================
  # 拓扑图模块
  #==========================================================================

  # 拓扑渲染器
  set ${DOLLAR}upstream_frontend_topo_render "http://frontend-module-topo_render";
  location /module-topo_render {
    rewrite /module-topo_render/(.+) /${DOLLAR}1 break;
    proxy_pass ${DOLLAR}upstream_frontend_topo_render;
    #proxy_set_header X-Forwarded-Proto https;    # 启用 HTTPS 时取消注释
  }
  # 拓扑渲染器背景图
  location /images/bg-net.png {
    proxy_pass ${DOLLAR}upstream_frontend_topo_render;
    #proxy_set_header X-Forwarded-Proto https;    # 启用 HTTPS 时取消注释
  }

  # 拓扑编辑器
  set ${DOLLAR}upstream_frontend_module_topo_editor "http://frontend-module-topo_editor";
  location /module-topo_editor {
    rewrite /module-topo_editor/(.+) /${DOLLAR}1 break;
    proxy_pass ${DOLLAR}upstream_frontend_module_topo_editor;
    #proxy_set_header X-Forwarded-Proto https;    # 启用 HTTPS 时取消注释
  }

  # 拓扑渲染器（嵌入式）
  set ${DOLLAR}upstream_frontend_topo_embed "http://frontend-module-topo_embed";
  location /preview-topo {
    rewrite /preview-topo/(.+) /${DOLLAR}1 break;
    proxy_pass ${DOLLAR}upstream_frontend_topo_embed;
    #proxy_set_header X-Forwarded-Proto https;    # 启用 HTTPS 时取消注释
  }

  # 拓扑编辑器（新版）
  set ${DOLLAR}upstream_module_prtopo_editor "http://frontend-base-prtopo-editor";
  location /module-prtopo_editor {
    rewrite /module-prtopo_editor/(.+) /${DOLLAR}1 break;
    proxy_pass ${DOLLAR}upstream_module_prtopo_editor;
    #proxy_set_header X-Forwarded-Proto https;    # 启用 HTTPS 时取消注释
  }

  # 拓扑渲染器（嵌入式 新版）
  set ${DOLLAR}upstream_module_prtopo_embed "http://frontend-base-prtopo-embed";
  location /preview-prtopo {
    rewrite /preview-prtopo/(.+) /${DOLLAR}1 break;
    proxy_pass ${DOLLAR}upstream_module_prtopo_embed;
    #proxy_set_header X-Forwarded-Proto https;    # 启用 HTTPS 时取消注释
  }

  # 拓扑设备模型图片
  location /interface/images {
    rewrite /interface/images/(.+) /${DOLLAR}1 break;
    root /usr/share/nginx/html/topo/images;
  }

  #==========================================================================
  # 页面构建模块
  #==========================================================================

  # 页面编辑器
  set ${DOLLAR}upstream_frontend_module_page_builder "http://frontend-module-page_builder";
  location /module-page_builder {
    rewrite /module-page_builder/(.+) /${DOLLAR}1 break;
    proxy_pass ${DOLLAR}upstream_frontend_module_page_builder;
    #proxy_set_header X-Forwarded-Proto https;    # 启用 HTTPS 时取消注释
  }

  # 页面渲染器
  set ${DOLLAR}upstream_frontend_module_page_render "http://frontend-module-page_render";
  location /module-page_render {
    rewrite /module-page_render/(.+) /${DOLLAR}1 break;
    proxy_pass ${DOLLAR}upstream_frontend_module_page_render;
    #proxy_set_header X-Forwarded-Proto https;    # 启用 HTTPS 时取消注释
  }

  #==========================================================================
  # 业务系统模块
  #==========================================================================

  # 业务系统前端
  set ${DOLLAR}upstream_frontend_base_business_serve "http://frontend-base-business_serve";
  location /base-business_serve {
    rewrite /base-business_serve/(.+) /${DOLLAR}1 break;
    proxy_pass ${DOLLAR}upstream_frontend_base_business_serve;
    #proxy_set_header X-Forwarded-Proto https;    # 启用 HTTPS 时取消注释
  }

  # MES 业务后端服务
  set ${DOLLAR}upstream_backend_mes_biz "http://backend-mes-biz:9001";
  location /mes-biz {
    client_max_body_size 100m;
    proxy_pass ${DOLLAR}upstream_backend_mes_biz;
    #proxy_set_header X-Forwarded-Proto https;    # 启用 HTTPS 时取消注释
  }

  # 预警报警前端模块
  set ${DOLLAR}upstream_frontend_module_alarm_serve "http://frontend-module-alarm_serve";
  location /base-alarm_serve {
    rewrite /base-alarm_serve/(.+) /${DOLLAR}1 break;
    proxy_pass ${DOLLAR}upstream_frontend_module_alarm_serve;
    #proxy_set_header X-Forwarded-Proto https;    # 启用 HTTPS 时取消注释
  }

  # 消息推送后端服务
  set ${DOLLAR}upstream_backend_push_msg "http://backend-push-msg:9001";
  location /push-msg {
    client_max_body_size 100m;
    proxy_pass ${DOLLAR}upstream_backend_push_msg;
    #proxy_set_header X-Forwarded-Proto https;    # 启用 HTTPS 时取消注释
  }

  #==========================================================================
  # 视频监控系统
  #==========================================================================

  # 视频服务 - SRS 流媒体服务器
  set ${DOLLAR}upstream_video_srs "http://video-srs:1985";
  location /api/video-service/v1 {
    rewrite /api/video-service/(.+) /api/${DOLLAR}1 break;
    proxy_pass ${DOLLAR}upstream_video_srs;
    #proxy_set_header X-Forwarded-Proto https;    # 启用 HTTPS 时取消注释
  }

  # 视频服务 - 视频管理服务器
  set ${DOLLAR}upstream_video_server "http://video-server:9000";
  location /api/video-service {
    rewrite /api/video-service/(.+) /${DOLLAR}1 break;
    proxy_pass ${DOLLAR}upstream_video_server;
    #proxy_set_header X-Forwarded-Proto https;    # 启用 HTTPS 时取消注释
  }

  # 视频服务 - 视频预览服务
  set ${DOLLAR}upstream_video_server_preview "http://video-preview:19850";
  location /api/video-service/videoPreview {
    rewrite /api/video-service/(.+) /${DOLLAR}1 break;
    proxy_pass ${DOLLAR}upstream_video_server_preview;
    #proxy_set_header X-Forwarded-Proto https;    # 启用 HTTPS 时取消注释
  }

  # 视频流 - WebSocket FLV
  set ${DOLLAR}upstream_video_streaming_server "http://videojs-flow:8088";
  location /flv {
    rewrite /flv/(.+) /${DOLLAR}1 break;
    proxy_pass ${DOLLAR}upstream_video_streaming_server;
    proxy_http_version 1.1;
    proxy_set_header Upgrade ${DOLLAR}http_upgrade;
    proxy_set_header Connection "upgrade";
    #proxy_set_header X-Forwarded-Proto https;    # 启用 HTTPS 时取消注释
  }

  # 视频转码 - WebSocket Raw H.264
  location ~ /api/video-transcode/([0-9]+) {
    proxy_pass http://${DOLLAR}arg_HOST:$1;
    proxy_http_version 1.1;
    proxy_set_header Upgrade ${DOLLAR}http_upgrade;
    proxy_set_header Connection "upgrade";
    #proxy_set_header X-Forwarded-Proto https;    # 启用 HTTPS 时取消注释
  }

  #==========================================================================
  # AI 设备集成
  #==========================================================================

  # AI 设备视频流转码
  location /api/ai-device-transcode {
    rewrite /api/ai-device-transcode/(.+) /${DOLLAR}1 break;
    proxy_pass http://${DOLLAR}arg_HOST:${DOLLAR}arg_PORT;
    proxy_http_version 1.1;
    proxy_set_header Upgrade ${DOLLAR}http_upgrade;
    proxy_set_header Connection "upgrade";
    #proxy_set_header X-Forwarded-Proto https;    # 启用 HTTPS 时取消注释
  }

  # AI 设备后端 API
  # 参考: http://nginx.org/en/docs/http/ngx_http_core_module.html#var_http_
  # 注意: Nginx 取 Header field 时会转换为小写，中划线会转换为下划线
  # 例如: X-Device-IP 可以通过 $http_x_device_ip 获取
  location /api/ai-device {
    rewrite /api/ai-device/(.+) /${DOLLAR}1 break;
    proxy_pass http://${DOLLAR}http_x_device_ip:${DOLLAR}http_x_device_port;
    client_max_body_size 1024m;
    proxy_set_header Host ${DOLLAR}proxy_host;
    proxy_set_header X-Real-IP ${DOLLAR}remote_addr;
    proxy_set_header X-Forwarded-For ${DOLLAR}proxy_add_x_forwarded_for;
    #proxy_set_header X-Forwarded-Proto https;    # 启用 HTTPS 时取消注释
  }

  #==========================================================================
  # 移动端服务
  #==========================================================================

  # APP 后端服务
  set ${DOLLAR}upstream_backend_app_api "http://backend-bd-app:9001";
  location /app-backend {
    proxy_pass ${DOLLAR}upstream_backend_app_api;
    #proxy_set_header X-Forwarded-Proto https;    # 启用 HTTPS 时取消注释
  }

  #==========================================================================
  # 工作流系统
  #==========================================================================

  # 工作流前端模块
  set ${DOLLAR}upstream_frontend_base_workflow_serve "http://frontend-base-workflow_serve";
  location /base-workflow_serve {
    rewrite /base-workflow_serve/(.+) /${DOLLAR}1 break;
    proxy_pass ${DOLLAR}upstream_frontend_base_workflow_serve;
    #proxy_set_header X-Forwarded-Proto https;    # 启用 HTTPS 时取消注释
  }

  # 工作流后端服务
  set ${DOLLAR}upstream_backend_workflow "http://backend-workflow:8004";
  location /workflow {
    client_max_body_size 100m;
    proxy_pass ${DOLLAR}upstream_backend_workflow;
    #proxy_set_header X-Forwarded-Proto https;    # 启用 HTTPS 时取消注释
  }

  #==========================================================================
  # GIS 地理信息系统
  #==========================================================================

  # GIS 前端模块
  set ${DOLLAR}upstream_frontend_module_gis "http://frontend-module-gis";
  location /module-gis {
    rewrite /module-gis/(.+) /${DOLLAR}1 break;
    proxy_pass ${DOLLAR}upstream_frontend_module_gis;
    #proxy_set_header X-Forwarded-Proto https;    # 启用 HTTPS 时取消注释
  }

  # GIS 后端业务服务
  set ${DOLLAR}upstream_backend_gis_biz "http://backend-gis-biz:9001";
  location /gis-biz {
    client_max_body_size 10m;
    proxy_pass ${DOLLAR}upstream_backend_gis_biz;
    #proxy_set_header X-Forwarded-Proto https;    # 启用 HTTPS 时取消注释
  }

  # GIS API 服务
  set ${DOLLAR}upstream_backend_gis_api "${UPSTREAM_GIS_API}";
  location /api/gis {
    client_max_body_size 10m;
    rewrite /api/gis/(.+) /${DOLLAR}1 break;
    proxy_pass ${DOLLAR}upstream_backend_gis_api;
    #proxy_set_header X-Forwarded-Proto https;    # 启用 HTTPS 时取消注释
  }

  # GIS 静态资源服务
  set ${DOLLAR}upstream_backend_gis_static "${UPSTREAM_GIS_STATIC}";
  location /gis/source {
    rewrite /gis/(.+) /${DOLLAR}1 break;
    proxy_pass ${DOLLAR}upstream_backend_gis_static;
    #proxy_set_header X-Forwarded-Proto https;    # 启用 HTTPS 时取消注释
  }
  location /minio {
    proxy_pass ${DOLLAR}upstream_backend_gis_static;
    #proxy_set_header X-Forwarded-Proto https;    # 启用 HTTPS 时取消注释
  }
  location /fonts/simsun.ttf {
    proxy_pass ${DOLLAR}upstream_backend_gis_static;
    #proxy_set_header X-Forwarded-Proto https;    # 启用 HTTPS 时取消注释
  }

  # GIS 2D 服务
  location /gisserver {
    rewrite /gisserver/(.+) /${DOLLAR}1 break;
    proxy_pass http://172.17.0.1:50003;
    #proxy_set_header X-Forwarded-Proto https;    # 启用 HTTPS 时取消注释
  }

  # GIS 三维 BIM 服务
  set ${DOLLAR}upstream_backend_gis_bim "${UPSTREAM_GIS_THREE}";
  location /bim {
    proxy_pass ${DOLLAR}upstream_backend_gis_bim;
    #proxy_set_header X-Forwarded-Proto https;    # 启用 HTTPS 时取消注释
  }
  location /app-gis/noToken {
    rewrite /app-gis/noToken/(.+) /noToken/${DOLLAR}1 break;
    proxy_pass ${DOLLAR}upstream_backend_gis_bim;
    #proxy_set_header X-Forwarded-Proto https;    # 启用 HTTPS 时取消注释
  }

  # GIS 三维资源
  set ${DOLLAR}upstream_frontend_module_gis_threed "http://frontend-module-gis";
  location /app-gis/res {
    rewrite /app-gis/res/(.+) /last/res/${DOLLAR}1 break;
    proxy_pass ${DOLLAR}upstream_frontend_module_gis_threed;
    #proxy_set_header X-Forwarded-Proto https;    # 启用 HTTPS 时取消注释
  }
  location /app-gis/extensions {
    rewrite /app-gis/extensions/(.+) /last/extensions/${DOLLAR}1 break;
    proxy_pass ${DOLLAR}upstream_frontend_module_gis_threed;
    #proxy_set_header X-Forwarded-Proto https;    # 启用 HTTPS 时取消注释
  }
  location /app-gis/last {
    rewrite /app-gis/last/(.+) /last/${DOLLAR}1 break;
    proxy_pass ${DOLLAR}upstream_frontend_module_gis_threed;
    #proxy_set_header X-Forwarded-Proto https;    # 启用 HTTPS 时取消注释
  }

  # GIS 静态资源
  location /app-gis/images {
    rewrite /app-gis/(.+) /gis/css/${DOLLAR}1 break;
    proxy_pass ${DOLLAR}upstream_frontend_module_gis;
    #proxy_set_header X-Forwarded-Proto https;    # 启用 HTTPS 时取消注释
  }

  # 三维模型服务
  set ${DOLLAR}upstream_backend_three_model "${THREE_GIS_UR}";
  location /standard {
    client_max_body_size 100m;
    proxy_pass ${DOLLAR}upstream_backend_three_model;
    #proxy_set_header X-Forwarded-Proto https;    # 启用 HTTPS 时取消注释
  }

  #==========================================================================
  # 外部集成服务
  #==========================================================================

  # RabbitMQ 管理界面
  set ${DOLLAR}upstream_rabbitmq "http://rabbitmq:15672";
  location ^~ /rabbit/api/ {
    rewrite ^ ${DOLLAR}request_uri;
    rewrite ^/rabbit/api/(.*) /api/${DOLLAR}1 break;
    proxy_pass ${DOLLAR}upstream_rabbitmq${DOLLAR}uri;
    #proxy_set_header X-Forwarded-Proto https;    # 启用 HTTPS 时取消注释
  }
  location /rabbit/ {
    rewrite /rabbit/(.*) /${DOLLAR}1 break;
    proxy_pass ${DOLLAR}upstream_rabbitmq;
    #proxy_set_header X-Forwarded-Proto https;    # 启用 HTTPS 时取消注释
  }

  # Hasura GraphQL Engine
  set ${DOLLAR}upstream_hasura "http://graphql-engine:8080";
  location /hasura {
    rewrite /hasura/(.*) /${DOLLAR}1 break;
    proxy_pass ${DOLLAR}upstream_hasura;
    #proxy_set_header X-Forwarded-Proto https;    # 启用 HTTPS 时取消注释
    proxy_http_version 1.1;
    proxy_set_header Upgrade ${DOLLAR}http_upgrade;
    proxy_set_header Connection "upgrade";
  }
  location /preview-topo/hasura/ {
    rewrite /preview-topo/hasura/(.+) /${DOLLAR}1 break;
    proxy_pass ${DOLLAR}upstream_hasura;
    #proxy_set_header X-Forwarded-Proto https;    # 启用 HTTPS 时取消注释
    proxy_http_version 1.1;
    proxy_set_header Upgrade ${DOLLAR}http_upgrade;
    proxy_set_header Connection "upgrade";
  }

  # 帆软报表系统
  set ${DOLLAR}upstream_backend_finereport "${UPSTREAM_FINEREPORT}";
  location /webroot {
    proxy_pass ${DOLLAR}upstream_backend_finereport;
    #proxy_set_header X-Forwarded-Proto https;    # 启用 HTTPS 时取消注释
  }

  # 文件预览服务
  set ${DOLLAR}upstream_backend_file_preview "${UPSTREAM_FILE_PREVIEW}";
  location /preview {
    proxy_connect_timeout 60s;
    client_max_body_size 150m;
    proxy_pass ${DOLLAR}upstream_backend_file_preview;
    #proxy_set_header X-Forwarded-Proto https;    # 启用 HTTPS 时取消注释
  }

  # CMS 内容管理系统
  set ${DOLLAR}upstream_backend_cms "${UPSTREAM_CMS}";
  location /cms {
    proxy_pass ${DOLLAR}upstream_backend_cms;
    #proxy_set_header X-Forwarded-Proto https;    # 启用 HTTPS 时取消注释
  }

  #==========================================================================
  # 静态资源
  #==========================================================================

  # 默认静态资源
  location /static {
    root /usr/share/nginx/html;
  }

  # 采集归档文件
  location /archive/ {
    autoindex on;
    alias /usr/share/nginx/archive/;
  }

  #==========================================================================
  # 安全配置
  #==========================================================================

  # 禁用 Swagger 文档接口
  location ~* (?:\/swagger|\/druid) {
    return 404;
  }
  location ^~ /api/doc {
    return 404;
  }
  location ^~ /mes-biz/doc {
    return 404;
  }
  location ^~ /workflow/doc {
    return 404;
  }
}
