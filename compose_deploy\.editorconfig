# EditorConfig is awesome: https://EditorConfig.org

# top-most EditorConfig file
root = true

# 默认配置
[*]
indent_style = space
indent_size = 2
end_of_line = lf
charset = utf-8
trim_trailing_whitespace = true
insert_final_newline = true

# Shell 脚本文件配置
[*.{sh,bash}]
indent_style = space
indent_size = 2
end_of_line = lf
charset = utf-8
trim_trailing_whitespace = true
insert_final_newline = true

# Markdown 文件配置
[*.md]
indent_style = space
indent_size = 2
trim_trailing_whitespace = false

# YAML 文件配置
[*.{yml,yaml}]
indent_style = space
indent_size = 2

# JSON 文件配置
[*.json]
indent_style = space
indent_size = 2

# Docker 文件配置
[{Dockerfile,*.dockerfile}]
indent_style = space
indent_size = 2

# 配置文件
[*.{env,conf,config}]
indent_style = space
indent_size = 2
