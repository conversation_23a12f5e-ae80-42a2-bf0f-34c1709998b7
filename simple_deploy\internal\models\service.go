package models

import (
    "encoding/json"
    "time"
    "gorm.io/gorm"
)

// Service 服务定义模型
type Service struct {
    ID              uint      `json:"id" gorm:"primaryKey"`
    Name            string    `json:"name" gorm:"uniqueIndex;not null"`
    DisplayName     string    `json:"display_name"`
    Description     string    `json:"description"`
    Category        string    `json:"category"` // base, gis, video, mos, iot, mes, workflow
    Image           string    `json:"image" gorm:"not null"`
    Version         string    `json:"version" gorm:"default:latest"`
    PortsJSON       string    `json:"-" gorm:"column:ports"`
    VolumesJSON     string    `json:"-" gorm:"column:volumes"`
    EnvironmentJSON string    `json:"-" gorm:"column:environment_vars"`
    Command         string    `json:"command"`
    DependsOnJSON   string    `json:"-" gorm:"column:depends_on"`
    HealthCheckJSON string    `json:"-" gorm:"column:health_check"`
    RestartPolicy   string    `json:"restart_policy" gorm:"default:unless-stopped"`
    Enabled         bool      `json:"enabled" gorm:"default:true"`
    Status          string    `json:"status" gorm:"default:stopped"`
    ContainerID     string    `json:"container_id"`
    CreatedAt       time.Time `json:"created_at"`
    UpdatedAt       time.Time `json:"updated_at"`

    // 关联字段
    Groups          []ServiceGroup `json:"groups" gorm:"many2many:service_group_relations;"`
    Configurations  []Configuration `json:"configurations"`
    Deployments     []Deployment   `json:"deployments"`

    // 虚拟字段（不存储到数据库）
    Ports       []PortMapping     `json:"ports" gorm:"-"`
    Volumes     []VolumeMapping   `json:"volumes" gorm:"-"`
    Environment map[string]string `json:"environment" gorm:"-"`
    DependsOn   []uint           `json:"depends_on" gorm:"-"`
    HealthCheck *HealthCheck     `json:"health_check" gorm:"-"`
}

// PortMapping 端口映射
type PortMapping struct {
    HostPort      string `json:"host_port"`
    ContainerPort string `json:"container_port"`
    Protocol      string `json:"protocol" default:"tcp"`
}

// VolumeMapping 卷映射
type VolumeMapping struct {
    HostPath      string `json:"host_path"`
    ContainerPath string `json:"container_path"`
    Mode          string `json:"mode" default:"rw"`
}

// HealthCheck 健康检查配置
type HealthCheck struct {
    Test        []string      `json:"test"`
    Interval    time.Duration `json:"interval"`
    Timeout     time.Duration `json:"timeout"`
    Retries     int          `json:"retries"`
    StartPeriod time.Duration `json:"start_period"`
}

// ServiceGroup 服务分组
type ServiceGroup struct {
    ID          uint      `json:"id" gorm:"primaryKey"`
    Name        string    `json:"name" gorm:"uniqueIndex;not null"`
    DisplayName string    `json:"display_name"`
    Description string    `json:"description"`
    Enabled     bool      `json:"enabled" gorm:"default:true"`
    SortOrder   int       `json:"sort_order" gorm:"default:0"`
    CreatedAt   time.Time `json:"created_at"`

    Services []Service `json:"services" gorm:"many2many:service_group_relations;"`
}

// Configuration 配置文件
type Configuration struct {
    ID              uint      `json:"id" gorm:"primaryKey"`
    ServiceID       *uint     `json:"service_id"`
    ConfigType      string    `json:"config_type"` // env, volume, template
    Name            string    `json:"name" gorm:"not null"`
    FilePath        string    `json:"file_path"`
    Content         string    `json:"content"`
    TemplateContent string    `json:"template_content"`
    VariablesJSON   string    `json:"-" gorm:"column:variables"`
    IsTemplate      bool      `json:"is_template" gorm:"default:false"`
    CreatedAt       time.Time `json:"created_at"`
    UpdatedAt       time.Time `json:"updated_at"`

    Variables map[string]interface{} `json:"variables" gorm:"-"`
}

// Deployment 部署记录
type Deployment struct {
    ID           uint       `json:"id" gorm:"primaryKey"`
    ServiceID    uint       `json:"service_id"`
    Action       string     `json:"action"` // start, stop, restart, update
    Status       string     `json:"status"` // success, failed, running
    ContainerID  string     `json:"container_id"`
    Logs         string     `json:"logs"`
    ErrorMessage string     `json:"error_message"`
    StartedAt    time.Time  `json:"started_at"`
    CompletedAt  *time.Time `json:"completed_at"`

    Service Service `json:"service"`
}

// SystemSetting 系统配置
type SystemSetting struct {
    ID          uint      `json:"id" gorm:"primaryKey"`
    Category    string    `json:"category"`
    Key         string    `json:"key" gorm:"uniqueIndex;not null"`
    Value       string    `json:"value"`
    Description string    `json:"description"`
    DataType    string    `json:"data_type" gorm:"default:string"`
    CreatedAt   time.Time `json:"created_at"`
    UpdatedAt   time.Time `json:"updated_at"`
}

// GORM Hooks - 在保存前序列化JSON字段
func (s *Service) BeforeSave(tx *gorm.DB) error {
    if s.Ports != nil {
        if data, err := json.Marshal(s.Ports); err == nil {
            s.PortsJSON = string(data)
        }
    }
    if s.Volumes != nil {
        if data, err := json.Marshal(s.Volumes); err == nil {
            s.VolumesJSON = string(data)
        }
    }
    if s.Environment != nil {
        if data, err := json.Marshal(s.Environment); err == nil {
            s.EnvironmentJSON = string(data)
        }
    }
    if s.DependsOn != nil {
        if data, err := json.Marshal(s.DependsOn); err == nil {
            s.DependsOnJSON = string(data)
        }
    }
    if s.HealthCheck != nil {
        if data, err := json.Marshal(s.HealthCheck); err == nil {
            s.HealthCheckJSON = string(data)
        }
    }
    return nil
}

// GORM Hooks - 在加载后反序列化JSON字段
func (s *Service) AfterFind(tx *gorm.DB) error {
    if s.PortsJSON != "" {
        json.Unmarshal([]byte(s.PortsJSON), &s.Ports)
    }
    if s.VolumesJSON != "" {
        json.Unmarshal([]byte(s.VolumesJSON), &s.Volumes)
    }
    if s.EnvironmentJSON != "" {
        json.Unmarshal([]byte(s.EnvironmentJSON), &s.Environment)
    }
    if s.DependsOnJSON != "" {
        json.Unmarshal([]byte(s.DependsOnJSON), &s.DependsOn)
    }
    if s.HealthCheckJSON != "" {
        json.Unmarshal([]byte(s.HealthCheckJSON), &s.HealthCheck)
    }
    return nil
}

func (c *Configuration) BeforeSave(tx *gorm.DB) error {
    if c.Variables != nil {
        if data, err := json.Marshal(c.Variables); err == nil {
            c.VariablesJSON = string(data)
        }
    }
    return nil
}

func (c *Configuration) AfterFind(tx *gorm.DB) error {
    if c.VariablesJSON != "" {
        json.Unmarshal([]byte(c.VariablesJSON), &c.Variables)
    }
    return nil
}
