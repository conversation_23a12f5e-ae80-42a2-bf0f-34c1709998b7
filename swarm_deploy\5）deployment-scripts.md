# 5）Docker Swarm 部署脚本和管理工具

## 🚀 主部署脚本设计

### swarm_deploy.sh - 主部署脚本

```bash
#!/bin/bash

#===============================================================================
# 综合信息平台 - Docker Swarm 主部署脚本
#===============================================================================
# 功能描述: Docker Swarm 模式的主要部署和管理脚本
# 支持功能: 集群初始化、服务部署、栈管理、健康检查等
# 创建时间: 2024年
#
# 使用方法: ./swarm_deploy.sh [COMMAND] [OPTIONS]
# 详细帮助: ./swarm_deploy.sh --help
#===============================================================================

set -e

# 脚本名称和路径
PROGNAME=$0
DEPLOY_PATH=$(dirname $(readlink -f "$0"))

# 颜色输出函数
echo_red() { echo -e "\033[31m$1\033[0m"; }
echo_green() { echo -e "\033[32m$1\033[0m"; }
echo_yellow() { echo -e "\033[33m$1\033[0m"; }
echo_blue() { echo -e "\033[34m$1\033[0m"; }

# 日志函数
log_info() { echo_blue "[INFO] $(date '+%Y-%m-%d %H:%M:%S') $1"; }
log_warn() { echo_yellow "[WARN] $(date '+%Y-%m-%d %H:%M:%S') $1"; }
log_error() { echo_red "[ERROR] $(date '+%Y-%m-%d %H:%M:%S') $1"; }
log_success() { echo_green "[SUCCESS] $(date '+%Y-%m-%d %H:%M:%S') $1"; }

#===============================================================================
# 配置加载
#===============================================================================

# 加载环境配置
load_config() {
    local env_file="${1:-env/production.env}"
    
    if [ ! -f "$env_file" ]; then
        log_error "配置文件不存在: $env_file"
        exit 1
    fi
    
    log_info "加载配置文件: $env_file"
    source "$env_file"
    
    # 验证必需的环境变量
    local required_vars=(
        "POSTGRES_USERNAME" "POSTGRES_PASSWORD" "POSTGRES_DATABASE"
        "REDIS_PASSWORD" "RABBITMQ_USERNAME" "RABBITMQ_PASSWORD"
        "MINIO_ACCESS_KEY" "MINIO_SECRET_KEY"
    )
    
    for var in "${required_vars[@]}"; do
        if [ -z "${!var}" ]; then
            log_error "必需的环境变量未设置: $var"
            exit 1
        fi
    done
    
    log_success "配置加载完成"
}

#===============================================================================
# Swarm 集群管理
#===============================================================================

# 初始化 Swarm 集群
init_swarm() {
    log_info "初始化 Docker Swarm 集群..."
    
    # 检查是否已经是 Swarm 节点
    if docker info --format '{{.Swarm.LocalNodeState}}' | grep -q active; then
        log_warn "当前节点已经是 Swarm 集群的一部分"
        return 0
    fi
    
    # 获取本机 IP 地址
    local advertise_addr=$(ip route get ******* | awk '{print $7; exit}')
    
    # 初始化 Swarm
    docker swarm init --advertise-addr "$advertise_addr"
    
    log_success "Swarm 集群初始化完成"
    log_info "Manager 节点地址: $advertise_addr"
    
    # 显示加入命令
    echo_yellow "Worker 节点加入命令:"
    docker swarm join-token worker
    
    echo_yellow "Manager 节点加入命令:"
    docker swarm join-token manager
}

# 检查 Swarm 状态
check_swarm() {
    log_info "检查 Swarm 集群状态..."
    
    if ! docker info --format '{{.Swarm.LocalNodeState}}' | grep -q active; then
        log_error "当前节点不是 Swarm 集群的一部分"
        log_info "请先运行: $PROGNAME init"
        exit 1
    fi
    
    # 显示集群信息
    echo_blue "=== Swarm 集群信息 ==="
    docker node ls
    echo
    
    echo_blue "=== 网络列表 ==="
    docker network ls --filter driver=overlay
    echo
    
    echo_blue "=== 服务列表 ==="
    docker service ls
    echo
    
    log_success "Swarm 集群状态检查完成"
}

#===============================================================================
# 网络和存储初始化
#===============================================================================

# 创建网络
create_networks() {
    log_info "创建 Docker 网络..."
    
    # 主业务网络
    if ! docker network ls | grep -q middle; then
        docker network create \
            --driver overlay \
            --opt encrypted=true \
            --attachable \
            --subnet ********/24 \
            --gateway ******** \
            --label "network.description=主业务网络" \
            --label "network.environment=production" \
            middle
        log_success "创建网络: middle"
    else
        log_warn "网络已存在: middle"
    fi
    
    # 管理网络
    if ! docker network ls | grep -q admin-net; then
        docker network create \
            --driver overlay \
            --opt encrypted=true \
            --subnet ********/24 \
            --gateway ******** \
            --label "network.description=管理网络" \
            --label "network.access=admin-only" \
            admin-net
        log_success "创建网络: admin-net"
    else
        log_warn "网络已存在: admin-net"
    fi
    
    # 监控网络
    if ! docker network ls | grep -q monitoring-net; then
        docker network create \
            --driver overlay \
            --opt encrypted=true \
            --attachable \
            --subnet ********/24 \
            --gateway ******** \
            --label "network.description=监控网络" \
            --label "network.purpose=metrics-collection" \
            monitoring-net
        log_success "创建网络: monitoring-net"
    else
        log_warn "网络已存在: monitoring-net"
    fi
}

# 创建存储卷
create_volumes() {
    log_info "创建 Docker 存储卷..."
    
    # 存储卷列表
    local volumes=(
        "base-postgres:数据库主数据"
        "base-business-postgres:数据库业务数据"
        "base-redis:Redis缓存数据"
        "base-rabbitmq:RabbitMQ消息数据"
        "base-tdengine-data:TDengine时序数据"
        "base-tdengine-log:TDengine日志数据"
        "bladex-minio:MinIO对象存储"
        "base-ftp:FTP文件存储"
        "monitoring-prometheus-data:Prometheus监控数据"
        "monitoring-grafana-data:Grafana仪表板数据"
        "monitoring-portainer-data:Portainer管理数据"
    )
    
    for volume_info in "${volumes[@]}"; do
        local volume_name="${volume_info%:*}"
        local volume_desc="${volume_info#*:}"
        
        if ! docker volume ls | grep -q "$volume_name"; then
            docker volume create \
                --label "volume.description=$volume_desc" \
                --label "volume.environment=production" \
                "$volume_name"
            log_success "创建存储卷: $volume_name"
        else
            log_warn "存储卷已存在: $volume_name"
        fi
    done
}

#===============================================================================
# 密钥和配置管理
#===============================================================================

# 创建密钥
create_secrets() {
    log_info "创建 Docker Secrets..."
    
    # 检查密钥配置文件
    local secrets_file="secrets/secrets.env"
    if [ ! -f "$secrets_file" ]; then
        log_error "密钥配置文件不存在: $secrets_file"
        log_info "请先创建密钥配置文件"
        return 1
    fi
    
    source "$secrets_file"
    
    # 密钥列表
    local secrets=(
        "postgres_password:$POSTGRES_PASSWORD"
        "business_postgres_password:$BUSINESS_POSTGRES_PASSWORD"
        "redis_password:$REDIS_PASSWORD"
        "rabbitmq_user:$RABBITMQ_USER"
        "rabbitmq_password:$RABBITMQ_PASSWORD"
        "minio_access_key:$MINIO_ACCESS_KEY"
        "minio_secret_key:$MINIO_SECRET_KEY"
        "jwt_secret:$JWT_SECRET"
        "auth_secret:$AUTH_SECRET"
        "nacos_password:$NACOS_PASSWORD"
        "grafana_admin_password:$GRAFANA_ADMIN_PASSWORD"
        "ssh_password:$SSH_PASSWORD"
        "portainer_password:$PORTAINER_PASSWORD"
    )
    
    for secret_info in "${secrets[@]}"; do
        local secret_name="${secret_info%:*}"
        local secret_value="${secret_info#*:}"
        
        if ! docker secret ls | grep -q "$secret_name"; then
            echo "$secret_value" | docker secret create "$secret_name" -
            log_success "创建密钥: $secret_name"
        else
            log_warn "密钥已存在: $secret_name"
        fi
    done
}

# 创建配置
create_configs() {
    log_info "创建 Docker Configs..."
    
    # 配置文件列表
    local configs=(
        "nginx_main_config:configs/nginx/nginx.conf"
        "nginx_default_config:configs/nginx/nginx-default.conf"
        "nginx_upstream_config:configs/nginx/upstream.conf"
        "prometheus_config:configs/prometheus/prometheus.yml"
        "prometheus_rules:configs/prometheus/alert.rules.yml"
        "grafana_config:configs/grafana/grafana.ini"
        "alertmanager_config:configs/prometheus/alertmanager.yml"
        "redis_config:configs/redis/redis.template"
        "rabbitmq_config:configs/rabbitmq/rabbitmq.conf"
        "tdengine_config:configs/tdengine/taos.cfg"
    )
    
    for config_info in "${configs[@]}"; do
        local config_name="${config_info%:*}"
        local config_file="${config_info#*:}"
        
        if [ ! -f "$config_file" ]; then
            log_warn "配置文件不存在: $config_file"
            continue
        fi
        
        if ! docker config ls | grep -q "$config_name"; then
            docker config create "$config_name" "$config_file"
            log_success "创建配置: $config_name"
        else
            log_warn "配置已存在: $config_name"
        fi
    done
}

#===============================================================================
# 栈部署管理
#===============================================================================

# 部署栈
deploy_stack() {
    local stack_name=$1
    local stack_file="stacks/${stack_name}.yml"
    
    if [ ! -f "$stack_file" ]; then
        log_error "栈文件不存在: $stack_file"
        return 1
    fi
    
    log_info "部署栈: $stack_name"
    
    # 验证栈文件语法
    if ! docker-compose -f "$stack_file" config > /dev/null 2>&1; then
        log_error "栈文件语法错误: $stack_file"
        return 1
    fi
    
    # 部署栈
    docker stack deploy -c "$stack_file" "$stack_name"
    
    log_success "栈部署完成: $stack_name"
}

# 部署所有栈
deploy_all_stacks() {
    log_info "开始部署所有服务栈..."
    
    # 部署顺序（按依赖关系）
    local stacks=(
        "infrastructure"
        "core"
        "iot"
        "monitoring"
        "auxiliary"
    )
    
    for stack in "${stacks[@]}"; do
        deploy_stack "$stack"
        
        # 等待栈启动
        log_info "等待栈启动: $stack"
        sleep 30
        
        # 检查栈状态
        if ! check_stack_health "$stack"; then
            log_error "栈启动失败: $stack"
            return 1
        fi
    done
    
    log_success "所有服务栈部署完成！"
}

# 检查栈健康状态
check_stack_health() {
    local stack_name=$1
    local max_attempts=10
    local attempt=1
    
    log_info "检查栈健康状态: $stack_name"
    
    while [ $attempt -le $max_attempts ]; do
        local running_services=$(docker stack services "$stack_name" --format "{{.Replicas}}" | grep -c "1/1" || true)
        local total_services=$(docker stack services "$stack_name" --quiet | wc -l)
        
        if [ "$running_services" -eq "$total_services" ] && [ "$total_services" -gt 0 ]; then
            log_success "栈健康检查通过: $stack_name ($running_services/$total_services)"
            return 0
        fi
        
        log_info "等待服务启动... ($running_services/$total_services) 尝试 $attempt/$max_attempts"
        sleep 30
        ((attempt++))
    done
    
    log_error "栈健康检查失败: $stack_name"
    docker stack services "$stack_name"
    return 1
}

#===============================================================================
# 服务管理
#===============================================================================

# 启动服务
start_services() {
    log_info "启动所有服务..."
    
    # 检查栈是否存在
    local stacks=("infrastructure" "core" "iot" "monitoring" "auxiliary")
    
    for stack in "${stacks[@]}"; do
        if docker stack ls | grep -q "$stack"; then
            log_info "更新栈: $stack"
            deploy_stack "$stack"
        else
            log_warn "栈不存在: $stack"
        fi
    done
    
    log_success "服务启动完成"
}

# 停止服务
stop_services() {
    log_info "停止所有服务..."
    
    # 获取所有栈
    local stacks=$(docker stack ls --format "{{.Name}}")
    
    for stack in $stacks; do
        log_info "移除栈: $stack"
        docker stack rm "$stack"
    done
    
    # 等待服务完全停止
    log_info "等待服务完全停止..."
    sleep 60
    
    log_success "所有服务已停止"
}

# 重启服务
restart_services() {
    log_info "重启所有服务..."
    
    stop_services
    sleep 30
    start_services
    
    log_success "服务重启完成"
}

#===============================================================================
# 监控和日志
#===============================================================================

# 查看服务状态
show_status() {
    echo_blue "=== Docker Swarm 集群状态 ==="
    docker node ls
    echo
    
    echo_blue "=== 服务栈列表 ==="
    docker stack ls
    echo
    
    echo_blue "=== 服务详情 ==="
    docker service ls
    echo
    
    echo_blue "=== 网络列表 ==="
    docker network ls --filter driver=overlay
    echo
    
    echo_blue "=== 存储卷列表 ==="
    docker volume ls --filter label=volume.environment=production
    echo
}

# 查看服务日志
show_logs() {
    local service_name=$1
    local lines=${2:-100}
    
    if [ -z "$service_name" ]; then
        log_error "请指定服务名称"
        echo "用法: $PROGNAME logs <service_name> [lines]"
        return 1
    fi
    
    log_info "查看服务日志: $service_name (最近 $lines 行)"
    docker service logs --tail "$lines" --follow "$service_name"
}

#===============================================================================
# 备份和恢复
#===============================================================================

# 数据备份
backup_data() {
    log_info "开始数据备份..."
    
    local backup_script="scripts/backup.sh"
    if [ -f "$backup_script" ]; then
        bash "$backup_script"
    else
        log_error "备份脚本不存在: $backup_script"
        return 1
    fi
    
    log_success "数据备份完成"
}

# 数据恢复
restore_data() {
    local backup_date=$1
    
    if [ -z "$backup_date" ]; then
        log_error "请指定备份日期"
        echo "用法: $PROGNAME restore <backup_date>"
        return 1
    fi
    
    log_info "开始数据恢复: $backup_date"
    
    local restore_script="scripts/restore.sh"
    if [ -f "$restore_script" ]; then
        bash "$restore_script" "$backup_date"
    else
        log_error "恢复脚本不存在: $restore_script"
        return 1
    fi
    
    log_success "数据恢复完成"
}

#===============================================================================
# 帮助信息
#===============================================================================

show_help() {
    cat << EOF
===============================================================================
综合信息平台 - Docker Swarm 部署脚本使用说明
===============================================================================

使用方法:
  $PROGNAME COMMAND [OPTIONS]

主要命令:
  init                          初始化 Docker Swarm 集群
  deploy                        部署所有服务栈
  start                         启动所有服务
  stop                          停止所有服务
  restart                       重启所有服务
  status                        查看集群和服务状态
  
栈管理:
  deploy-stack <stack_name>     部署指定栈
  remove-stack <stack_name>     移除指定栈
  list-stacks                   列出所有栈
  
服务管理:
  logs <service_name> [lines]   查看服务日志
  scale <service_name> <count>  扩展服务副本数
  update <service_name>         更新服务
  
集群管理:
  check                         检查 Swarm 集群状态
  join-token [worker|manager]   获取节点加入令牌
  node-ls                       列出集群节点
  
数据管理:
  backup                        备份数据
  restore <backup_date>         恢复数据
  
初始化:
  init-networks                 创建网络
  init-volumes                  创建存储卷
  init-secrets                  创建密钥
  init-configs                  创建配置
  
使用示例:
  # 完整部署流程
  $PROGNAME init                # 初始化集群
  $PROGNAME init-networks       # 创建网络
  $PROGNAME init-volumes        # 创建存储卷
  $PROGNAME init-secrets        # 创建密钥
  $PROGNAME init-configs        # 创建配置
  $PROGNAME deploy              # 部署所有服务
  
  # 日常管理
  $PROGNAME status              # 查看状态
  $PROGNAME logs core_auth-service 50  # 查看日志
  $PROGNAME backup              # 备份数据
  
注意事项:
  - 首次部署前请确保配置文件已正确设置
  - 备份操作会暂时影响服务性能
  - 恢复操作会覆盖现有数据，请谨慎操作
  - 所有操作需要在 Swarm Manager 节点上执行

===============================================================================
EOF
}

#===============================================================================
# 主程序入口
#===============================================================================

# 检查参数
if [ $# -eq 0 ]; then
    show_help
    exit 1
fi

# 加载配置
load_config

# 命令处理
case $1 in
    "init")
        init_swarm
        ;;
    "deploy")
        create_networks
        create_volumes
        create_secrets
        create_configs
        deploy_all_stacks
        ;;
    "start")
        start_services
        ;;
    "stop")
        stop_services
        ;;
    "restart")
        restart_services
        ;;
    "status")
        show_status
        ;;
    "check")
        check_swarm
        ;;
    "deploy-stack")
        if [ -z "$2" ]; then
            log_error "请指定栈名称"
            exit 1
        fi
        deploy_stack "$2"
        ;;
    "logs")
        show_logs "$2" "$3"
        ;;
    "backup")
        backup_data
        ;;
    "restore")
        restore_data "$2"
        ;;
    "init-networks")
        create_networks
        ;;
    "init-volumes")
        create_volumes
        ;;
    "init-secrets")
        create_secrets
        ;;
    "init-configs")
        create_configs
        ;;
    "--help"|"help")
        show_help
        ;;
    *)
        log_error "未知命令: $1"
        show_help
        exit 1
        ;;
esac

log_success "操作完成！"
```

## 🛠️ 辅助管理工具

### stack_manager.sh - 栈管理工具

```bash
#!/bin/bash

# 栈管理工具
# 提供更细粒度的栈管理功能

set -e

# 颜色输出
echo_info() { echo -e "\033[34m[INFO]\033[0m $1"; }
echo_warn() { echo -e "\033[33m[WARN]\033[0m $1"; }
echo_error() { echo -e "\033[31m[ERROR]\033[0m $1"; }
echo_success() { echo -e "\033[32m[SUCCESS]\033[0m $1"; }

# 列出所有栈
list_stacks() {
    echo_info "当前部署的服务栈:"
    docker stack ls --format "table {{.Name}}\t{{.Services}}\t{{.Orchestrator}}"
}

# 查看栈详情
show_stack_details() {
    local stack_name=$1
    
    if [ -z "$stack_name" ]; then
        echo_error "请指定栈名称"
        return 1
    fi
    
    echo_info "栈详情: $stack_name"
    echo
    
    echo "=== 服务列表 ==="
    docker stack services "$stack_name"
    echo
    
    echo "=== 任务列表 ==="
    docker stack ps "$stack_name" --no-trunc
}

# 扩展服务
scale_service() {
    local service_name=$1
    local replica_count=$2
    
    if [ -z "$service_name" ] || [ -z "$replica_count" ]; then
        echo_error "用法: scale_service <service_name> <replica_count>"
        return 1
    fi
    
    echo_info "扩展服务: $service_name 到 $replica_count 个副本"
    docker service scale "$service_name=$replica_count"
    
    echo_success "服务扩展完成"
}

# 更新服务镜像
update_service_image() {
    local service_name=$1
    local new_image=$2
    
    if [ -z "$service_name" ] || [ -z "$new_image" ]; then
        echo_error "用法: update_service_image <service_name> <new_image>"
        return 1
    fi
    
    echo_info "更新服务镜像: $service_name -> $new_image"
    docker service update --image "$new_image" "$service_name"
    
    echo_success "服务镜像更新完成"
}

# 回滚服务
rollback_service() {
    local service_name=$1
    
    if [ -z "$service_name" ]; then
        echo_error "请指定服务名称"
        return 1
    fi
    
    echo_info "回滚服务: $service_name"
    docker service rollback "$service_name"
    
    echo_success "服务回滚完成"
}

# 主程序
case $1 in
    "list")
        list_stacks
        ;;
    "details")
        show_stack_details "$2"
        ;;
    "scale")
        scale_service "$2" "$3"
        ;;
    "update-image")
        update_service_image "$2" "$3"
        ;;
    "rollback")
        rollback_service "$2"
        ;;
    *)
        echo "用法: $0 {list|details|scale|update-image|rollback}"
        echo
        echo "命令说明:"
        echo "  list                          - 列出所有栈"
        echo "  details <stack_name>          - 查看栈详情"
        echo "  scale <service> <count>       - 扩展服务副本"
        echo "  update-image <service> <image> - 更新服务镜像"
        echo "  rollback <service>            - 回滚服务"
        exit 1
        ;;
esac
```

### health_check.sh - 健康检查工具

```bash
#!/bin/bash

# 健康检查工具
# 检查所有服务的健康状态

set -e

# 颜色输出
echo_info() { echo -e "\033[34m[INFO]\033[0m $1"; }
echo_warn() { echo -e "\033[33m[WARN]\033[0m $1"; }
echo_error() { echo -e "\033[31m[ERROR]\033[0m $1"; }
echo_success() { echo -e "\033[32m[SUCCESS]\033[0m $1"; }

# 检查 Swarm 集群状态
check_swarm_cluster() {
    echo_info "检查 Swarm 集群状态..."
    
    if ! docker info --format '{{.Swarm.LocalNodeState}}' | grep -q active; then
        echo_error "当前节点不是 Swarm 集群的一部分"
        return 1
    fi
    
    local manager_count=$(docker node ls --filter role=manager --quiet | wc -l)
    local worker_count=$(docker node ls --filter role=worker --quiet | wc -l)
    local total_nodes=$((manager_count + worker_count))
    
    echo_success "集群状态正常 - Manager: $manager_count, Worker: $worker_count, 总计: $total_nodes"
    
    # 检查节点状态
    local down_nodes=$(docker node ls --filter availability=drain --quiet | wc -l)
    if [ "$down_nodes" -gt 0 ]; then
        echo_warn "发现 $down_nodes 个不可用节点"
        docker node ls --filter availability=drain
    fi
}

# 检查服务健康状态
check_services_health() {
    echo_info "检查服务健康状态..."
    
    local services=$(docker service ls --quiet)
    local healthy_count=0
    local unhealthy_count=0
    
    for service_id in $services; do
        local service_name=$(docker service inspect "$service_id" --format '{{.Spec.Name}}')
        local replicas=$(docker service ls --filter id="$service_id" --format '{{.Replicas}}')
        
        if echo "$replicas" | grep -q "/"; then
            local current=$(echo "$replicas" | cut -d'/' -f1)
            local desired=$(echo "$replicas" | cut -d'/' -f2)
            
            if [ "$current" -eq "$desired" ]; then
                echo_success "✓ $service_name ($replicas)"
                ((healthy_count++))
            else
                echo_error "✗ $service_name ($replicas)"
                ((unhealthy_count++))
                
                # 显示失败的任务
                docker service ps "$service_id" --filter desired-state=running --format "table {{.Name}}\t{{.CurrentState}}\t{{.Error}}" | grep -v "Running"
            fi
        fi
    done
    
    echo
    echo_info "健康检查汇总: 健康 $healthy_count, 异常 $unhealthy_count"
    
    if [ "$unhealthy_count" -gt 0 ]; then
        return 1
    fi
}

# 检查网络连通性
check_network_connectivity() {
    echo_info "检查网络连通性..."
    
    # 检查 overlay 网络
    local networks=$(docker network ls --filter driver=overlay --format '{{.Name}}')
    
    for network in $networks; do
        local containers=$(docker network inspect "$network" --format '{{range .Containers}}{{.Name}} {{end}}')
        if [ -n "$containers" ]; then
            echo_success "✓ 网络 $network 有 $(echo $containers | wc -w) 个容器连接"
        else
            echo_warn "⚠ 网络 $network 没有容器连接"
        fi
    done
}

# 检查存储卷使用情况
check_volume_usage() {
    echo_info "检查存储卷使用情况..."
    
    # 检查 Docker 卷
    local volumes=$(docker volume ls --filter label=volume.environment=production --quiet)
    
    for volume in $volumes; do
        local volume_info=$(docker volume inspect "$volume" --format '{{.Name}}: {{index .Labels "volume.description"}}')
        echo_success "
