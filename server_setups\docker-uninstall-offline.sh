#!/bin/bash

# shellcheck source=_common.sh
# shellcheck disable=SC1091

#===============================================================================
# Docker 离线卸载脚本
#===============================================================================
# 功能描述: 完全卸载 Docker CE 和 Docker Compose
# 适用系统: CentOS 7.x / RHEL 7.x
# 执行权限: 需要 root 权限
# 依赖文件:
#   - _common.sh (共通函数库)
# 注意事项:
#   - 此脚本将完全移除 Docker 及其所有数据
#   - 请在执行前备份重要的容器数据
#   - 卸载过程不可逆，请谨慎操作
#===============================================================================

set -e  # 遇到错误立即退出

# 加载共通函数库
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
if [[ -f "$SCRIPT_DIR/_common.sh" ]]; then
  # shellcheck source=./_common.sh
  source "$SCRIPT_DIR/_common.sh"
else
  echo "错误: 找不到共通函数库 _common.sh"
  exit 1
fi

# 显示警告信息并确认
show_warning_and_confirm() {
  show_separator "=" 70
  log_warning "此脚本将完全卸载 Docker 和 Docker Compose！"
  log_warning "以下内容将被删除："
  echo "  - 所有 Docker 容器和镜像"
  echo "  - Docker 二进制文件"
  echo "  - Docker 配置文件"
  echo "  - Docker 数据目录 (/data/docker)"
  echo "  - systemd 服务文件"
  echo "  - Docker Compose 二进制文件"
  echo ""
  log_warning "此操作不可逆！请确保已备份重要数据！"
  show_separator "=" 70
  echo ""

  # 如果是强制模式，跳过确认
  if [[ "$FORCE_MODE" == true ]]; then
    log_info "强制模式，跳过确认..."
    return 0
  fi

  # 如果是非交互模式，检查环境变量
  if [[ -n "$DOCKER_UNINSTALL_CONFIRM" && "$DOCKER_UNINSTALL_CONFIRM" == "yes" ]]; then
    log_info "检测到确认环境变量，继续执行卸载..."
    return 0
  fi

  # 交互式确认
  if ! confirm_operation "您确定要继续吗？请输入 'yes' 确认"; then
    log_info "用户取消操作，退出脚本"
    exit 0
  fi

  # 二次确认
  echo ""
  log_warning "最后确认：这将删除所有 Docker 数据！"
  read -p "请再次输入 'DELETE' 确认删除所有数据: " final_confirm
  if [[ "$final_confirm" != "DELETE" ]]; then
    log_info "用户取消操作，退出脚本"
    exit 0
  fi
}

# 检查 Docker 是否已安装
check_docker_installation() {
  log_info "检查 Docker 安装状态..."

  local docker_installed=false
  local compose_installed=false

  # 检查 Docker
  if is_docker_installed; then
    docker_installed=true
    local docker_version=$(get_docker_version)
    log_info "检测到 Docker: $docker_version"
  fi

  # 检查 Docker Compose
  if is_docker_compose_installed; then
    compose_installed=true
    local compose_version=$(get_docker_compose_version)
    log_info "检测到 Docker Compose: $compose_version"
  fi

  if [[ "$docker_installed" == false && "$compose_installed" == false ]]; then
    log_warning "未检测到 Docker 或 Docker Compose 安装"
    if [[ "$FORCE_MODE" != true ]]; then
      if ! confirm_operation "是否继续清理可能残留的文件？"; then
        log_info "用户选择不清理，退出脚本"
        exit 0
      fi
    fi
  fi
}

# 停止所有 Docker 容器
stop_all_containers() {
  log_info "停止所有 Docker 容器..."

  if is_docker_installed && service_is_active docker; then
    # 获取所有运行中的容器
    local running_containers=$(docker ps -q 2>/dev/null || true)
    if [[ -n "$running_containers" ]]; then
      log_info "停止运行中的容器..."
      docker stop $running_containers 2>/dev/null || true
      log_success "已停止所有运行中的容器"
    else
      log_info "没有运行中的容器"
    fi

    # 删除所有容器
    local all_containers=$(docker ps -aq 2>/dev/null || true)
    if [[ -n "$all_containers" ]]; then
      log_info "删除所有容器..."
      docker rm -f $all_containers 2>/dev/null || true
      log_success "已删除所有容器"
    else
      log_info "没有容器需要删除"
    fi

    # 删除所有镜像
    local all_images=$(docker images -q 2>/dev/null || true)
    if [[ -n "$all_images" ]]; then
      log_info "删除所有镜像..."
      docker rmi -f $all_images 2>/dev/null || true
      log_success "已删除所有镜像"
    else
      log_info "没有镜像需要删除"
    fi

    # 清理系统
    log_info "清理 Docker 系统..."
    docker system prune -af --volumes 2>/dev/null || true
    log_success "Docker 系统清理完成"
  else
    log_info "Docker 服务未运行或不可用，跳过容器清理"
  fi
}

# 停止并禁用 Docker 服务
stop_and_disable_services() {
  log_info "停止并禁用 Docker 服务..."

  # 停止 Docker 相关服务
  safe_stop_service docker
  safe_stop_service containerd
  safe_stop_service docker.socket

  # 禁用服务
  safe_disable_service docker
  safe_disable_service containerd
  safe_disable_service docker.socket
}

# 移除通过包管理器安装的 Docker
remove_package_docker() {
  log_info "检查并移除通过包管理器安装的 Docker..."

  # 检查是否有通过 yum/rpm 安装的 Docker 包
  local docker_packages=$(rpm -qa | grep -E "docker|containerd" | grep -v grep || true)

  if [[ -n "$docker_packages" ]]; then
    log_info "发现通过包管理器安装的 Docker 包:"
    echo "$docker_packages"
    log_info "移除这些包..."
    yum remove -y docker docker-common docker-selinux docker-engine docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin 2>/dev/null || true
    log_success "已移除通过包管理器安装的 Docker 包"
  else
    log_info "未发现通过包管理器安装的 Docker 包"
  fi
}

# 删除 Docker 二进制文件
remove_docker_binaries() {
  log_info "删除 Docker 二进制文件..."

  # Docker 相关二进制文件列表
  local binaries=($(get_docker_binaries) docker-compose)

  # 删除 /usr/local/bin 中的二进制文件
  log_info "删除 /usr/local/bin 中的 Docker 二进制文件..."
  for binary in "${binaries[@]}"; do
    safe_remove_file "/usr/local/bin/$binary"
  done

  # 删除 /usr/bin 中的软链接和文件
  log_info "删除 /usr/bin 中的 Docker 软链接和文件..."
  for binary in "${binaries[@]}"; do
    safe_remove_file "/usr/bin/$binary"
  done

  log_success "Docker 二进制文件删除完成"
}

# 删除 systemd 服务文件
remove_systemd_services() {
  log_info "删除 systemd 服务文件..."

  local service_files=(
    "/etc/systemd/system/docker.service"
    "/etc/systemd/system/containerd.service"
    "/etc/systemd/system/docker.socket"
    "/etc/systemd/system/docker.target"
  )

  for service_file in "${service_files[@]}"; do
    safe_remove_file "$service_file"
  done

  # 重新加载 systemd
  reload_systemd
  log_success "systemd 服务文件删除完成"
}

# 删除 Docker 配置文件和数据
remove_docker_data() {
  log_info "删除 Docker 配置文件和数据..."

  # 删除配置目录
  safe_remove_dir "/etc/docker"

  # 删除数据目录
  if dir_exists "/data/docker"; then
    log_warning "删除 Docker 数据目录: /data/docker"
    log_warning "这将删除所有容器数据、镜像和卷！"
    safe_remove_dir "/data/docker"
    log_success "Docker 数据目录已删除"
  fi

  # 删除默认数据目录（如果存在）
  if dir_exists "/var/lib/docker"; then
    log_warning "删除默认 Docker 数据目录: /var/lib/docker"
    safe_remove_dir "/var/lib/docker"
    log_success "默认 Docker 数据目录已删除"
  fi

  # 删除 containerd 数据目录
  safe_remove_dir "/var/lib/containerd"

  # 删除运行时目录
  safe_remove_dir "/var/run/docker"
  safe_remove_dir "/run/docker"
  safe_remove_dir "/run/containerd"

  log_success "Docker 配置文件和数据删除完成"
}

# 清理网络配置
cleanup_network() {
  log_info "清理 Docker 网络配置..."

  # 删除 Docker 创建的网络接口
  local docker_interfaces=$(ip link show | grep docker | awk -F: '{print $2}' | tr -d ' ' || true)
  if [[ -n "$docker_interfaces" ]]; then
    for interface in $docker_interfaces; do
      log_info "删除网络接口: $interface"
      ip link delete "$interface" 2>/dev/null || true
    done
  fi

  # 清理 iptables 规则（Docker 相关）
  log_info "清理 Docker 相关的 iptables 规则..."
  iptables -t nat -F DOCKER 2>/dev/null || true
  iptables -t filter -F DOCKER 2>/dev/null || true
  iptables -t filter -F DOCKER-ISOLATION-STAGE-1 2>/dev/null || true
  iptables -t filter -F DOCKER-ISOLATION-STAGE-2 2>/dev/null || true
  iptables -t nat -X DOCKER 2>/dev/null || true
  iptables -t filter -X DOCKER 2>/dev/null || true
  iptables -t filter -X DOCKER-ISOLATION-STAGE-1 2>/dev/null || true
  iptables -t filter -X DOCKER-ISOLATION-STAGE-2 2>/dev/null || true

  log_success "网络配置清理完成"
}

# 清理用户组
cleanup_user_groups() {
  log_info "清理 Docker 用户组..."

  # 检查 docker 组是否存在
  if getent group docker >/dev/null 2>&1; then
    log_info "删除 docker 用户组..."
    groupdel docker 2>/dev/null || true
    log_success "docker 用户组已删除"
  else
    log_info "docker 用户组不存在"
  fi
}

# 清理临时文件和缓存
cleanup_temp_files() {
  log_info "清理临时文件和缓存..."

  # 清理可能的临时文件
  rm -rf /tmp/docker* 2>/dev/null || true
  rm -rf /tmp/containerd* 2>/dev/null || true

  # 清理日志文件
  safe_remove_dir "/var/log/docker"

  log_success "临时文件和缓存清理完成"
}

# 验证卸载
verify_uninstallation() {
  log_info "验证卸载结果..."

  local uninstall_success=true

  # 检查命令是否还存在
  if is_docker_installed; then
    log_error "Docker 命令仍然存在"
    uninstall_success=false
  else
    log_success "Docker 命令已移除"
  fi

  if is_docker_compose_installed; then
    log_error "Docker Compose 命令仍然存在"
    uninstall_success=false
  else
    log_success "Docker Compose 命令已移除"
  fi

  # 检查服务状态
  if service_is_enabled docker; then
    log_error "Docker 服务仍然启用"
    uninstall_success=false
  else
    log_success "Docker 服务已禁用"
  fi

  if service_is_enabled containerd; then
    log_error "containerd 服务仍然启用"
    uninstall_success=false
  else
    log_success "containerd 服务已禁用"
  fi

  # 检查关键目录是否已删除
  local critical_dirs=(
    "/etc/docker"
    "/data/docker"
    "/var/lib/docker"
    "/var/lib/containerd"
  )

  for dir in "${critical_dirs[@]}"; do
    if dir_exists "$dir"; then
      log_warning "目录仍然存在: $dir"
    else
      log_success "目录已删除: $dir"
    fi
  done

  return $([[ "$uninstall_success" == true ]] && echo 0 || echo 1)
}

# 显示卸载后信息
show_post_uninstall_info() {
  log_info "卸载完成信息:"
  show_separator "=" 50
  echo "以下内容已被删除:"
  echo "  ✓ Docker 和 Docker Compose 二进制文件"
  echo "  ✓ systemd 服务文件"
  echo "  ✓ Docker 配置文件 (/etc/docker)"
  echo "  ✓ Docker 数据目录 (/data/docker, /var/lib/docker)"
  echo "  ✓ containerd 数据目录"
  echo "  ✓ Docker 网络配置"
  echo "  ✓ Docker 用户组"
  echo "  ✓ 临时文件和缓存"
  echo ""
  echo "注意事项:"
  echo "  - 所有容器、镜像和卷数据已被永久删除"
  echo "  - 如需重新安装，请运行 docker-install-offline.sh"
  echo "  - 建议重启系统以确保所有更改生效"
  show_separator "=" 50
}

# 主函数
main() {
  # 解析命令行参数
  parse_arguments "$@"

  show_title "Docker 离线卸载开始"

  # 执行卸载步骤
  check_root
  show_warning_and_confirm
  check_docker_installation
  stop_all_containers
  stop_and_disable_services
  remove_package_docker
  remove_docker_binaries
  remove_systemd_services
  remove_docker_data
  cleanup_network
  cleanup_user_groups
  cleanup_temp_files

  # 验证卸载
  if verify_uninstallation; then
    log_success "Docker 离线卸载完成！"
    show_post_uninstall_info
    show_title "Docker 离线卸载完成"
    exit 0
  else
    log_warning "卸载过程中发现一些问题，请检查上述信息"
    log_info "您可能需要手动清理剩余的文件或重启系统"
    exit 1
  fi
}

# 执行主函数
main "$@"
