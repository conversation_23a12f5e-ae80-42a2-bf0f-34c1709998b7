# 综合信息平台 - Docker Swarm 部署方案

## 📋 项目概述

本项目提供了从 Docker Compose 到 Docker Swarm 的完整迁移解决方案，支持混合模式部署：
- **开发测试环境**: 使用 Docker Compose
- **生产环境**: 使用 Docker Swarm

## 🏗️ 架构设计

### 服务架构

```
综合信息平台 (57个微服务)
├── 基础设施服务栈 (8个服务) - 必需
│   ├── PostgreSQL 主数据库
│   ├── PostgreSQL 业务数据库  
│   ├── Redis 缓存
│   ├── RabbitMQ 消息队列
│   ├── TDengine 时序数据库
│   ├── MinIO 对象存储
│   ├── Node-RED 数据网关
│   └── FTP 服务器
├── 核心业务服务栈 (9个服务) - 必需
│   ├── 授权认证服务
│   ├── 用户权限管理
│   ├── 消息推送服务
│   ├── Nacos 配置中心
│   └── 前端服务模块
├── IoT 平台服务栈 (9个服务) - 可选
│   ├── 数据接入服务
│   ├── 数据解析服务
│   ├── 数据预警服务
│   └── 前端管理界面
├── 监控运维服务栈 (8个服务) - 可选
│   ├── Prometheus 监控
│   ├── Grafana 可视化
│   ├── AlertManager 告警
│   └── Portainer 管理
└── 其他可选服务栈
    ├── GIS 地理信息服务 (2个服务)
    ├── MES 业务前端服务 (2个服务)
    ├── 工作流服务 (2个服务)
    ├── 移动应用服务 (2个服务)
    ├── 视频监控服务 (7个服务)
    └── 辅助服务 (2个服务)
```

### 网络架构
- **ingress 网络**: Swarm 内置负载均衡
- **middle 网络**: 主业务网络 (10.0.1.0/24)
- **admin-net 网络**: 管理网络 (10.0.2.0/24)
- **monitoring-net 网络**: 监控网络 (10.0.3.0/24)

## 🚀 快速开始

### 环境要求

#### 硬件要求
- **CPU**: 最少 8 核心，推荐 16+ 核心
- **内存**: 最少 16GB，推荐 32GB+
- **存储**: 最少 500GB，推荐 1TB+ SSD
- **网络**: 千兆网卡

#### 软件要求
- Docker Engine 20.10+
- Docker Compose 2.0+
- Linux 操作系统 (Ubuntu 20.04+ / CentOS 8+)

### 安装部署

#### 1. 克隆项目
```bash
git clone <repository-url>
cd swarm_deploy
```

#### 2. 环境配置
```bash
# 复制环境配置模板
cp env/production.env.template env/production.env

# 编辑配置文件
vim env/production.env
```

#### 3. 初始化 Swarm 集群
```bash
# 初始化 Swarm 集群
./swarm_deploy.sh init

# 创建网络和存储卷
./swarm_deploy.sh init-networks
./swarm_deploy.sh init-volumes
```

#### 4. 配置密钥和配置文件
```bash
# 创建密钥配置文件
cp secrets/secrets.env.template secrets/secrets.env
vim secrets/secrets.env

# 创建密钥和配置
./swarm_deploy.sh init-secrets
./swarm_deploy.sh init-configs
```

#### 5. 部署服务
```bash
# 部署所有选定的服务栈
./swarm_deploy.sh deploy

# 或者分步部署
./swarm_deploy.sh deploy-stack infrastructure
./swarm_deploy.sh deploy-stack core
./swarm_deploy.sh deploy-stack iot
./swarm_deploy.sh deploy-stack monitoring
```

#### 6. 验证部署
```bash
# 检查集群状态
./swarm_deploy.sh status

# 检查服务健康状态
./scripts/health_check.sh
```

## 📁 目录结构

```
swarm_deploy/
├── README.md                           # 项目说明文档
├── services-catalog.md                 # 服务目录和选择
├── deployment-plan.md                  # 部署方案设计
├── stack-architecture.md              # Stack 架构设计
├── config-secrets-management.md       # 配置和密钥管理
├── network-storage-strategy.md        # 网络和存储策略
├── deployment-scripts.md              # 部署脚本设计
├── monitoring-operations.md           # 监控运维方案
├── migration-rollback-strategy.md     # 迁移回滚策略
├── best-practices.md                  # 最佳实践指南
├── project-summary.md                 # 项目总结报告
├── stacks/                            # Docker Stack 文件
│   ├── infrastructure-stack.yml       # 基础设施服务栈
│   ├── core-stack.yml                 # 核心业务服务栈
│   ├── iot-stack.yml                  # IoT 平台服务栈
│   ├── monitoring-stack.yml           # 监控运维服务栈
│   └── auxiliary-stack.yml            # 辅助服务栈
├── configs/                           # 配置文件
│   ├── nginx/                         # Nginx 配置
│   ├── prometheus/                    # Prometheus 配置
│   ├── grafana/                       # Grafana 配置
│   └── redis/                         # Redis 配置
├── secrets/                           # 密钥文件
│   └── secrets.env.template           # 密钥配置模板
├── scripts/                           # 管理脚本
│   ├── swarm_deploy.sh                # 主部署脚本
│   ├── stack_manager.sh               # 栈管理工具
│   ├── health_check.sh                # 健康检查工具
│   ├── backup.sh                      # 备份脚本
│   └── restore.sh                     # 恢复脚本
├── env/                               # 环境配置
│   ├── production.env.template        # 生产环境配置模板
│   ├── staging.env.template           # 测试环境配置模板
│   └── development.env.template       # 开发环境配置模板
└── docs/                              # 文档目录
    ├── deployment-guide.md            # 部署指南
    ├── operations-manual.md           # 运维手册
    └── troubleshooting.md             # 故障排除
```

## 🛠️ 管理命令

### 主要命令
```bash
# 集群管理
./swarm_deploy.sh init                 # 初始化 Swarm 集群
./swarm_deploy.sh check                # 检查集群状态
./swarm_deploy.sh status               # 查看服务状态

# 服务部署
./swarm_deploy.sh deploy               # 部署所有服务
./swarm_deploy.sh deploy-stack <name>  # 部署指定栈
./swarm_deploy.sh start                # 启动服务
./swarm_deploy.sh stop                 # 停止服务
./swarm_deploy.sh restart              # 重启服务

# 日志和监控
./swarm_deploy.sh logs <service> [lines] # 查看服务日志
./scripts/health_check.sh              # 健康检查
./scripts/stack_manager.sh list        # 列出所有栈

# 数据管理
./swarm_deploy.sh backup               # 备份数据
./swarm_deploy.sh restore <date>       # 恢复数据
```

### 栈管理
```bash
# 栈操作
./scripts/stack_manager.sh list                    # 列出所有栈
./scripts/stack_manager.sh details <stack>         # 查看栈详情
./scripts/stack_manager.sh scale <service> <count> # 扩展服务
./scripts/stack_manager.sh update-image <service> <image> # 更新镜像
./scripts/stack_manager.sh rollback <service>      # 回滚服务
```

## 📊 监控和运维

### 监控访问地址
- **Grafana 监控面板**: http://your-host:3000
- **Prometheus 指标**: http://your-host:9090
- **Portainer 管理界面**: http://your-host:9000
- **前端应用**: http://your-host

### 默认账号密码
- **Grafana**: admin / (在 secrets.env 中配置)
- **Portainer**: admin / (首次访问时设置)

### 监控指标
- 基础设施监控: CPU、内存、磁盘、网络
- 容器监控: 资源使用、重启次数、健康状态
- 应用监控: HTTP 请求、响应时间、错误率
- 数据库监控: 连接数、查询性能、锁状态
- 业务监控: IoT 数据接入量、用户活跃度

## 🔄 迁移指南

### 从 Docker Compose 迁移

#### 1. 迁移评估
```bash
# 运行迁移评估脚本
./scripts/migration_assessment.sh
```

#### 2. 数据备份
```bash
# 备份现有数据
./scripts/database_migration.sh backup
```

#### 3. 执行迁移
```bash
# 完整迁移流程
./scripts/database_migration.sh migrate
```

#### 4. 验证迁移
```bash
# 验证数据完整性
./scripts/health_check.sh
```

### 回滚策略
```bash
# 服务回滚
./scripts/rollback_service.sh service <service_name>

# 栈回滚
./scripts/rollback_service.sh stack <stack_name>

# 数据库回滚
./scripts/rollback_service.sh database <backup_date>

# 完整系统回滚
./scripts/rollback_service.sh full <backup_date>
```

## 🔐 安全配置

### 密钥管理
- 使用 Docker Secrets 管理敏感信息
- 支持密钥轮换和版本管理
- 最小权限原则访问控制

### 网络安全
- Overlay 网络加密传输
- 网络分段和访问控制
- 防火墙规则配置

### 数据安全
- 数据库连接加密
- 定期数据备份
- 访问日志审计

## 📈 性能优化

### 资源配置
- CPU 和内存限制配置
- 节点亲和性设置
- 存储性能优化

### 扩缩容策略
- 水平扩展配置
- 自动扩缩容脚本
- 负载均衡优化

## 🚨 故障处理

### 常见问题
1. **服务启动失败**: 检查资源约束和依赖关系
2. **网络连接问题**: 验证网络配置和防火墙规则
3. **性能问题**: 分析资源使用和服务负载
4. **数据问题**: 检查数据完整性和备份状态

### 应急响应
- 自动故障检测和恢复
- 服务降级和熔断机制
- 快速回滚和数据恢复

## 📚 文档和支持

### 详细文档
- [部署指南](docs/deployment-guide.md)
- [运维手册](docs/operations-manual.md)
- [故障排除](docs/troubleshooting.md)
- [最佳实践](best-practices.md)

### 技术支持
- 查看项目 Issues
- 参考最佳实践指南
- 联系技术支持团队

## 🎯 版本信息

- **当前版本**: v2.1.0
- **Docker 版本要求**: 20.10+
- **支持的操作系统**: Ubuntu 20.04+, CentOS 8+
- **最后更新**: 2024年

## 📄 许可证

本项目采用 MIT 许可证，详见 LICENSE 文件。

---

## 🤝 贡献指南

欢迎提交 Issue 和 Pull Request 来改进本项目。

### 开发流程
1. Fork 本项目
2. 创建特性分支
3. 提交更改
4. 创建 Pull Request

### 代码规范
- 遵循 Shell 脚本最佳实践
- 添加适当的注释和文档
- 确保脚本的可移植性

---

**注意**: 本方案基于您的实际需求设计，部署前请仔细阅读相关文档并根据实际环境调整配置参数。
