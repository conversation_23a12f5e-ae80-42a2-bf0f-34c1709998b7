#===============================================================================
# YAML 锚点定义 - 可复用的配置模板
#===============================================================================

# 基础默认配置锚点
x-base-defaults: &base_defaults
  restart: unless-stopped
  logging:
    driver: "json-file"
    options:
      max-size: "1g"
      max-file: "2"
  networks:
    - middle

# 网络配置
networks:
  middle:
    external: true
    name: ${DOCKER_NETWORK}

# 数据卷配置
volumes:
  # 数据库相关卷
  #base-postgres:
  #  external: true
  #base-business-postgres:
  #  external: true
  base-opengauss:
    external: true
  base-opengauss-backup:
    external: true
  base-opengauss-backup-log:
    external: true
  base-business-opengauss:
    external: true
  base-business-opengauss-backup:
    external: true
  base-business-opengauss-backup-log:
    external: true

  # 缓存相关卷
  base-redis:
    external: true

  # 消息队列相关卷
  base-rabbitmq:
    external: true
  #base-emqx-data:
  #  external: true
  #base-emqx-etc:
  #  external: true
  #base-emqx-log:
  #  external: true

  # 时序数据库相关卷
  base-influxdb:
    external: true
  base-tdengine-data:
    external: true
  base-tdengine-etc:
    external: true
  base-tdengine-log:
    external: true

  # 应用相关卷
  base-red-node:
    external: true
  #base-red-node_1:
  #  external: true
  #base-red-node_2:
  #  external: true
  #base-red-node_3:
  #  external: true

  # 存储相关卷
  bladex-minio:
    external: true
  base-sftpgo-data:
    external: true
  base-sftpgo-home:
    external: true
  #base-ftp:
  #  external: true

# 服务配置
services:
  # ===========================================
  # 基础数据库服务
  # ===========================================

  # PostgreSQL 主数据库
  #postgres:
  #  <<: *base_defaults
  #  #image: postgres:13.1
  #  #image: harbor2.qdbdtd.com:8088/middleware/postgres:10.23-alpine3.16
  #  image: harbor2.qdbdtd.com:8088/middleware/postgis:${PG_VERSION}
  #  container_name: base_postgres
  #  environment:
  #    - POSTGRES_USER=${POSTGRES_USERNAME}
  #    - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
  #    - POSTGRES_DB=${POSTGRES_DATABASE}
  #  volumes:
  #    - "base-postgres:/var/lib/postgresql/data"
  #    #- "./config/postgres_init.sql:/docker-entrypoint-initdb.d/postgres_init.sql:ro"
  #  #ports:
  #  #  - ${PORT_POSTGRES}:5432
  #---
  postgres:
    <<: *base_defaults
    image: harbor2.qdbdtd.com:8088/middleware/vastbase-g100-single-v1.4-3.0.8.24875-linux-x86_64:${OG_VERSION}
    container_name: base_postgres
    privileged: true
    environment:
      - VB_USERNAME=${POSTGRES_USERNAME}
      - VB_PASSWORD=${POSTGRES_PASSWORD}
      - VB_DBCOMPATIBILITY=${OG_DB_COMPATIBILITY:-PG}
    volumes:
      - "./config/openGauss:/home/<USER>/vastbase/lic"
      - "base-opengauss:/home/<USER>/data"
      - "base-opengauss-backup:/home/<USER>/backup"
      - "base-opengauss-backup-log:/home/<USER>/backup_log"
    #ports:
    #  - ${VB_PORT_HOST}:5432
    depends_on:
      postgres-permission-fix:
        condition: service_completed_successfully
  # postgres 权限修复服务
  postgres-permission-fix:
    <<: *base_defaults
    restart: "no"
    image: harbor2.qdbdtd.com:8088/middleware/alpine-ssh:3.12
    container_name: postgres-permission-fix
    user: root
    volumes:
      - "./config/openGauss:/home/<USER>/vastbase/lic"
      - "base-opengauss:/home/<USER>/data"
      - "base-opengauss-backup:/home/<USER>/backup"
      - "base-opengauss-backup-log:/home/<USER>/backup_log"
    command: >
      sh -c "
        echo 'Setting permissions for volumes...' &&
        chown -R 1000:1000 /home/<USER>/vastbase/lic &&
        chown -R 1000:1000 /home/<USER>/data &&
        chown -R 1000:1000 /home/<USER>/backup &&
        chown -R 1000:1000 /home/<USER>/backup_log &&
        echo 'Permissions set successfully.'
      "

  # PostgreSQL 业务数据库
  #business-postgres:
  #  <<: *base_defaults
  #  #image: harbor2.qdbdtd.com:8088/middleware/postgres:10.23-alpine3.16
  #  image: harbor2.qdbdtd.com:8088/middleware/postgis:${PG_VERSION}
  #  container_name: base_business_postgres
  #  environment:
  #    - POSTGRES_USER=${BLADEX_POSTGRES_USERNAME}
  #    - POSTGRES_PASSWORD=${BLADEX_POSTGRES_PASSWORD}
  #    - POSTGRES_DB=${BLADEX_POSTGRES_DATABASE}
  #  volumes:
  #    - "base-business-postgres:/var/lib/postgresql/data"
  #  #ports:
  #  #  - ${BLADEX_POSTGRES_PORT_HOST}:5432
  #---
  business-postgres:
    <<: *base_defaults
    image: harbor2.qdbdtd.com:8088/middleware/vastbase-g100-single-v1.4-3.0.8.24875-linux-x86_64:${OG_VERSION}
    container_name: base_business_postgres
    privileged: true
    environment:
      - VB_USERNAME=${BLADEX_POSTGRES_USERNAME}
      - VB_PASSWORD=${BLADEX_POSTGRES_PASSWORD}
      - VB_DBCOMPATIBILITY=${OG_DB_COMPATIBILITY:-PG}
    volumes:
      - "./config/openGauss:/home/<USER>/vastbase/lic"
      - "base-business-opengauss:/home/<USER>/data"
      - "base-business-opengauss-backup:/home/<USER>/backup"
      - "base-business-opengauss-backup-log:/home/<USER>/backup_log"
    #ports:
    #  - ${BLADEX_VB_PORT_HOST}:5432
    depends_on:
      business-postgres-permission-fix:
        condition: service_completed_successfully
  # business-postgres 权限修复服务
  business-postgres-permission-fix:
    <<: *base_defaults
    restart: "no"
    image: harbor2.qdbdtd.com:8088/middleware/alpine-ssh:3.12
    container_name: business-postgres-permission-fix
    user: root
    volumes:
      - "./config/openGauss:/home/<USER>/vastbase/lic"
      - "base-business-opengauss:/home/<USER>/data"
      - "base-business-opengauss-backup:/home/<USER>/backup"
      - "base-business-opengauss-backup-log:/home/<USER>/backup_log"
    command: >
      sh -c "
        echo 'Setting permissions for volumes...' &&
        chown -R 1000:1000 /home/<USER>/vastbase/lic &&
        chown -R 1000:1000 /home/<USER>/data &&
        chown -R 1000:1000 /home/<USER>/backup &&
        chown -R 1000:1000 /home/<USER>/backup_log &&
        echo 'Permissions set successfully.'
      "

  # ===========================================
  # 缓存服务
  # ===========================================

  # Redis 缓存服务
  redis:
    <<: *base_defaults
    #image: harbor2.qdbdtd.com:8088/middleware/redis:${REDIS_VERSION}
    #command: /bin/bash -c "envsubst < /usr/local/etc/redis/redis.template > /usr/local/etc/redis/redis.conf && exec redis-server /usr/local/etc/redis/redis.conf"
    image: harbor2.qdbdtd.com:8088/middleware/redis:7.4.3
    command: /bin/bash -c "cp /usr/local/etc/redis/redis.template /usr/local/etc/redis/redis.conf && redis-server /usr/local/etc/redis/redis.conf --requirepass \"$REDIS_PASSWORD\""
    environment:
      - REDIS_PASSWORD=${REDIS_PASSWORD}
    volumes:
      - "base-redis:/data"
      - "./config/redis.template:/usr/local/etc/redis/redis.template:ro"
    #ports:
    #  - 6379:6379

  # ===========================================
  # 消息队列服务
  # ===========================================

  # RabbitMQ 消息队列
  rabbitmq:
    <<: *base_defaults
    image: harbor2.qdbdtd.com:8088/middleware/rabbitmq:${RABBITMQ_VERSION}
    environment:
      - RABBITMQ_DEFAULT_USER=${RABBITMQ_USERNAME}
      - RABBITMQ_DEFAULT_PASS=${RABBITMQ_PASSWORD}
    volumes:
      - base-rabbitmq:/var/lib/rabbitmq
      - ./config/rabbitmq.conf:/etc/rabbitmq/rabbitmq.conf
    #ports:
    #  # don't expose rabbitmq to publish
    #  - ${PORT_RABBITMQ}:5672
    #  - ${PORT_RABBITMQ_DASHBOARD}:15672

  # EMQX MQTT 消息代理
  #emqx:
  #  <<: *base_defaults
  #  container_name: emqx
  #  image: emqx/emqx:4.4.19
  #  hostname: emqx
  #  environment:
  #    - "EMQX_NAME=emqx"
  #    #- "EMQX_HOST=node1.emqx.io"
  #    #- "EMQX_CLUSTER__DISCOVERY_STRATEGY=static"
  #    #- "EMQX_CLUSTER__STATIC__SEEDS=[<EMAIL>, <EMAIL>]"
  #  volumes:
  #    - "base-emqx-data:/opt/emqx/data"
  #    - "base-emqx-etc:/opt/emqx/etc"
  #    - "base-emqx-log:/opt/emqx/log"
  #  #ports:
  #  #  - "1883:1883"
  #  #  - "18083:18083"

  # Kafka 消息流处理平台
  #kafka:
  #  <<: *base_defaults
  #  container_name: kafka-1
  #  image: dockerhub.icu/bitnami/kafka:${KAFKA_VERSION}
  #  environment:
  #    # 允许使用kraft，即Kafka替代Zookeeper
  #    - KAFKA_ENABLE_KRAFT=yes
  #    # 集群配置
  #    - KAFKA_CFG_NODE_ID=1
  #    # 集群地址
  #    - KAFKA_CFG_CONTROLLER_QUORUM_VOTERS=1@kafka:${KAFKA_CONTROLLER_PORT}
  #    # broker.id，必须唯一，且与KAFKA_CFG_NODE_ID一致
  #    - KAFKA_BROKER_ID=1
  #    # kafka角色，做broker，也要做controller
  #    - KAFKA_CFG_PROCESS_ROLES=controller,broker
  #    # 定义kafka服务端socket监听端口（Docker内部的地址和端口）
  #    #- KAFKA_CFG_LISTENERS=PLAINTEXT://:9092,CONTROLLER://:9093
  #    - KAFKA_CFG_LISTENERS=SASL_PLAINTEXT://:9092,CONTROLLER://:9093
  #    # 定义客户端应使用的地址和端口、外网访问地址，宿主机地址和端口，不能是0.0.0.0
  #    #- KAFKA_CFG_ADVERTISED_LISTENERS=PLAINTEXT://${KAFKA_ADVERTISED_IP}:${KAFKA_ADVERTISED_PORT}
  #    - KAFKA_CFG_ADVERTISED_LISTENERS=SASL_PLAINTEXT://${KAFKA_ADVERTISED_IP}:${KAFKA_ADVERTISED_PORT}
  #    # 定义安全协议
  #    #- KAFKA_CFG_LISTENER_SECURITY_PROTOCOL_MAP=CONTROLLER:PLAINTEXT,PLAINTEXT:PLAINTEXT
  #    - KAFKA_CFG_LISTENER_SECURITY_PROTOCOL_MAP=CONTROLLER:PLAINTEXT,SASL_PLAINTEXT:SASL_PLAINTEXT
  #    # 客户端用户
  #    - KAFKA_CLIENT_USERS=${KAFKA_CLIENT_USERS}
  #    - KAFKA_CLIENT_PASSWORDS=${KAFKA_CLIENT_PASSWORDS}
  #    # 控制器的监听器名称，指定供外部使用的控制类请求信息
  #    - KAFKA_CFG_CONTROLLER_LISTENER_NAMES=CONTROLLER
  #    # 控制器的 SASL 机制协议
  #    - KAFKA_CFG_SASL_MECHANISM_CONTROLLER_PROTOCOL=PLAIN
  #    # 经纪人之间的通信监听器 安全协议
  #    - KAFKA_CFG_INTER_BROKER_LISTENER_NAME=SASL_PLAINTEXT
  #    # 经纪人之间的 SASL 机制协议
  #    - KAFKA_CFG_SASL_MECHANISM_INTER_BROKER_PROTOCOL=PLAIN
  #    # 经纪人之间的身份验证的用户名和密码
  #    - KAFKA_INTER_BROKER_USER=${KAFKA_INTER_BROKER_USER}
  #    - KAFKA_INTER_BROKER_PASSWORD=${KAFKA_INTER_BROKER_PASSWORD}
  #    # 设置broker最大内存，和初始内存
  #    - KAFKA_HEAP_OPTS=-Xmx512M -Xms256M
  #    # 使用Kafka时的集群id，集群内的Kafka都要用这个id做初始化，生成一个UUID即可(22byte)
  #    - KAFKA_KRAFT_CLUSTER_ID=${KAFKA_KRAFT_CLUSTER_ID}
  #    # 允许使用PLAINTEXT监听器，默认false，不建议在生产环境使用
  #    - ALLOW_PLAINTEXT_LISTENER=yes
  #    # 自动创建主题 允许
  #    - KAFKA_CFG_AUTO_CREATE_TOPICS_ENABLE=true
  #  # ports:
  #  #   - ${KAFKA_ADVERTISED_PORT}:9092
  #  #   - ${KAFKA_CONTROLLER_PORT}:9093
  # Kafka UI 管理界面
  #kafka-ui:
  #  <<: *base_defaults
  #  container_name: kafka-ui-1
  #  image: dockerhub.icu/provectuslabs/kafka-ui:latest
  #  hostname: kafka-ui-1
  #  restart: always
  #  environment:
  #    - TZ=Asia/Shanghai
  #    - AUTH_TYPE=LOGIN_FORM
  #    - SPRING_SECURITY_USER_NAME=${KAFKA_UI_USER_NAME}
  #    - SPRING_SECURITY_USER_PASSWORD=${KAFKA_UI_USER_PASSWORD}
  #    - DYNAMIC_CONFIG_ENABLED=${KAFKA_UI_DYNAMIC_CONFIG_ENABLED}
  #    - KAFKA_CLUSTERS_0_NAME=${KAFKA_UI_DEFAULT_CLUSTER_NODE_NAME}
  #    - KAFKA_CLUSTERS_0_BOOTSTRAPSERVERS=${KAFKA_ADVERTISED_IP}:${KAFKA_ADVERTISED_PORT}
  #    - KAFKA_CLUSTERS_0_PROPERTIES_SECURITY_PROTOCOL=SASL_PLAINTEXT
  #    - KAFKA_CLUSTERS_0_PROPERTIES_SASL_MECHANISM=PLAIN
  #    - KAFKA_CLUSTERS_0_PROPERTIES_SASL_JAAS_CONFIG='org.apache.kafka.common.security.plain.PlainLoginModule required username="${KAFKA_UI_DEFAULT_CLUSTER_NODE_USERNAME}" password="${KAFKA_UI_DEFAULT_CLUSTER_NODE_PASSWORD}";'
  #  depends_on:
  #    - kafka
  #  # network_mode: "bridge"
  #  # networks:
  #  #   - yklw
  #  # ports:
  #  #   - ${KAFKA_UI_PORT}:8080
  #  # volumes:
  #  #   - /home/<USER>/etc/localtime:/etc/localtime

  # ===========================================
  # 时序数据库服务
  # ===========================================

  # TDengine 时序数据库
  tdengine:
    <<: *base_defaults
    container_name: tdengine
    hostname: tdengine
    image: harbor2.qdbdtd.com:8088/middleware/tdengine:*******
    environment:
      - TZ=Asia/Shanghai
    volumes:
      - "base-tdengine-etc:/etc/taos"
      - "base-tdengine-data:/var/lib/taos"
      - "base-tdengine-log:/var/log/taos:rw"
      #- "./changeset/init/taosdb:/home/<USER>"
    #ports:
    #  - "6030-6049:6030-6049/tcp"
    #  - "6030-6049:6030-6049/udp"
    #  #- "6060:6060" # 企业版内 Monitor 服务的网络端口

  # InfluxDB 时序数据库
  #influxdb:
  #  <<: *base_defaults
  #  image: harbor2.qdbdtd.com:8088/middleware/influxdb:${TDENGINE_VERSION}
  #  environment:
  #    - INFLUXDB_DB=${INFLUXDB_DATABASE}
  #    - INFLUXDB_ADMIN_USER=${INFLUXDB_USERNAME}
  #    - INFLUXDB_ADMIN_PASSWORD=${INFLUXDB_PASSWORD}
  #    - INFLUXDB_ADMIN_ENABLED=${INFLUXDB_ADMIN_ENABLED}
  #    - INFLUXDB_HTTP_AUTH_ENABLED=${INFLUXDB_HTTP_AUTH_ENABLED}
  #  volumes:
  #    - "base-influxdb:/var/lib/influxdb"
  #    - "./config/influxdb.conf:/etc/influxdb/influxdb.conf:ro"

  # ===========================================
  # 存储服务
  # ===========================================

  # MinIO 对象存储
  bladex-minio:
    <<: *base_defaults
    image: harbor2.qdbdtd.com:8088/middleware/minio:${MINIO_VERSION}
    command: ['server', '/export']
    environment:
      - MINIO_ACCESS_KEY=${BLADEX_MINIO_ACCESS_KEY}
      - MINIO_SECRET_KEY=${BLADEX_MINIO_SECRET_KEY}
    volumes:
      - bladex-minio:/export
    #ports:
    #  - ${BLADEX_MINIO_PORT}:9000

  # FTP 文件服务器（SFTPGo 版本）
  ftpserver:
    <<: *base_defaults
    container_name: ftpserver
    image: drakkan/sftpgo:latest
    environment:
      - SFTPGO_FTPD__BINDINGS__0__PORT=${FTP_PORT:-2121}
      # FTP 被动模式绑定 IP
      #- SFTPGO_FTPD__BINDINGS__0__FORCE_PASSIVE_IP=${FTP_PASSIVE_HOST:***************}
      - SFTPGO_FTPD__PASSIVE_PORT_RANGE__START=${FTP_PASSIVE_PORTS_START:-12100}
      - SFTPGO_FTPD__PASSIVE_PORT_RANGE__END=${FTP_PASSIVE_PORTS_END:-12200}
      # 对应 Nginx 代理地址
      - SFTPGO_HTTPD__WEB_ROOT=${FTP_ADMIN_WEB_ROOT:-/ftp-admin}
    volumes:
      - base-sftpgo-data:/srv/sftpgo
      - base-sftpgo-home:/var/lib/sftpgo
    ports:
      # Web UI 端口
      #- "8080:8080"
      # SFTP 端口
      - "2022:2022"
      # FTP 端口
      - "2121:2121"
      # FTP 被动端口
      - "12100-12200:12100-12200"

  # FTP 文件服务器（vsftpd 版本）
  #ftpserver:
  #  <<: *base_defaults
  #  image: fauria/vsftpd
  #  environment:
  #    - FTP_USER=${FTP_USERNAME:ftpusr}
  #    - FTP_PASS=${FTP_PASSWORD:Swf#46Ds7&}
  #    #- PASV_ADDRESS=${FTP_PASSIVE_HOST:127.0.0.1}
  #    - PASV_MIN_PORT=${FTP_PASSIVE_PORTS_START:34140}
  #    - PASV_MAX_PORT=${FTP_PASSIVE_PORTS_END:34146}
  #  volumes:
  #    - "base-ftp:/home/<USER>"
  #  ports:
  #    - "20:20"
  #    - "21:21"
  #    - "34140-34146:34140-34146"

  # FTP 服务器（Pure-FTPd 版本）
  #ftpserver:
  #  <<: *base_defaults
  #  image: harbor2.qdbdtd.com:8088/middleware/pure-ftpd:1.0
  #  environment:
  #    - PUBLICHOST=${FTP_PUBLIC_HOST:127.0.0.1}
  #    - FTP_USER_NAME=${FTP_USERNAME:ftpusr}
  #    - FTP_USER_PASS=${FTP_PASSWORD:Swf#46Ds7&}
  #    - FTP_USER_HOME=${FTP_USER_HOME:/home/<USER>
  #    - FTP_MAX_CLIENTS=${FTP_MAX_CLIENTS:500}
  #    - FTP_MAX_CONNECTIONS=${FTP_MAX_CONNECTIONS:500}
  #    - FTP_PASSIVE_PORTS=${FTP_PASSIVE_PORTS:30000-30010}
  #    - ADDED_FLAGS=${FTP_ADDED_FLAGS:-d -d}
  #  volumes:
  #    - "base-ftp:/home/<USER>"
  #    - "$PWD/vsftp/passwd:/etc/pure-ftpd/passwd"
  #  ports:
  #    - "21:21"
  #    # max concurrent connection is 10, if need more, can change it
  #    - "30000-30010"

  # ===========================================
  # 应用服务
  # ===========================================

  # Node-RED 网关服务
  rednode:
    <<: *base_defaults
    image: harbor2.qdbdtd.com:8088/bjproduct/platform/gateway/node-gw:${NODEGW_VERSION}
    environment:
      - NDGW_RABBITMQ_USERNAME=${RABBITMQ_USERNAME}
      - NDGW_RABBITMQ_PASSWORD=${RABBITMQ_PASSWORD}
    volumes:
      - "base-red-node:/app/data"
      #- "base-ftp:/app/vsftp:rw"
      - "/home/<USER>/file_sync:/app/rsync:rw"
    #ports:
    #  - "1880:1880"

  # Node-RED 网关服务扩展实例
  #base_rednode_2:
  #  <<: *base_defaults
  #  container_name: base_rednode_2
  #  image: harbor2.qdbdtd.com:8088/bjproduct/platform/gateway/node-gw:${NODEGW_VERSION}
  #  volumes:
  #    - "base-red-node_1:/app/data"
  #    - "/home/<USER>/file_sync:/app/rsync:rw"
  #base_rednode_3:
  #  <<: *base_defaults
  #  container_name: base_rednode_3
  #  image: harbor2.qdbdtd.com:8088/bjproduct/platform/gateway/node-gw:${NODEGW_VERSION}
  #  volumes:
  #    - "base-red-node_2:/app/data"
  #    - "base-ftp:/app/vsftp:rw"
  #    - "/home/<USER>/file_sync/archive/sgjc:/app/rsync/archive/sgjc:rw"
  #    - "/home/<USER>/file_sync/archive/swjc:/app/rsync/archive/swjc:rw"
  #base_rednode_4:
  #  <<: *base_defaults
  #  container_name: base_rednode_4
  #  image: harbor2.qdbdtd.com:8088/bjproduct/platform/gateway/node-gw:${NODEGW_VERSION}
  #  volumes:
  #    - "base-red-node_3:/app/data"
  #    - "/home/<USER>/file_sync/archive:/app/rsync/archive:rw"

  # ===========================================
  # 文档预览服务
  # ===========================================

  # KKFileView 文档预览服务
  #kkfileview:
  #  <<: *base_defaults
  #  image: harbor2.qdbdtd.com:8088/middleware/kkfileview:${KKFILE_VERSION}
  #  container_name: kkfileview
  #  #ports:
  #  #  - ${PORT_FILE_PREVIEW}:8012

