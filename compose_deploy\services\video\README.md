# 视频服务 (Video Services)

本目录包含视频相关的服务组件。

## 📁 目录结构

```
video/
├── docker-compose.yml    # 视频服务编排配置
├── .env                  # 环境变量配置
└── config/               # 服务配置文件目录
```

## 🏗️ 包含的服务

根据 `docker-compose.yml` 配置，本模块包含的服务组件。

## 🚀 使用方法

### 启动服务

```bash
# 通过主部署脚本启动
./compose_deploy.sh start video

# 或直接使用docker-compose
cd services/video
docker compose up -d
```

### 查看服务状态

```bash
cd services/video
docker compose ps
```

### 查看服务日志

```bash
cd services/video
docker compose logs [service_name]
```

## 🔧 配置说明

主要配置通过 `.env` 文件和 `config/` 目录下的配置文件进行管理。

## 🔗 相关文档

- [主部署脚本说明](../../compose_deploy.sh)
- [服务总览](../README.md)
