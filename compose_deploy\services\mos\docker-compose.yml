# =============================================================================
# YAML 模板定义 (按功能分组)
# =============================================================================

# 基础服务模板
x-defaults: &defaults
  restart: unless-stopped
  logging:
    driver: "json-file"
    options:
      max-size: "1g"
      max-file: "2"
  networks:
    - middle

# Nginx 前端服务模板
x-nginx-defaults: &nginx-defaults
  <<: *defaults
  env_file:
    - ../base/.env
    - ../mos/.env
  healthcheck:
    test: ["CMD", "nginx", "-t"]
    interval: 30s
    timeout: 10s
    retries: 3
    start_period: 30s

# Nginx 模块服务模板 (使用默认配置)
x-nginx-reset: &nginx-reset
  <<: *nginx-defaults
  command: nginx -g 'daemon off;'
  volumes:
    - "./config/nginx-default.conf:/etc/nginx/conf.d/default.conf:ro"

# 后端服务模板
x-backend-defaults: &backend-defaults
  <<: *defaults
  env_file:
    - ../base/.env
    - ../mos/.env

# =============================================================================
# 网络和存储卷定义
# =============================================================================

networks:
  middle:
    external: true
    name: ${DOCKER_NETWORK}

volumes:
  # 监控图像数据存储
  data-monitor-image:
    external: true
  # Nacos 日志存储
  nacos-log:
    external: true

# =============================================================================
# 服务定义
# =============================================================================

services:

  # ---------------------------------------------------------------------------
  # 后端服务层 - 核心业务服务 (按依赖关系排序)
  # ---------------------------------------------------------------------------

  # 授权认证服务
  backend-service-authorization:
    <<: *backend-defaults
    container_name: service-authorization
    privileged: true
    image: harbor2.qdbdtd.com:8088/bjproduct/platform/services/auth_client:${AUTH_CLIENT_VERSION}
    environment:
      - PUBLIC_SERVICE_PATH=${PUBLIC_SERVICE_PATH}
      - PUBLIC_SERVICE_PORT=${PUBLIC_SERVICE_PORT}
    volumes:
      - "./config/project-data/licenceRepository:/home/<USER>/licenceRepository"
      - /sbin/dmidecode:/sbin/dmidecode
      - /dev/mem:/dev/mem
      - /etc/NetworkManager/system-connections:/tmp/info
      #- /etc/sysconfig/network-scripts:/tmp/info
      - ./config/init.sh:/home/<USER>/init.sh
    ports:
      - "${PORT_AUTH_BACKEND}:22260"
      #- "14148:22260"
    depends_on:
      - nacos
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:22260/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # BladeX 用户权限管理服务
  backend-process-user-permission:
    <<: *backend-defaults
    container_name: backend-platform
    image: harbor2.qdbdtd.com:8088/bjproduct/platform/bladex/bladex_boot:${BACKEND_BLADEX_VERSION}
    environment:
      #- BLADEX_POSTGRES_DATABASE=${BLADEX_POSTGRES_DATABASE}
      #- BLADEX_MONGO_DATABASES=${BLADEX_MONGO_DATABASES}
      #- BLADEX_MINIO_BUCKET_NAME=${BLADEX_MINIO_BUCKET_NAME}
      #- AUTH_PATH=${AUTH_PATH}
      #- POINT_URL=${POINT_URL}
      #- AUTH_ENABLE=${AUTH_ENABLE}
      - AUTH_SERVICE_NAME=${AUTH_SERVICE_NAME}
      - AUTH_PROJECT_NAME=${AUTH_PROJECT_NAME}
      - AUTH_SERVICE_ID=${AUTH_SERVICE_ID}
      - AUTH_PROJECT_ID=${AUTH_PROJECT_ID}
    #ports:
    #  - "${PORT_BASE_BACKEND}:9401"
    depends_on:
      - nacos
      - backend-service-authorization
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9401/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 90s

  # ---------------------------------------------------------------------------
  # 前端服务层 - 用户界面服务 (按模块重要性排序)
  # ---------------------------------------------------------------------------

  # 前端基座项目 (核心前端框架)
  frontend-module-base:
    <<: *nginx-reset
    image: harbor2.qdbdtd.com:8088/bjproduct/platform/front-services/base-frontend:${FRONTEND_BASE_VERSION}
    depends_on:
      - backend-process-user-permission

  # 前端系统管理模块
  frontend-module-system_admin:
    <<: *nginx-reset
    image: harbor2.qdbdtd.com:8088/bjproduct/platform/front-services/base-system-admin:${FRONTEND_SYSADMIN_VERSION}
    depends_on:
      - frontend-module-base

  # BladeX Saber 前端框架
  frontend-saber:
    <<: *nginx-defaults
    image: harbor2.qdbdtd.com:8088/bjproduct/platform/front-services/bladex_saber:${FRONTEND_BLADEX_VERSION}
    environment:
      - BACKEND_ENDPOINT=${BLADEX_BACKEND_ENDPOINT}
      #- BACKEND_DATA_MONITOR=${BACKEND_DATA_MONITOR}
    ports:
      - ${PORT_FRONTEND_SABER}:80
    depends_on:
      - backend-process-user-permission

  # ---------------------------------------------------------------------------
  # 网关层 - 统一入口和路由
  # ---------------------------------------------------------------------------

  # 前端网关 (统一入口，依赖所有后端服务)
  frontend-nginx-gateway:
    <<: *nginx-defaults
    # image: harbor2.qdbdtd.com:8088/middleware/nginx:${NGINX_VERSION}
    image: harbor2.qdbdtd.com:8088/middleware/nginx:1.24.0
    command: /bin/sh -c "DOLLAR=$$ envsubst < /etc/nginx/conf.d/default.conf.template > /etc/nginx/conf.d/default.conf && nginx -g 'daemon off;'"
    environment:
      # 上游服务配置
      - UPSTREAM_CMS=${UPSTREAM_CMS}
      - UPSTREAM_GIS_API=${UPSTREAM_GIS_API}
      - UPSTREAM_GIS_STATIC=${UPSTREAM_GIS_STATIC}
      - UPSTREAM_GIS_THREE=${UPSTREAM_GIS_THREE}
      - UPSTREAM_FINEREPORT=${UPSTREAM_FINEREPORT}
    volumes:
      # Nginx 配置文件
      - "./config/nginx.conf.d/:/etc/nginx/conf.d/localhost.etc/:ro"
      - "./config/nginx.conf:/etc/nginx/conf.d/default.conf.template:ro"
      # 静态资源挂载
      - "data-monitor-image:/usr/share/nginx/html/topo/images:ro"
      - "./static:/usr/share/nginx/html/static:ro"
      - "/home/<USER>/file_sync/archive:/usr/share/nginx/archive:ro"
    ports:
      - "${PORT_FRONTEND}:80"
    depends_on:
      - frontend-module-base
      - frontend-module-system_admin
      - frontend-saber
      - frontend-module-alarm_serve
      - backend-process-user-permission
      - backend-service-authorization
      - backend-push-msg

  # ---------------------------------------------------------------------------
  # 基础设施层 - 配置中心和服务发现
  # ---------------------------------------------------------------------------

  # Nacos 配置中心和服务发现
  nacos:
    <<: *backend-defaults
    image: harbor2.qdbdtd.com:8088/middleware/nacos-pg:v2.1.0
    container_name: nacos-standalone-pg
    environment:
      - PREFER_HOST_MODE=hostname
      - MODE=standalone
      - SPRING_DATASOURCE_PLATFORM=postgresql
      - PG_SERVICE_HOST=postgres
      - PG_SERVICE_PORT=${POSTGRES_PORT}
      - PG_SERVICE_PASSWORD=${POSTGRES_PASSWORD}
      - PG_SERVICE_USER=${POSTGRES_USERNAME}
      - PG_SERVICE_DB_NAME=nacos
    volumes:
      - "nacos-log:/home/<USER>/logs"
      #- ./init.d/custom.properties:/home/<USER>/init.d/custom.properties
    #ports:
    #  - "${PORT_NACOS}:8848"
    #  - "${PORT_GRPC_NACOS}:9848"
    #  - "9555:9555"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8848/nacos/v1/ns/operator/metrics"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s
