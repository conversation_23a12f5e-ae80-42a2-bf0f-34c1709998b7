# 3）Docker Swarm 配置管理和密钥管理方案

## 🔐 密钥管理策略

### 密钥分类和管理

基于您选择的服务栈，需要管理以下密钥：

#### 🗄️ 数据库密钥
```bash
# PostgreSQL 数据库密码
echo "your_postgres_password" | docker secret create postgres_password -
echo "your_business_postgres_password" | docker secret create business_postgres_password -

# Redis 密码
echo "your_redis_password" | docker secret create redis_password -

# TDengine 密码
echo "your_tdengine_password" | docker secret create tdengine_password -
```

#### 🔄 消息队列密钥
```bash
# RabbitMQ 认证信息
echo "your_rabbitmq_user" | docker secret create rabbitmq_user -
echo "your_rabbitmq_password" | docker secret create rabbitmq_password -
```

#### 📦 对象存储密钥
```bash
# MinIO 访问密钥
echo "your_minio_access_key" | docker secret create minio_access_key -
echo "your_minio_secret_key" | docker secret create minio_secret_key -
```

#### 🔑 应用服务密钥
```bash
# JWT 签名密钥
echo "your_jwt_secret" | docker secret create jwt_secret -

# 授权服务密钥
echo "your_auth_secret" | docker secret create auth_secret -

# Nacos 配置中心密钥
echo "your_nacos_password" | docker secret create nacos_password -
```

#### 📊 监控服务密钥
```bash
# Grafana 管理员密码
echo "your_grafana_admin_password" | docker secret create grafana_admin_password -

# Prometheus 认证密钥
echo "your_prometheus_password" | docker secret create prometheus_password -

# AlertManager 密钥
echo "your_alertmanager_secret" | docker secret create alertmanager_secret -
```

#### 🔧 辅助服务密钥
```bash
# SSH 服务密码
echo "your_ssh_password" | docker secret create ssh_password -

# Portainer 管理员密码
echo "your_portainer_password" | docker secret create portainer_password -
```

### 🛡️ 密钥创建脚本

```bash
#!/bin/bash
# create_secrets.sh - 批量创建 Docker Secrets

set -e

echo "🔐 开始创建 Docker Swarm 密钥..."

# 检查是否为 Swarm Manager
if ! docker info --format '{{.Swarm.LocalNodeState}}' | grep -q active; then
    echo "❌ 错误：当前节点不是 Swarm Manager"
    exit 1
fi

# 密钥配置文件
SECRETS_CONFIG="secrets.env"

if [ ! -f "$SECRETS_CONFIG" ]; then
    echo "❌ 错误：未找到密钥配置文件 $SECRETS_CONFIG"
    echo "请创建 secrets.env 文件并配置所需密钥"
    exit 1
fi

# 加载密钥配置
source "$SECRETS_CONFIG"

# 创建数据库密钥
create_secret() {
    local secret_name=$1
    local secret_value=$2
    
    if docker secret inspect "$secret_name" >/dev/null 2>&1; then
        echo "⚠️  密钥 $secret_name 已存在，跳过创建"
    else
        echo "$secret_value" | docker secret create "$secret_name" -
        echo "✅ 创建密钥: $secret_name"
    fi
}

# 批量创建密钥
echo "📊 创建数据库密钥..."
create_secret "postgres_password" "$POSTGRES_PASSWORD"
create_secret "business_postgres_password" "$BUSINESS_POSTGRES_PASSWORD"
create_secret "redis_password" "$REDIS_PASSWORD"
create_secret "tdengine_password" "$TDENGINE_PASSWORD"

echo "🔄 创建消息队列密钥..."
create_secret "rabbitmq_user" "$RABBITMQ_USER"
create_secret "rabbitmq_password" "$RABBITMQ_PASSWORD"

echo "📦 创建对象存储密钥..."
create_secret "minio_access_key" "$MINIO_ACCESS_KEY"
create_secret "minio_secret_key" "$MINIO_SECRET_KEY"

echo "🔑 创建应用服务密钥..."
create_secret "jwt_secret" "$JWT_SECRET"
create_secret "auth_secret" "$AUTH_SECRET"
create_secret "nacos_password" "$NACOS_PASSWORD"

echo "📊 创建监控服务密钥..."
create_secret "grafana_admin_password" "$GRAFANA_ADMIN_PASSWORD"
create_secret "prometheus_password" "$PROMETHEUS_PASSWORD"
create_secret "alertmanager_secret" "$ALERTMANAGER_SECRET"

echo "🔧 创建辅助服务密钥..."
create_secret "ssh_password" "$SSH_PASSWORD"
create_secret "portainer_password" "$PORTAINER_PASSWORD"

echo "🎉 所有密钥创建完成！"

# 列出所有密钥
echo "📋 当前 Docker Secrets 列表："
docker secret ls
```

### 🔄 密钥轮换策略

```bash
#!/bin/bash
# rotate_secrets.sh - 密钥轮换脚本

rotate_secret() {
    local secret_name=$1
    local new_secret_value=$2
    local affected_services=("${@:3}")
    
    echo "🔄 开始轮换密钥: $secret_name"
    
    # 创建新版本密钥
    local new_secret_name="${secret_name}_v$(date +%Y%m%d%H%M%S)"
    echo "$new_secret_value" | docker secret create "$new_secret_name" -
    
    # 更新使用该密钥的服务
    for service in "${affected_services[@]}"; do
        echo "📝 更新服务: $service"
        docker service update \
            --secret-rm "$secret_name" \
            --secret-add "source=$new_secret_name,target=/run/secrets/$secret_name" \
            "$service"
    done
    
    # 等待服务更新完成
    sleep 30
    
    # 删除旧密钥
    docker secret rm "$secret_name"
    
    # 重命名新密钥
    docker secret create "$secret_name" < <(docker secret inspect "$new_secret_name" --format '{{.Spec.Data}}' | base64 -d)
    docker secret rm "$new_secret_name"
    
    echo "✅ 密钥轮换完成: $secret_name"
}

# 示例：轮换 PostgreSQL 密码
# rotate_secret "postgres_password" "new_password_here" "infrastructure_postgres" "core_nacos"
```

## ⚙️ 配置管理策略

### 配置文件分类

#### 🌐 网络配置
```yaml
# configs/nginx/nginx.conf
user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

events {
    worker_connections 1024;
    use epoll;
    multi_accept on;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;
    
    # 日志格式
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';
    
    access_log /var/log/nginx/access.log main;
    
    # 性能优化
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    
    # Gzip 压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
    
    # 上游服务配置
    include /etc/nginx/conf.d/upstream.conf;
    
    # 虚拟主机配置
    include /etc/nginx/conf.d/*.conf;
}
```

#### 📊 监控配置
```yaml
# configs/prometheus/prometheus.yml
global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    cluster: 'swarm-cluster'
    environment: 'production'

rule_files:
  - "/etc/prometheus/rules/*.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

scrape_configs:
  # Docker Swarm 服务发现
  - job_name: 'docker-swarm'
    dockerswarm_sd_configs:
      - host: unix:///var/run/docker.sock
        role: services
    relabel_configs:
      - source_labels: [__meta_dockerswarm_service_name]
        target_label: job
      - source_labels: [__meta_dockerswarm_service_label_prometheus_job]
        target_label: job
        regex: (.+)
      - source_labels: [__meta_dockerswarm_service_label_prometheus_port]
        target_label: __address__
        regex: (.+)
        replacement: ${1}

  # 基础设施监控
  - job_name: 'infrastructure'
    static_configs:
      - targets:
        - 'postgres:5432'
        - 'redis:6379'
        - 'rabbitmq:15672'
        - 'tdengine:6041'
        - 'minio:9000'

  # 应用服务监控
  - job_name: 'applications'
    static_configs:
      - targets:
        - 'auth-service:22260'
        - 'bladex-backend:9401'
        - 'data-storage:9001'
        - 'data-monitor:9001'

  # 节点监控
  - job_name: 'node-exporter'
    static_configs:
      - targets:
        - 'node-exporter:9100'

  # 容器监控
  - job_name: 'cadvisor'
    static_configs:
      - targets:
        - 'cadvisor:8080'
```

#### 🗄️ 数据库配置
```ini
# configs/redis/redis.template
# Redis 配置模板
bind 0.0.0.0
port 6379
timeout 0
tcp-keepalive 300

# 内存管理
maxmemory 2gb
maxmemory-policy allkeys-lru

# 持久化配置
save 900 1
save 300 10
save 60 10000

# 日志配置
loglevel notice
logfile ""

# 安全配置
# requirepass 将通过 Docker Secret 设置

# 性能优化
tcp-backlog 511
databases 16
```

### 🔧 配置创建脚本

```bash
#!/bin/bash
# create_configs.sh - 批量创建 Docker Configs

set -e

echo "⚙️ 开始创建 Docker Swarm 配置..."

# 检查是否为 Swarm Manager
if ! docker info --format '{{.Swarm.LocalNodeState}}' | grep -q active; then
    echo "❌ 错误：当前节点不是 Swarm Manager"
    exit 1
fi

# 配置文件目录
CONFIGS_DIR="configs"

if [ ! -d "$CONFIGS_DIR" ]; then
    echo "❌ 错误：未找到配置文件目录 $CONFIGS_DIR"
    exit 1
fi

# 创建配置
create_config() {
    local config_name=$1
    local config_file=$2
    
    if [ ! -f "$config_file" ]; then
        echo "⚠️  配置文件不存在: $config_file"
        return 1
    fi
    
    if docker config inspect "$config_name" >/dev/null 2>&1; then
        echo "⚠️  配置 $config_name 已存在，跳过创建"
    else
        docker config create "$config_name" "$config_file"
        echo "✅ 创建配置: $config_name"
    fi
}

# 批量创建配置
echo "🌐 创建网络配置..."
create_config "nginx_main_config" "$CONFIGS_DIR/nginx/nginx.conf"
create_config "nginx_default_config" "$CONFIGS_DIR/nginx/nginx-default.conf"
create_config "nginx_upstream_config" "$CONFIGS_DIR/nginx/upstream.conf"

echo "📊 创建监控配置..."
create_config "prometheus_config" "$CONFIGS_DIR/prometheus/prometheus.yml"
create_config "prometheus_rules" "$CONFIGS_DIR/prometheus/alert.rules.yml"
create_config "grafana_config" "$CONFIGS_DIR/grafana/grafana.ini"
create_config "alertmanager_config" "$CONFIGS_DIR/prometheus/alertmanager.yml"

echo "🗄️ 创建数据库配置..."
create_config "redis_config" "$CONFIGS_DIR/redis/redis.template"
create_config "rabbitmq_config" "$CONFIGS_DIR/rabbitmq/rabbitmq.conf"
create_config "tdengine_config" "$CONFIGS_DIR/tdengine/taos.cfg"

echo "🎉 所有配置创建完成！"

# 列出所有配置
echo "📋 当前 Docker Configs 列表："
docker config ls
```

### 🔄 配置版本管理

```bash
#!/bin/bash
# update_config.sh - 配置更新脚本

update_config() {
    local config_name=$1
    local config_file=$2
    local affected_services=("${@:3}")
    
    echo "🔄 开始更新配置: $config_name"
    
    # 创建新版本配置
    local timestamp=$(date +%Y%m%d%H%M%S)
    local new_config_name="${config_name}_v${timestamp}"
    
    docker config create "$new_config_name" "$config_file"
    
    # 更新使用该配置的服务
    for service in "${affected_services[@]}"; do
        echo "📝 更新服务: $service"
        docker service update \
            --config-rm "$config_name" \
            --config-add "source=$new_config_name,target=/etc/config/$config_name" \
            "$service"
    done
    
    # 等待服务更新完成
    sleep 30
    
    # 删除旧配置
    docker config rm "$config_name"
    
    # 重命名新配置
    docker config create "$config_name" "$config_file"
    docker config rm "$new_config_name"
    
    echo "✅ 配置更新完成: $config_name"
}

# 示例：更新 Nginx 配置
# update_config "nginx_main_config" "configs/nginx/nginx.conf" "core_frontend-gateway"
```

## 🔒 安全最佳实践

### 密钥安全原则

1. **最小权限原则**: 每个服务只能访问必需的密钥
2. **密钥轮换**: 定期更换密钥，建议每季度轮换一次
3. **访问审计**: 记录密钥访问日志
4. **加密传输**: 所有密钥传输都使用 TLS 加密
5. **备份策略**: 安全备份密钥到离线存储

### 配置安全原则

1. **版本控制**: 所有配置文件纳入版本控制
2. **环境隔离**: 不同环境使用不同的配置
3. **敏感信息分离**: 敏感信息使用 Docker Secrets
4. **配置验证**: 部署前验证配置文件语法
5. **回滚机制**: 支持快速回滚到上一版本配置

### 监控和告警

```yaml
# 密钥和配置监控告警规则
groups:
  - name: secrets_configs
    rules:
      - alert: SecretAccessFailure
        expr: increase(docker_secret_access_failures_total[5m]) > 0
        for: 1m
        labels:
          severity: warning
        annotations:
          summary: "Docker Secret 访问失败"
          description: "服务 {{ $labels.service }} 访问密钥失败"

      - alert: ConfigUpdateFailure
        expr: increase(docker_config_update_failures_total[5m]) > 0
        for: 1m
        labels:
          severity: warning
        annotations:
          summary: "Docker Config 更新失败"
          description: "配置 {{ $labels.config }} 更新失败"

      - alert: SecretExpiringSoon
        expr: (docker_secret_created_timestamp + 7776000) - time() < 604800
        for: 1h
        labels:
          severity: info
        annotations:
          summary: "密钥即将过期"
          description: "密钥 {{ $labels.secret }} 将在一周内过期，请及时轮换"
```

这个配置管理和密钥管理方案为您的 Docker Swarm 集群提供了完整的安全保障，确保敏感信息的安全存储和传输，同时支持配置的版本化管理和动态更新。
