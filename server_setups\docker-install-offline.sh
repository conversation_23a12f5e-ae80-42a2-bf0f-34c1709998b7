#!/bin/bash

# shellcheck source=_common.sh
# shellcheck disable=SC1091

#===============================================================================
# Docker 离线安装脚本
#===============================================================================
# 功能描述: 离线安装 Docker CE 和 Docker Compose
# 适用系统: CentOS 7.x / RHEL 7.x
# 执行权限: 需要 root 权限
# 依赖文件:
#   - _common.sh (共通函数库)
#   - docker/setups/x86_64/docker-*.tar (Docker 二进制包)
#   - docker/systemd/docker.service (Docker systemd 服务文件)
#   - docker/systemd/containerd.service (containerd systemd 服务文件)
#   - docker/systemd/docker.socket (Docker socket 文件)
#   - docker/setups/x86_64/docker-compose-* (Docker Compose 二进制文件)
#===============================================================================

set -e  # 遇到错误立即退出

# 加载共通函数库
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
if [[ -f "$SCRIPT_DIR/_common.sh" ]]; then
  # shellcheck source=./_common.sh
  source "$SCRIPT_DIR/_common.sh"
else
  echo "错误: 找不到共通函数库 _common.sh"
  exit 1
fi

# 全局变量
DOCKER_TAR_FILE=""
DOCKER_COMPOSE_FILE=""

# 检查必需文件是否存在
check_required_files() {
  local docker_dir="$SCRIPT_DIR/docker"

  log_info "检查必需文件..."

  # 检查 Docker 二进制包
  local docker_tar_files=("$docker_dir/setups/x86_64"/docker-*.tar)
  if [[ ! -f "${docker_tar_files[0]}" ]]; then
    log_error "未找到 Docker 二进制包文件: $docker_dir/setups/x86_64/docker-*.tar"
    exit 1
  fi
  DOCKER_TAR_FILE="${docker_tar_files[0]}"
  log_info "找到 Docker 二进制包: $(basename "$DOCKER_TAR_FILE")"

  # 检查 systemd 服务文件
  local required_systemd_files=(
    "$docker_dir/systemd/docker.service"
    "$docker_dir/systemd/containerd.service"
  )

  for file in "${required_systemd_files[@]}"; do
    if [[ ! -f "$file" ]]; then
      log_error "未找到必需文件: $file"
      exit 1
    fi
    log_info "找到服务文件: $(basename "$file")"
  done

  # 检查 Docker Compose 二进制文件
  local compose_files=("$docker_dir/setups/x86_64"/docker-compose-*)
  if [[ ! -f "${compose_files[0]}" ]]; then
    log_error "未找到 Docker Compose 二进制文件: $docker_dir/setups/x86_64/docker-compose-*"
    exit 1
  fi
  DOCKER_COMPOSE_FILE="${compose_files[0]}"
  log_info "找到 Docker Compose 二进制文件: $(basename "$DOCKER_COMPOSE_FILE")"
}

# 停止并移除现有的 Docker 服务
remove_existing_docker() {
  log_info "检查并移除现有的 Docker 安装..."

  # 停止 Docker 相关服务
  safe_stop_service docker
  safe_stop_service containerd
  safe_stop_service docker.socket

  # 禁用服务
  safe_disable_service docker
  safe_disable_service containerd
  safe_disable_service docker.socket

  # 移除旧版本的 Docker（如果通过 yum 安装）
  if rpm -qa | grep -q docker; then
    log_info "移除通过 yum 安装的 Docker 包..."
    yum remove -y docker docker-common docker-selinux docker-engine docker-ce docker-ce-cli containerd.io 2>/dev/null || true
  fi

  # 清理可能存在的旧版本二进制文件
  local old_binaries=(
    "/usr/bin/docker"
    "/usr/bin/dockerd"
    "/usr/bin/docker-proxy"
    "/usr/bin/docker-init"
    "/usr/bin/containerd"
    "/usr/bin/containerd-shim"
    "/usr/bin/containerd-shim-runc-v2"
    "/usr/bin/runc"
    "/usr/bin/ctr"
  )

  for binary in "${old_binaries[@]}"; do
    if [[ -f "$binary" && ! -L "$binary" ]]; then
      log_info "移除旧版本二进制文件: $binary"
      rm -f "$binary"
    fi
  done
}

# 安装 Docker 二进制文件
install_docker_binaries() {
  log_info "开始安装 Docker 二进制文件..."

  # 创建临时目录
  local temp_dir=$(mktemp -d)
  trap "rm -rf $temp_dir" EXIT

  # 解压 Docker 二进制包
  log_info "解压 Docker 二进制包..."
  tar -xf "$DOCKER_TAR_FILE" -C "$temp_dir"

  # 移动二进制文件到 /usr/local/bin
  log_info "安装 Docker 二进制文件到 /usr/local/bin..."
  if [[ -d "$temp_dir/docker" ]]; then
    # 确保目标目录存在
    safe_create_dir "/usr/local/bin"

    # 复制文件，如果已存在则覆盖
    for file in "$temp_dir/docker"/*; do
      if [[ -f "$file" ]]; then
        local filename=$(basename "$file")
        cp -f "$file" "/usr/local/bin/$filename"
        log_info "安装二进制文件: $filename"
      fi
    done
  else
    log_error "解压后未找到 docker 目录"
    exit 1
  fi

  # 设置执行权限
  local binaries=($(get_docker_binaries))
  for binary in "${binaries[@]}"; do
    if file_exists "/usr/local/bin/$binary"; then
      set_executable "/usr/local/bin/$binary"
    else
      log_warning "二进制文件不存在: /usr/local/bin/$binary"
    fi
  done

  # 创建软链接到 /usr/bin（如果不存在或者是旧的链接）
  log_info "创建软链接..."
  for binary in "${binaries[@]}"; do
    if file_exists "/usr/local/bin/$binary"; then
      # 如果目标已存在且不是指向正确位置的链接，先删除
      if [[ -e "/usr/bin/$binary" && ! -L "/usr/bin/$binary" ]]; then
        log_warning "移除非链接文件: /usr/bin/$binary"
        rm -f "/usr/bin/$binary"
      elif [[ -L "/usr/bin/$binary" ]]; then
        local current_target=$(readlink "/usr/bin/$binary")
        if [[ "$current_target" != "/usr/local/bin/$binary" ]]; then
          log_info "更新软链接: /usr/bin/$binary"
          rm -f "/usr/bin/$binary"
        fi
      fi

      # 创建或重新创建软链接
      if [[ ! -e "/usr/bin/$binary" ]]; then
        safe_create_symlink "/usr/local/bin/$binary" "/usr/bin/$binary"
      fi
    else
      log_warning "二进制文件不存在: /usr/local/bin/$binary"
    fi
  done
}

# 配置 systemd 服务文件
configure_systemd_services() {
  log_info "配置 systemd 服务文件..."

  local systemd_dir="$SCRIPT_DIR/docker/systemd"

  # 复制服务文件
  safe_copy_file "$systemd_dir/containerd.service" "/etc/systemd/system/"
  safe_copy_file "$systemd_dir/docker.service" "/etc/systemd/system/"

  # 如果存在 docker.socket 文件，也复制它
  if file_exists "$systemd_dir/docker.socket"; then
    safe_copy_file "$systemd_dir/docker.socket" "/etc/systemd/system/"
    log_info "复制 docker.socket 服务文件"
  fi

  log_info "systemd 服务文件配置完成"
}

# 创建 Docker 配置目录和配置文件
configure_docker() {
  log_info "配置 Docker..."

  # 创建 docker 用户组
  safe_create_group docker

  # 创建 Docker 配置目录
  safe_create_dir "/etc/docker"
  safe_create_dir "/data/docker"

  # 备份现有配置文件（如果存在）
  if [[ -f "/etc/docker/daemon.json" ]]; then
    local backup_file="/etc/docker/daemon.json.backup.$(date +%Y%m%d_%H%M%S)"
    log_info "备份现有配置文件到: $backup_file"
    cp "/etc/docker/daemon.json" "$backup_file"
  fi

  # 创建 Docker daemon 配置文件
  log_info "创建 Docker daemon 配置文件..."
  cat > /etc/docker/daemon.json << 'EOF'
{
  "log-driver": "json-file",
  "data-root": "/data/docker",
  "log-opts": {
    "max-size": "50m",
    "max-file": "3"
  },
  "registry-mirrors": [
    "https://5gmkcj2a.mirror.aliyuncs.com",
    "https://docker.m.daocloud.io"
  ],
  "storage-driver": "overlay2"
}
EOF

  # 设置正确的权限
  chmod 644 "/etc/docker/daemon.json"

  log_success "Docker 配置文件创建完成"
}

# 启动 Docker 服务
start_docker_services() {
  log_info "启动 Docker 服务..."

  # 重新加载 systemd
  reload_systemd

  # 启用并启动 containerd
  log_info "配置 containerd 服务..."
  if ! systemctl is-enabled containerd >/dev/null 2>&1; then
    systemctl enable containerd
    log_info "已启用 containerd 服务"
  else
    log_info "containerd 服务已启用"
  fi

  if ! service_is_active containerd; then
    log_info "启动 containerd 服务..."
    systemctl start containerd

    # 等待 containerd 启动
    if ! wait_for_service containerd 30; then
      log_error "containerd 服务启动失败"
      systemctl status containerd --no-pager || true
      exit 1
    fi
  else
    log_info "containerd 服务已运行"
  fi

  # 启用并启动 Docker
  log_info "配置 Docker 服务..."
  if ! systemctl is-enabled docker >/dev/null 2>&1; then
    systemctl enable docker
    log_info "已启用 Docker 服务"
  else
    log_info "Docker 服务已启用"
  fi

  if ! service_is_active docker; then
    log_info "启动 Docker 服务..."
    systemctl start docker

    # 等待 Docker 启动
    if ! wait_for_service docker 30; then
      log_error "Docker 服务启动失败"
      systemctl status docker --no-pager || true
      journalctl -u docker --no-pager -n 20 || true
      exit 1
    fi
  else
    log_info "Docker 服务已运行"
    # 如果服务已运行，重启以应用新配置
    log_info "重启 Docker 服务以应用新配置..."
    systemctl restart docker
    if ! wait_for_service docker 30; then
      log_error "Docker 服务重启失败"
      exit 1
    fi
  fi

  log_success "Docker 服务启动完成"
}

# 安装 Docker Compose 插件
# 基于 Docker 官方文档: https://docs.docker.com/compose/install/linux/#install-the-plugin-manually
install_docker_compose() {
  log_info "安装 Docker Compose 插件..."

  # 检查是否有 Docker Compose 二进制文件
  if [[ ! -f "$DOCKER_COMPOSE_FILE" ]]; then
    log_error "未找到 Docker Compose 二进制文件: $DOCKER_COMPOSE_FILE"
    exit 1
  fi

  # 检查文件架构是否匹配
  if ! file "$DOCKER_COMPOSE_FILE" | grep -q "$(uname -m)"; then
    log_error "Docker Compose 二进制文件架构不匹配: $(file "$DOCKER_COMPOSE_FILE")"
    log_error "系统架构: $(uname -m)"
    exit 1
  fi

  # 确保 Docker 服务正在运行
  if ! service_is_active docker; then
    log_error "Docker 服务未运行，无法安装插件"
    exit 1
  fi

  # 检查是否已经安装，如果已安装则先移除（按照官方文档路径）
  local plugin_paths=(
    "/usr/local/lib/docker/cli-plugins/docker-compose"
  )

  # 如果是通过 sudo 运行，也检查用户目录
  if [[ -n "$SUDO_USER" ]]; then
    local user_home=$(eval echo "~$SUDO_USER")
    plugin_paths+=("$user_home/.docker/cli-plugins/docker-compose")
  fi

  for plugin_path in "${plugin_paths[@]}"; do
    if [[ -f "$plugin_path" ]]; then
      log_info "移除已存在的插件: $plugin_path"
      rm -f "$plugin_path"
    fi
  done

  # 创建插件目录（按照 Docker 官方文档的标准路径）
  safe_create_dir "/usr/local/lib/docker/cli-plugins"

  # 用户级安装路径（如果当前用户不是 root）
  if [[ -n "$SUDO_USER" ]]; then
    local user_home=$(eval echo "~$SUDO_USER")
    safe_create_dir "$user_home/.docker/cli-plugins"
  fi

  # 安装到系统级目录（官方文档路径）
  safe_copy_file "$DOCKER_COMPOSE_FILE" "/usr/local/lib/docker/cli-plugins/docker-compose"

  # 设置正确的权限（按照官方文档要求）
  chmod +x "/usr/local/lib/docker/cli-plugins/docker-compose"

  # 如果存在普通用户，也为其安装
  if [[ -n "$SUDO_USER" ]]; then
    local user_home=$(eval echo "~$SUDO_USER")
    safe_copy_file "$DOCKER_COMPOSE_FILE" "$user_home/.docker/cli-plugins/docker-compose"
    chmod +x "$user_home/.docker/cli-plugins/docker-compose"
    chown "$SUDO_USER:$SUDO_USER" "$user_home/.docker/cli-plugins/docker-compose"
  fi

  log_success "Docker Compose 插件安装完成"

  # 验证安装结果
  if docker compose version >/dev/null 2>&1; then
    local compose_version=$(docker compose version --short 2>/dev/null || echo "未知版本")
    log_success "Docker Compose 插件验证成功，版本: $compose_version"
  else
    log_error "Docker Compose 插件安装失败，请检查"
    exit 1
  fi
}

# 验证安装
verify_installation() {
  log_info "验证安装..."
  local verification_failed=false

  # 验证 Docker 二进制文件
  log_info "检查 Docker 二进制文件..."
  local binaries=($(get_docker_binaries))
  for binary in "${binaries[@]}"; do
    if command_exists "$binary"; then
      log_info "✓ $binary 可用"
    else
      log_error "✗ $binary 不可用"
      verification_failed=true
    fi
  done

  # 验证 Docker 服务状态
  log_info "检查 Docker 服务状态..."
  if service_is_active docker; then
    log_success "✓ Docker 服务运行中"
  else
    log_error "✗ Docker 服务未运行"
    verification_failed=true
  fi

  if service_is_active containerd; then
    log_success "✓ containerd 服务运行中"
  else
    log_error "✗ containerd 服务未运行"
    verification_failed=true
  fi

  # 验证 Docker 版本
  if is_docker_installed; then
    local docker_version=$(get_docker_version)
    log_success "✓ Docker 安装成功: $docker_version"
  else
    log_error "✗ Docker 安装失败"
    verification_failed=true
  fi

  # 验证 Docker Compose 插件
  log_info "检查 Docker Compose 插件..."
  local plugin_path="/usr/local/lib/docker/cli-plugins/docker-compose"
  local plugin_found=false

  if [[ -f "$plugin_path" && -x "$plugin_path" ]]; then
    log_success "✓ 找到 Docker Compose 插件: $plugin_path"
    plugin_found=true
  fi

  # 检查用户级插件（如果存在）
  if [[ -n "$SUDO_USER" ]]; then
    local user_home=$(eval echo "~$SUDO_USER")
    local user_plugin_path="$user_home/.docker/cli-plugins/docker-compose"
    if [[ -f "$user_plugin_path" && -x "$user_plugin_path" ]]; then
      log_success "✓ 找到用户级 Docker Compose 插件: $user_plugin_path"
      plugin_found=true
    fi
  fi

  if [[ "$plugin_found" == false ]]; then
    log_error "✗ 未找到 Docker Compose 插件"
    verification_failed=true
  fi

  # 验证 Docker Compose 功能
  if is_docker_compose_installed; then
    local compose_version=$(get_docker_compose_version)
    log_success "✓ Docker Compose 安装成功: $compose_version"
  else
    log_error "✗ Docker Compose 安装失败"
    verification_failed=true
  fi

  # 测试 Docker 运行状态
  log_info "测试 Docker 运行状态..."
  if docker info >/dev/null 2>&1; then
    log_success "✓ Docker 运行正常"

    # 显示 Docker 系统信息
    local docker_root_dir=$(docker info --format '{{.DockerRootDir}}' 2>/dev/null || echo "未知")
    local storage_driver=$(docker info --format '{{.Driver}}' 2>/dev/null || echo "未知")
    log_info "Docker 数据目录: $docker_root_dir"
    log_info "存储驱动: $storage_driver"
  else
    log_error "✗ Docker 运行异常"
    log_error "请检查 Docker 服务状态: systemctl status docker"
    verification_failed=true
  fi

  # 测试 Docker Compose 功能
  log_info "测试 Docker Compose 功能..."
  if docker compose version >/dev/null 2>&1; then
    log_success "✓ Docker Compose 功能正常"
  else
    log_error "✗ Docker Compose 功能异常"
    verification_failed=true
  fi

  if [[ "$verification_failed" == true ]]; then
    log_error "安装验证失败，存在问题需要解决"
    return 1
  else
    log_success "所有验证检查通过"
    return 0
  fi
}

# 显示安装后信息
show_post_install_info() {
  log_info "安装后信息:"
  show_separator "=" 50
  echo "Docker 数据目录: /data/docker"
  echo "Docker 配置文件: /etc/docker/daemon.json"
  echo "Docker 二进制文件: /usr/local/bin/docker*"
  echo "Docker Compose 插件: /usr/local/lib/docker/cli-plugins/docker-compose"
  echo ""
  echo "服务状态:"
  echo "  Docker: $(check_docker_service_status docker)"
  echo "  containerd: $(check_docker_service_status containerd)"
  echo ""
  echo "常用命令:"
  echo "  查看 Docker 版本: docker --version"
  echo "  查看 Docker 信息: docker info"
  echo "  查看 Docker Compose 版本: docker compose version"
  echo "  查看 Docker 服务状态: systemctl status docker"
  echo "  查看 containerd 服务状态: systemctl status containerd"
  echo ""
  echo "如需将用户添加到 docker 组以免使用 sudo:"
  echo "  usermod -aG docker \$USER"
  echo "  newgrp docker"
  show_separator "=" 50
}

# 主函数
main() {
  # 解析命令行参数
  parse_arguments "$@"

  show_title "Docker 离线安装开始"

  # 执行安装步骤
  check_root
  check_architecture
  check_system_requirements
  check_required_files

  # 如果不是强制模式，询问用户确认
  if [[ "$FORCE_MODE" != true ]]; then
    if ! confirm_operation "确定要开始安装 Docker 吗？" "y"; then
      log_info "用户取消安装"
      exit 0
    fi
  fi

  remove_existing_docker
  install_docker_binaries
  configure_systemd_services
  configure_docker
  start_docker_services
  install_docker_compose

  # 验证安装
  if verify_installation; then
    log_success "Docker 离线安装完成！"
    show_post_install_info
    show_title "Docker 离线安装完成"
    exit 0
  else
    log_error "安装验证失败，请检查错误信息"
    exit 1
  fi
}

# 执行主函数
main "$@"
