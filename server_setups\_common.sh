#!/bin/bash

#===============================================================================
# Docker 脚本共通函数库
#===============================================================================
# 功能描述: 为 Docker 安装和卸载脚本提供共通函数
# 适用系统: CentOS 7.x / RHEL 7.x
# 使用方法: source _common.sh
#===============================================================================

# 防止重复加载
if [[ -n "$DOCKER_COMMON_LOADED" ]]; then
  return 0
fi
DOCKER_COMMON_LOADED=1

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
  echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
  echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
  echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
  echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为 root 用户
check_root() {
  if [[ $EUID -ne 0 ]]; then
    log_error "此脚本需要 root 权限运行"
    exit 1
  fi
}

# 检查系统架构
check_architecture() {
  ARCH=$(uname -m)
  log_info "检测到系统架构: $ARCH"

  if [[ "$ARCH" != "x86_64" ]]; then
    log_warning "当前脚本主要针对 x86_64 架构，其他架构可能需要调整"
  fi
}

# 获取脚本目录
get_script_dir() {
  echo "$(cd "$(dirname "${BASH_SOURCE[1]}")" && pwd)"
}

# Docker 相关二进制文件列表
get_docker_binaries() {
  echo "docker dockerd docker-proxy docker-init ctr runc containerd containerd-shim-runc-v2"
}

# 检查服务是否存在
service_exists() {
  local service_name="$1"
  systemctl list-unit-files | grep -q "^${service_name}.service"
}

# 检查服务是否运行
service_is_active() {
  local service_name="$1"
  systemctl is-active --quiet "$service_name"
}

# 检查服务是否启用
service_is_enabled() {
  local service_name="$1"
  systemctl is-enabled --quiet "$service_name" 2>/dev/null
}

# 安全停止服务
safe_stop_service() {
  local service_name="$1"
  if service_is_active "$service_name"; then
    log_info "停止 $service_name 服务..."
    systemctl stop "$service_name" || true

    # 等待服务完全停止
    local timeout=30
    local count=0
    while service_is_active "$service_name" && [[ $count -lt $timeout ]]; do
      sleep 1
      ((count++))
    done

    if service_is_active "$service_name"; then
      log_warning "$service_name 服务停止超时，强制停止"
      systemctl kill "$service_name" || true
      sleep 2
    fi

    log_success "$service_name 服务已停止"
  else
    log_info "$service_name 服务未运行"
  fi
}

# 安全禁用服务
safe_disable_service() {
  local service_name="$1"
  if service_exists "$service_name"; then
    if service_is_enabled "$service_name"; then
      log_info "禁用 $service_name 服务..."
      systemctl disable "$service_name" 2>/dev/null || true
      log_success "$service_name 服务已禁用"
    else
      log_info "$service_name 服务未启用"
    fi
  else
    log_info "$service_name 服务不存在"
  fi
}

# 检查命令是否存在
command_exists() {
  local cmd="$1"
  command -v "$cmd" >/dev/null 2>&1
}

# 检查文件是否存在
file_exists() {
  local file="$1"
  [[ -f "$file" ]]
}

# 检查目录是否存在
dir_exists() {
  local dir="$1"
  [[ -d "$dir" ]]
}

# 安全删除文件
safe_remove_file() {
  local file="$1"
  if file_exists "$file"; then
    rm -f "$file"
    log_info "删除文件: $file"
    return 0
  else
    return 1
  fi
}

# 安全删除目录
safe_remove_dir() {
  local dir="$1"
  if dir_exists "$dir"; then
    rm -rf "$dir"
    log_info "删除目录: $dir"
    return 0
  else
    return 1
  fi
}

# 安全创建目录
safe_create_dir() {
  local dir="$1"
  if ! dir_exists "$dir"; then
    mkdir -p "$dir"
    log_info "创建目录: $dir"
  fi
}

# 安全复制文件
safe_copy_file() {
  local src="$1"
  local dest="$2"

  if ! file_exists "$src"; then
    log_error "源文件不存在: $src"
    return 1
  fi

  cp "$src" "$dest"
  log_info "复制文件: $src -> $dest"
  return 0
}

# 安全创建软链接
safe_create_symlink() {
  local target="$1"
  local link="$2"

  if ! file_exists "$target"; then
    log_warning "目标文件不存在: $target"
    return 1
  fi

  # 如果链接已存在，先删除
  if [[ -L "$link" ]] || [[ -f "$link" ]]; then
    rm -f "$link"
  fi

  ln -sf "$target" "$link"
  log_info "创建软链接: $link -> $target"
  return 0
}

# 设置文件执行权限
set_executable() {
  local file="$1"
  if file_exists "$file"; then
    chmod +x "$file"
    log_info "设置执行权限: $file"
    return 0
  else
    log_warning "文件不存在，无法设置权限: $file"
    return 1
  fi
}

# 检查 Docker 是否安装
is_docker_installed() {
  command_exists docker
}

# 检查 Docker Compose 是否安装
is_docker_compose_installed() {
  # 只检查 Docker 内置的 compose 功能
  command_exists docker && docker compose version >/dev/null 2>&1
}

# 获取 Docker 版本
get_docker_version() {
  if is_docker_installed; then
    docker --version 2>/dev/null || echo "获取版本失败"
  else
    echo "Docker 未安装"
  fi
}

# 获取 Docker Compose 版本
get_docker_compose_version() {
  if is_docker_compose_installed; then
    docker compose version 2>/dev/null || echo "获取版本失败"
  else
    echo "Docker Compose 未安装"
  fi
}

# 检查 Docker 服务状态
check_docker_service_status() {
  local service="$1"
  if service_exists "$service"; then
    if service_is_active "$service"; then
      echo "运行中"
    elif service_is_enabled "$service"; then
      echo "已启用但未运行"
    else
      echo "已停止"
    fi
  else
    echo "不存在"
  fi
}

# 重新加载 systemd
reload_systemd() {
  log_info "重新加载 systemd..."
  systemctl daemon-reload
  log_success "systemd 重新加载完成"
}

# 等待服务启动
wait_for_service() {
  local service_name="$1"
  local timeout="${2:-30}"
  local count=0

  log_info "等待 $service_name 服务启动..."

  while ! service_is_active "$service_name" && [[ $count -lt $timeout ]]; do
    sleep 1
    ((count++))
  done

  if service_is_active "$service_name"; then
    log_success "$service_name 服务启动成功"
    return 0
  else
    log_error "$service_name 服务启动超时"
    return 1
  fi
}

# 显示分隔线
show_separator() {
  local char="${1:-=}"
  local length="${2:-70}"
  printf "%*s\n" "$length" | tr ' ' "$char"
}

# 显示标题
show_title() {
  local title="$1"
  local char="${2:-=}"
  local length="${3:-70}"

  show_separator "$char" "$length"
  printf "%*s\n" $(((${#title} + $length) / 2)) "$title"
  show_separator "$char" "$length"
}

# 确认操作
confirm_operation() {
  local message="$1"
  local default="${2:-n}"

  if [[ "$default" == "y" ]]; then
    read -p "$message (Y/n): " confirm
    confirm=${confirm:-y}
  else
    read -p "$message (y/N): " confirm
    confirm=${confirm:-n}
  fi

  case "$confirm" in
    [Yy]|[Yy][Ee][Ss])
      return 0
      ;;
    *)
      return 1
      ;;
  esac
}

# 检查磁盘空间
check_disk_space() {
  local path="${1:-/}"
  local required_mb="${2:-1024}"  # 默认需要 1GB

  local available_mb=$(df -m "$path" | awk 'NR==2 {print $4}')

  if [[ $available_mb -lt $required_mb ]]; then
    log_warning "磁盘空间不足: 需要 ${required_mb}MB，可用 ${available_mb}MB"
    return 1
  else
    log_info "磁盘空间检查通过: 可用 ${available_mb}MB"
    return 0
  fi
}

# 检查内存
check_memory() {
  local required_mb="${1:-512}"  # 默认需要 512MB

  local available_mb=$(free -m | awk 'NR==2{print $7}')

  if [[ $available_mb -lt $required_mb ]]; then
    log_warning "可用内存不足: 需要 ${required_mb}MB，可用 ${available_mb}MB"
    return 1
  else
    log_info "内存检查通过: 可用 ${available_mb}MB"
    return 0
  fi
}

# 检查系统要求
check_system_requirements() {
  log_info "检查系统要求..."

  local requirements_met=true

  # 检查磁盘空间
  if ! check_disk_space "/" 2048; then
    requirements_met=false
  fi

  # 检查内存
  if ! check_memory 512; then
    requirements_met=false
  fi

  # 检查内核版本
  local kernel_version=$(uname -r | cut -d. -f1-2)
  local major=$(echo "$kernel_version" | cut -d. -f1)
  local minor=$(echo "$kernel_version" | cut -d. -f2)

  if [[ $major -lt 3 ]] || [[ $major -eq 3 && $minor -lt 10 ]]; then
    log_warning "内核版本较低: $kernel_version，建议升级到 3.10 或更高版本"
    requirements_met=false
  else
    log_info "内核版本检查通过: $kernel_version"
  fi

  if [[ "$requirements_met" == true ]]; then
    log_success "系统要求检查通过"
    return 0
  else
    log_warning "系统要求检查未完全通过，可能影响 Docker 运行"
    return 1
  fi
}

# 显示脚本使用帮助
show_help() {
  local script_name="$1"
  echo "使用方法: $script_name [选项]"
  echo ""
  echo "选项:"
  echo "  -h, --help     显示此帮助信息"
  echo "  -v, --version  显示版本信息"
  echo "  -q, --quiet    静默模式"
  echo "  -f, --force    强制执行（跳过确认）"
  echo ""
}

# 显示版本信息
show_version() {
  echo "Docker 离线管理脚本 v1.0.0"
  echo "适用于 CentOS 7.x / RHEL 7.x"
}

# 解析命令行参数
parse_arguments() {
  QUIET_MODE=false
  FORCE_MODE=false

  while [[ $# -gt 0 ]]; do
    case $1 in
      -h|--help)
        show_help "$(basename "${BASH_SOURCE[1]}")"
        exit 0
        ;;
      -v|--version)
        show_version
        exit 0
        ;;
      -q|--quiet)
        QUIET_MODE=true
        shift
        ;;
      -f|--force)
        FORCE_MODE=true
        shift
        ;;
      *)
        log_error "未知参数: $1"
        show_help "$(basename "${BASH_SOURCE[1]}")"
        exit 1
        ;;
    esac
  done
}

# 静默模式日志函数
quiet_log_info() {
  [[ "$QUIET_MODE" != true ]] && log_info "$1"
}

quiet_log_success() {
  [[ "$QUIET_MODE" != true ]] && log_success "$1"
}

quiet_log_warning() {
  [[ "$QUIET_MODE" != true ]] && log_warning "$1"
}

# 错误日志始终显示
quiet_log_error() {
  log_error "$1"
}

# 安全创建用户组
safe_create_group() {
  local group_name="$1"
  if ! getent group "$group_name" >/dev/null 2>&1; then
    log_info "创建用户组: $group_name"
    groupadd "$group_name"
    log_success "用户组 $group_name 创建完成"
    return 0
  else
    log_info "用户组 $group_name 已存在"
    return 0
  fi
}

log_info "Docker 共通函数库已加载"
