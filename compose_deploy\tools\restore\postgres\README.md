# Postgres 数据库恢复

以下命令均默认在本目录（`restore/postgres`）中执行，默认备份目录为 `~/backups/2020-09-25/`

1. 删除并重新创建 `base-postgres` 命名卷

```sh
docker volume rm base-postgres & docker volume create --name=base-postgres
```

2. 创建初始化 SQL 硬连接

```sh
ln ../../../services/base/config/postgres_init.sql ./db/
```

3. 拷贝通过 `pg_dump` 备份的 SQL 文件到 `./db/` 目录中，并确认目录中只有 `postgres_init.sql` 和备份 SQL 两个文件。

```sh
gunzip -c ~/backups/2020-09-25/pg.16.26.58.gz > ./db/restore.sql
```

4. 通过 `docker-compose` 启动服务

```sh
docker compose up
```

5. 查看容器日志，初始化成功即为备份恢复完成，之后停止服务

```sh
docker compose down
```

6. 删除 `./db/` 目录中的备份文件

```sh
rm ./db/*.sql
```
