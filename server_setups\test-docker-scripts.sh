#!/bin/bash

#===============================================================================
# Docker 脚本测试工具
#===============================================================================
# 功能描述: 测试 Docker 安装和卸载脚本的基本功能
# 适用系统: CentOS 7.x / RHEL 7.x
# 执行权限: 需要 root 权限
# 依赖文件: 
#   - _common.sh (共通函数库)
#   - docker-install-offline.sh (安装脚本)
#   - docker-uninstall-offline.sh (卸载脚本)
#===============================================================================

set -e  # 遇到错误立即退出

# 加载共通函数库
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
if [[ -f "$SCRIPT_DIR/_common.sh" ]]; then
    source "$SCRIPT_DIR/_common.sh"
else
    echo "错误: 找不到共通函数库 _common.sh"
    exit 1
fi

# 测试结果统计
TESTS_TOTAL=0
TESTS_PASSED=0
TESTS_FAILED=0

# 测试函数
run_test() {
    local test_name="$1"
    local test_command="$2"
    
    ((TESTS_TOTAL++))
    
    log_info "运行测试: $test_name"
    
    if eval "$test_command"; then
        log_success "✓ $test_name"
        ((TESTS_PASSED++))
        return 0
    else
        log_error "✗ $test_name"
        ((TESTS_FAILED++))
        return 1
    fi
}

# 测试共通函数库
test_common_functions() {
    show_title "测试共通函数库"
    
    # 测试日志函数
    run_test "日志函数测试" "log_info 'Test message' && log_success 'Success' && log_warning 'Warning' && log_error 'Error'"
    
    # 测试文件操作函数
    run_test "文件存在检查" "file_exists '$SCRIPT_DIR/_common.sh'"
    run_test "目录存在检查" "dir_exists '$SCRIPT_DIR'"
    
    # 测试命令检查函数
    run_test "命令存在检查" "command_exists 'bash'"
    
    # 测试架构检查
    run_test "系统架构检查" "check_architecture"
    
    # 测试系统要求检查
    run_test "系统要求检查" "check_system_requirements"
}

# 测试脚本文件完整性
test_script_integrity() {
    show_title "测试脚本文件完整性"
    
    local scripts=(
        "_common.sh"
        "docker-install-offline.sh"
        "docker-uninstall-offline.sh"
    )
    
    for script in "${scripts[@]}"; do
        run_test "检查脚本文件: $script" "file_exists '$SCRIPT_DIR/$script'"
        run_test "检查脚本权限: $script" "[[ -x '$SCRIPT_DIR/$script' ]]"
    done
}

# 测试必需文件检查
test_required_files() {
    show_title "测试必需文件检查"
    
    local docker_dir="$SCRIPT_DIR/docker"
    
    # 检查目录结构
    run_test "检查 docker 目录" "dir_exists '$docker_dir'"
    run_test "检查 setups 目录" "dir_exists '$docker_dir/setups'"
    run_test "检查 x86_64 目录" "dir_exists '$docker_dir/setups/x86_64'"
    run_test "检查 systemd 目录" "dir_exists '$docker_dir/systemd'"
    
    # 检查 systemd 服务文件
    local systemd_files=(
        "docker.service"
        "containerd.service"
        "docker.socket"
    )
    
    for file in "${systemd_files[@]}"; do
        run_test "检查服务文件: $file" "file_exists '$docker_dir/systemd/$file'"
    done
    
    # 检查二进制文件（可能存在）
    local binary_files=("$docker_dir/setups/x86_64"/docker-*.tar)
    if [[ -f "${binary_files[0]}" ]]; then
        run_test "检查 Docker 二进制包" "file_exists '${binary_files[0]}'"
    else
        log_warning "未找到 Docker 二进制包文件，跳过测试"
    fi
    
    local compose_files=("$docker_dir/setups/x86_64"/docker-compose-*)
    if [[ -f "${compose_files[0]}" ]]; then
        run_test "检查 Docker Compose 二进制文件" "file_exists '${compose_files[0]}'"
    else
        log_warning "未找到 Docker Compose 二进制文件，跳过测试"
    fi
}

# 测试脚本语法
test_script_syntax() {
    show_title "测试脚本语法"
    
    local scripts=(
        "_common.sh"
        "docker-install-offline.sh"
        "docker-uninstall-offline.sh"
    )
    
    for script in "${scripts[@]}"; do
        run_test "语法检查: $script" "bash -n '$SCRIPT_DIR/$script'"
    done
}

# 测试脚本帮助信息
test_script_help() {
    show_title "测试脚本帮助信息"
    
    local scripts=(
        "docker-install-offline.sh"
        "docker-uninstall-offline.sh"
    )
    
    for script in "${scripts[@]}"; do
        run_test "帮助信息: $script" "'$SCRIPT_DIR/$script' --help >/dev/null 2>&1"
        run_test "版本信息: $script" "'$SCRIPT_DIR/$script' --version >/dev/null 2>&1"
    done
}

# 测试 Docker 状态检查
test_docker_status() {
    show_title "测试 Docker 状态检查"
    
    # 测试 Docker 安装检查
    if is_docker_installed; then
        run_test "Docker 已安装检查" "is_docker_installed"
        run_test "Docker 版本获取" "get_docker_version | grep -q 'Docker'"
    else
        log_info "Docker 未安装，跳过相关测试"
    fi
    
    # 测试 Docker Compose 安装检查
    if is_docker_compose_installed; then
        run_test "Docker Compose 已安装检查" "is_docker_compose_installed"
        run_test "Docker Compose 版本获取" "get_docker_compose_version | grep -q 'docker-compose'"
    else
        log_info "Docker Compose 未安装，跳过相关测试"
    fi
    
    # 测试服务状态检查
    run_test "Docker 服务状态检查" "check_docker_service_status docker | grep -E '运行中|已停止|不存在'"
    run_test "containerd 服务状态检查" "check_docker_service_status containerd | grep -E '运行中|已停止|不存在'"
}

# 模拟安装测试（不实际安装）
test_install_simulation() {
    show_title "模拟安装测试"
    
    # 测试 root 权限检查（如果不是 root 用户会失败，这是预期的）
    if [[ $EUID -eq 0 ]]; then
        run_test "Root 权限检查" "check_root"
    else
        log_warning "非 root 用户，跳过权限检查测试"
    fi
    
    # 测试架构检查
    run_test "架构检查" "check_architecture"
    
    # 测试系统要求检查
    run_test "系统要求检查" "check_system_requirements"
}

# 显示测试结果
show_test_results() {
    show_title "测试结果汇总"
    
    echo "总测试数: $TESTS_TOTAL"
    echo "通过测试: $TESTS_PASSED"
    echo "失败测试: $TESTS_FAILED"
    echo "成功率: $(( TESTS_PASSED * 100 / TESTS_TOTAL ))%"
    
    if [[ $TESTS_FAILED -eq 0 ]]; then
        log_success "所有测试通过！"
        return 0
    else
        log_error "有 $TESTS_FAILED 个测试失败"
        return 1
    fi
}

# 显示使用帮助
show_usage() {
    echo "使用方法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help     显示此帮助信息"
    echo "  -a, --all      运行所有测试（默认）"
    echo "  -c, --common   仅测试共通函数库"
    echo "  -f, --files    仅测试文件完整性"
    echo "  -s, --syntax   仅测试脚本语法"
    echo "  -d, --docker   仅测试 Docker 状态"
    echo ""
}

# 主函数
main() {
    local test_type="all"
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_usage
                exit 0
                ;;
            -a|--all)
                test_type="all"
                shift
                ;;
            -c|--common)
                test_type="common"
                shift
                ;;
            -f|--files)
                test_type="files"
                shift
                ;;
            -s|--syntax)
                test_type="syntax"
                shift
                ;;
            -d|--docker)
                test_type="docker"
                shift
                ;;
            *)
                log_error "未知参数: $1"
                show_usage
                exit 1
                ;;
        esac
    done
    
    show_title "Docker 脚本测试开始"
    
    # 根据参数运行相应测试
    case $test_type in
        "all")
            test_common_functions
            test_script_integrity
            test_required_files
            test_script_syntax
            test_script_help
            test_docker_status
            test_install_simulation
            ;;
        "common")
            test_common_functions
            ;;
        "files")
            test_script_integrity
            test_required_files
            ;;
        "syntax")
            test_script_syntax
            ;;
        "docker")
            test_docker_status
            ;;
    esac
    
    # 显示测试结果
    if show_test_results; then
        show_title "测试完成 - 成功"
        exit 0
    else
        show_title "测试完成 - 有失败"
        exit 1
    fi
}

# 执行主函数
main "$@"