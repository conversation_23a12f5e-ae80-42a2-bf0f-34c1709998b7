## compose_deploy

根据 shell 脚本格式化最佳实践、同时参照 compose_deploy.sh 进行格式化；对于缺失注释的函数或者重要逻辑，同时进行补充。

根据 shell 脚本格式化最佳实践进行格式化；对于缺失注释的函数或者重要逻辑，同时进行补充。

根据 shell 脚本格式化最佳实践进行格式化，不用做改写和扩展。缩进大小使用 2。

根据 Nginx 配置格式化最佳实践进行格式化，同时根据业务相关性、依赖关系等进行配置顺序调整。

根据 YAML 文件格式化最佳实践进行格式化，同时根据业务相关性、依赖关系等进行配置顺序调整。请注意：现已注释的内容也要保留和调整，一定和未注释配置一样调整。

请根据业务相关性、依赖关系等进行配置顺序调整。

每次执行检查任务都会有新的内容生成，这不是我想要的，不需要一味地扩展需求。要求只要对脚本和工程有足够的说明即可。另外请遵循以下原则：
1）关于整个部署工程的内容主要放在根目录的 README.md 里。
2）子目录，比如服务目录里只要对该目录里的内容、功能做大致说明即可，即针对实际内容做合理总结，不需要对部署对象的功能做猜测和补充，这很可能是错误的。
3）比如目录里的服务台账、列表，请用表格的形式进行展示，不要用标题+分段的形式。
4）生成的文档内容里，标题后面记得总是增加一个空行；
基于以上内容，请再次检查。

基于 https://docs.docker.com/compose/install/linux/ 中 Install the plugin manually 一节的内容更新该文件中 docker compose 插件的安装方式。同时更新优化该脚本的逻辑，确保重新执行安装不会出错。

我调用 `./compose_deploy.sh deploy -i` 遇到下述错误：
 ✘ postgres Error Get "https://registry-1.docker.io/v2/": dial tcp: lookup registry-1.docker.io on [::1]:53: read udp [::1]:35434->[::1]:53: read: connection refused                       0.0s
Error response from daemon: Get "https://registry-1.docker.io/v2/": dial tcp: lookup registry-1.docker.io on [::1]:53: read udp [::1]:35434->[::1]:53: read: connection refused
所以想添加一个离线模式的参数（脚本中医已添加），用来控制一些逻辑上不要主动进行联网操作。
请注意，只要明确需要联网的操作才需要进行离线模式判断，比如 harbor 登录。像 `docker_compose_cmd up -d tdengine` 的操作就不需要，因为如果本地存在相关镜像就不需要联网。
请用你的理解说一下我的需求，在我确认一直后再进行下一步。

```bash
cd /home/<USER>/ && (rm -rf server_setups.zip || true)
unzip server_setups.zip && (rm -rf __MACOSX || true) && rm -rf ../server_setups && mv server_setups ../
cd ../server_setups && chmod +x *.sh

cd /home/<USER>/ && (rm -rf compose_deploy.zip || true)
unzip compose_deploy.zip && rm -rf __MACOSX && rm -rf ../compose_deploy && mv compose_deploy ../
cd ../compose_deploy && chmod +x *.sh && chmod +x libs/*.sh && chmod +x scripts/*.sh && chmod +x tools/*.sh

docker stop vastbase_g100_test && docker rm vastbase_g100_test

docker run -d --privileged=true -v /home/<USER>/compose_deploy/services/base/config/openGauss_license:/home/<USER>/vastbase/lic -v /home/<USER>/vatbase_test/data:/home/<USER>/data  -v /home/<USER>/vatbase_test/backup:/home/<USER>/backup -v /home/<USER>/vatbase_test/backup-log:/home/<USER>/backup_log  -e VB_PASSWORD=Vastbase@bdtd123 -e VB_USERNAME=postgres -e VB_DBCOMPATIBILITY=PG --name vastbase_g100_test -p 25432:5432 vastbase-g100-single-v1.4-3.0.8.24875-linux-x86_64:20250123144939

docker run -d --privileged=true -v D:\AppData\Docker\container.d\Vastbase\openGauss_license:/home/<USER>/vastbase/lic -v D:\AppData\Docker\container.d\Vastbase\data:/home/<USER>/data  -v D:\AppData\Docker\container.d\Vastbase\backup:/home/<USER>/backup -v D:\AppData\Docker\container.d\Vastbase\backup-log:/home/<USER>/backup_log  -e VB_PASSWORD=Vastbase@bdtd123 -e VB_USERNAME=postgres -e VB_DBCOMPATIBILITY=PG --name vastbase_g100_test -p 25432:5432 vastbase-g100-single-v1.4-3.0.8.24875-linux-x86_64:20250123144939

docker logs -f -n 500 vastbase_g100_test

docker exec -i base_postgres psql postgresql://postgres:Q5%5eRP%25ipEPWotT@localhost:5432/postgres?options=--search_path%3dpublic -c "SELECT 1;"

(docker stop base_postgres || true) && (docker rm base_postgres || true) && docker volume rm --force base-opengauss base-opengauss-backup base-opengauss-backup-log

(docker stop base_postgres-permission-fix || true) && (docker rm base_postgres-permission-fix || true) && (docker stop base_postgres || true) && (docker rm base_postgres || true) && docker volume rm --force base-opengauss base-opengauss-backup base-opengauss-backup-log

docker logs -f -n 500 base_postgres
```


## swarm-deploy



## simple_deploy

