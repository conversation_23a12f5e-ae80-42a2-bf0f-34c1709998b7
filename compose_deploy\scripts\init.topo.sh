#!/bin/bash

###
# TOPO 模型初始化脚本
# 功能:
# 1. 下载最新topo模型图片库
# 2. 导入模型图片到Docker卷
# 3. 执行模型SQL初始化
# 注意事项:
# - 需要提前配置好 timescale-monitor-image Docker 卷
# - 需要 git 客户端和 docker 环境
###

# 错误设置
set -eo pipefail  # 遇到错误立即退出，检查管道命令错误

# 加载工具库
source "$(dirname "$0")/../../libs/utils.sh"

# 获取MOS服务路径（从环境变量或默认配置）
IOT_SERVICE_PATH="${:-../../services/iot}"

# 进入 MOS 服务目录
cd "$IOT_SERVICE_PATH" || {
  echo_error "无法进入 MOS 服务目录: $IOT_SERVICE_PATH"
  exit 1
}

# 检查 timescale-monitor 服务状态
monitor_count=$(docker ps --filter "name=timescale-monitor" --format "{{.ID}}" | wc -l)

# 如果 timescale-monitor 正在运行则先停止
if [ "$monitor_count" -eq 1 ]; then
  echo_yellow "▌停止 timescale-monitor 服务..."
  docker_compose_down "$IOT_SERVICE_PATH"
fi

# 配置 topo 模型仓库地址
TOPO_REPO="${TOPO_GIT_REPO:-https://gitee.com/bjbigdata/topo-model.git}"

# 克隆模型仓库
echo_yellow "▌Topo模型初始化: 正在从仓库克隆代码..."
echo "仓库地址: $TOPO_REPO"
if ! git clone "$TOPO_REPO" my_topo_model; then
  echo_error "无法从git仓库克隆topo模型库，请检查:"
  echo "1. 网络连接是否正常"
  echo "2. Git凭证是否正确配置"
  echo "3. 仓库地址权限: $TOPO_REPO"
  exit 1
fi

# 进入临时克隆目录
pushd my_topo_model >/dev/null

# 导入模型图片到 Docker 卷
echo_yellow "▌正在导入模型图片..."
target_volume="timescale-monitor-image"
target_dir=$(docker volume inspect --format '{{ .Mountpoint }}' "$target_volume")
if [ ! -d "$target_dir" ]; then
  echo_error "Docker卷挂载点不存在: $target_volume"
  exit 1
fi

# 复制图片并保留权限
echo "源目录: $(pwd)/images"
echo "目标卷: $target_dir"
cp -av images/* "$target_dir/"

# 执行SQL初始化
echo_yellow "▌开始导入模型SQL..."

# 查找所有 SQL 文件并排序
find . -type f -name "*.sql" -print0 | sort -z | while IFS= read -r -d '' fname; do
  echo_yellow "正在处理: $fname"
  psqlExecSqlFile "$psql" "$fname"
done

# 清理临时目录
popd >/dev/null
rm -rf my_topo_model

# 如果之前有运行数据接口服务则重新启动
if [ "$monitor_count" -eq 1 ]; then
  echo_yellow "▌重新启动 timescale-monitor 服务..."
  docker_compose_up "$IOT_SERVICE_PATH"
fi

echo_green "Topo模型初始化完成!"
