# 1）综合信息平台 - Docker Swarm 服务目录

## 📋 完整服务清单

基于您的 compose-deploy 目录分析，以下是所有可部署的微服务，请您选择需要部署的服务：

### 🔧 基础设施服务 (infrastructure-stack) - **必需**

| 服务ID | 服务名称 | 容器名称 | 镜像 | 功能描述 | 资源需求 |
|--------|----------|----------|------|----------|----------|
| **INF-01** | PostgreSQL 主数据库 | base_postgres | postgis:${PG_VERSION} | GIS、IoT 等业务数据存储 | 2C/4G/50GB |
| **INF-02** | PostgreSQL 业务数据库 | base_business_postgres | postgis:${PG_VERSION} | MES、工作流等业务数据存储 | 2C/4G/50GB |
| **INF-03** | Redis 缓存 | base_redis | redis:7.4.3 | 数据缓存和会话存储 | 1C/2G/10GB |
| **INF-04** | RabbitMQ 消息队列 | base_rabbitmq | rabbitmq:${RABBITMQ_VERSION} | 服务间异步通信 | 1C/2G/10GB |
| **INF-05** | TDengine 时序数据库 | tdengine | tdengine:3.1.0.2 | IoT 传感器数据存储 | 2C/4G/100GB |
| **INF-06** | MinIO 对象存储 | bladex-minio | minio:${MINIO_VERSION} | 文件和图片存储 | 1C/2G/200GB |
| **INF-07** | Node-RED 数据网关 | base_rednode | node-gw:${NODEGW_VERSION} | 数据接入和转换 | 1C/1G/5GB |
| **INF-08** | FTP 服务器 | ftpserver | fauria/vsftpd | 文件传输服务 | 0.5C/1G/5GB |

**基础设施总计**: 10.5C/20G 内存, 430GB 存储

---

### 🏢 核心业务服务 (core-stack) - **必需**

| 服务ID | 服务名称 | 容器名称 | 镜像 | 功能描述 | 资源需求 |
|--------|----------|----------|------|----------|----------|
| **CORE-01** | 授权认证服务 | service-authorization | auth_client:${AUTH_CLIENT_VERSION} | 用户认证和授权 | 1C/2G |
| **CORE-02** | 用户权限管理 | backend-platform | bladex_boot:${BACKEND_BLADEX_VERSION} | BladeX 用户权限管理 | 2C/4G |
| **CORE-03** | 消息推送服务 | backend-push-msg | push-msg:${BACKEND_PUSH_MSG} | 告警消息推送 | 1C/2G |
| **CORE-04** | Nacos 配置中心 | nacos-standalone-pg | nacos-pg:v2.1.0 | 服务发现和配置管理 | 1C/2G |
| **CORE-05** | 前端基座模块 | frontend-module-base | base-frontend:${FRONTEND_BASE_VERSION} | 核心前端框架 | 0.5C/1G |
| **CORE-06** | 系统管理模块 | frontend-module-system_admin | base-system-admin:${FRONTEND_SYSADMIN_VERSION} | 系统管理界面 | 0.5C/1G |
| **CORE-07** | BladeX Saber 前端 | frontend-saber | bladex_saber:${FRONTEND_BLADEX_VERSION} | BladeX 前端框架 | 0.5C/1G |
| **CORE-08** | 告警服务模块 | frontend-module-alarm_serve | alarm_serve:${FRONTEND_ALARM_VERSION} | 告警服务前端 | 0.5C/1G |
| **CORE-09** | 前端网关 | frontend-nginx-gateway | nginx:1.24.0 | 统一入口和路由 | 0.5C/1G |

**核心服务总计**: 7.5C/15G 内存

---

### 🌐 IoT 平台服务 (iot-stack) - **可选**

| 服务ID | 服务名称 | 容器名称 | 镜像 | 功能描述 | 资源需求 |
|--------|----------|----------|------|----------|----------|
| **IOT-01** | 数据接入服务 | backend-data-storage | dataroute:${BACKEND_ROUTE_VERSION} | IoT 数据流入口点 | 5C/5G |
| **IOT-02** | 数据解析服务 | backend-converter-monitor | parse-data:${BACKEND_PARSE_VERSION} | 原始数据转换标准化 | 2C/4G |
| **IOT-03** | 数据预警服务 | backend-data-warn | data_warn:${BACKEND_WARN_VERSION} | 异常检测和告警 | 1C/2G |
| **IOT-04** | 数据监控服务 | backend-data-monitor | timescale-monitor:${BACKEND_TSMONITOR_VERSION} | 时序监控和数据查询 | 2C/4G |
| **IOT-05** | 联网系统前端 | frontend-biz-data_ims | biz-data-ims:${FRONTEND_DATA_IMS_VERSION} | 数据管理界面 | 0.5C/1G |
| **IOT-06** | 数据接入管理前端 | frontend-module-data_access | base-data-access:${FRONTEND_DATA_ACCESS_VERSION} | 数据接入配置界面 | 0.5C/1G |
| **IOT-07** | 数据查看前端 | frontend-module-data_view | base-data-view:${FRONTEND_DATA_VIEW_VERSION} | 数据查看界面 | 0.5C/1G |
| **IOT-08** | 页面编辑器 | frontend-module-page_builder | base-page-editor:${FRONTEND_PAGE_EDITOR_VERSION} | 可视化页面编辑 | 0.5C/1G |
| **IOT-09** | 页面渲染器 | frontend-module-page_render | base-page-editor:render-${FRONTEND_PAGE_EDITOR_VERSION} | 页面渲染展示 | 0.5C/1G |

**IoT 服务总计**: 12C/20G 内存

---

### 🗺️ GIS 地理信息服务 (gis-stack) - **可选**

| 服务ID | 服务名称 | 容器名称 | 镜像 | 功能描述 | 资源需求 |
|--------|----------|----------|------|----------|----------|
| **GIS-01** | GIS 业务后端 | backend-gis-biz | gis_biz:${BACKEND_GIS_VERSION} | 地理信息处理和分析 | 2C/4G |
| **GIS-02** | GIS 前端模块 | frontend-module-gis | base-gis:${FRONTEND_GIS_VERSION} | 地图展示和交互界面 | 0.5C/1G |

**GIS 服务总计**: 2.5C/5G 内存

---

### 🏭 MES 业务前端服务 (mes-stack) - **可选**

| 服务ID | 服务名称 | 容器名称 | 镜像 | 功能描述 | 资源需求 |
|--------|----------|----------|------|----------|----------|
| **MES-01** | MES 业务后端 | mes-business-service | mes_biz:${BACKEND_MES_VERSION} | 生产管理和工艺控制 | 2C/4G |
| **MES-02** | MES 前端模块 | mes-frontend-service | base-business-serve:${FRONTEND_MES_VERSION} | 业务前端系统界面 | 0.5C/1G |

**MES 服务总计**: 2.5C/5G 内存

---

### 📋 工作流服务 (workflow-stack) - **可选**

| 服务ID | 服务名称 | 容器名称 | 镜像 | 功能描述 | 资源需求 |
|--------|----------|----------|------|----------|----------|
| **WF-01** | 工作流后端引擎 | workflow-backend-service | bdtd-workflow-back:${BACKEND_WF_VERSION} | 流程定义和执行 | 2C/4G |
| **WF-02** | 工作流前端设计器 | workflow-frontend-service | bdtd-workflow-front:${FRONTEND_WF_VERSION} | 流程设计和管理界面 | 0.5C/1G |

**工作流服务总计**: 2.5C/5G 内存

---

### 📱 移动应用服务 (app-stack) - **可选**

| 服务ID | 服务名称 | 容器名称 | 镜像 | 功能描述 | 资源需求 |
|--------|----------|----------|------|----------|----------|
| **APP-01** | 移动应用后端 | backend-bd-app | app-backend:${BACKEND_APP_VERSION} | APP 接口服务 | 1C/2G |
| **APP-02** | 移动应用前端 | frontend-bd-app | bd-app:${FRONTEND_APP_VERSION} | H5 移动端界面 | 0.5C/1G |

**移动应用服务总计**: 1.5C/3G 内存

---

### 📹 视频监控服务 (media-stack) - **可选**

| 服务ID | 服务名称 | 容器名称 | 镜像 | 功能描述 | 资源需求 |
|--------|----------|----------|------|----------|----------|
| **VID-01** | 视频专用 Redis | video-redis | redis:4.0.11 | 视频流信息缓存 | 1C/2G/10GB |
| **VID-02** | SRS 流媒体服务器 | video-srs | srs:6.0.10 | 视频流接收和分发 | 2C/4G |
| **VID-03** | 视频协议转换 | videojs-flow | videojs-flow:1.1.0 | HTTP -> WebSocket 转换 | 1C/2G |
| **VID-04** | Celery 任务处理 | video-celery | video-server:${VERSION_BACKEND} | 异步视频处理任务 | 2C/4G |
| **VID-05** | 视频预览服务 | video-preview | video_preview:${VERSION_BACKENDS} | 视频截图和预览图生成 | 1C/2G |
| **VID-06** | 视频管理后端 | video-server | video-server:${VERSION_BACKEND} | 摄像头管理和录像 | 2C/4G |
| **VID-07** | 视频前端界面 | video-frontend | video-front:${VERSION_FRONTEND} | 监控展示和控制 | 0.5C/1G |

**视频服务总计**: 9.5C/19G 内存, 10GB 存储

---

### 📊 监控运维服务 (monitoring-stack) - **可选**

| 服务ID | 服务名称 | 容器名称 | 镜像 | 功能描述 | 资源需求 |
|--------|----------|----------|------|----------|----------|
| **MON-01** | Prometheus | prometheus | prometheus:1.0 | 指标收集和存储 | 2C/4G/50GB |
| **MON-02** | Node Exporter | node-exporter | node-exporter:1.0 | 主机监控数据采集 | 0.5C/1G |
| **MON-03** | cAdvisor | cadvisor | cadvisor:1.0 | 容器监控数据采集 | 1C/2G |
| **MON-04** | Blackbox Exporter | blackbox-exporter | blackbox-exporter:1.0 | 网络探测监控 | 0.5C/1G |
| **MON-05** | AlertManager | alertmanager | alertmanager:1.0 | 告警管理和路由 | 1C/2G |
| **MON-06** | 钉钉告警服务 | alone-alert | alone-alert:1.0 | 自定义告警推送 | 0.5C/1G |
| **MON-07** | Grafana | grafana | grafana:1.0 | 监控数据可视化仪表板 | 1C/2G/20GB |
| **MON-08** | Portainer | portainer | portainer:1.0 | 容器管理界面 | 0.5C/1G/5GB |

**监控服务总计**: 7C/14G 内存, 75GB 存储

---

### 🔧 辅助服务 (auxiliary-stack) - **可选**

| 服务ID | 服务名称 | 容器名称 | 镜像 | 功能描述 | 资源需求 |
|--------|----------|----------|------|----------|----------|
| **AUX-01** | SSH 调试服务 | ssh-service | ssh 相关镜像 | 远程访问和调试 | 0.5C/1G |
| **AUX-02** | IVS 智能视频服务 | ivs-service | IVS 相关镜像 | 智能视频分析处理 | 2C/4G |

**辅助服务总计**: 2.5C/5G 内存

---

## 📊 资源需求汇总

| 服务栈 | CPU 需求 | 内存需求 | 存储需求 | 状态 |
|--------|----------|----------|----------|------|
| 基础设施服务 | 10.5C | 20G | 430GB | **必需** |
| 核心业务服务 | 7.5C | 15G | - | **必需** |
| IoT 平台服务 | 12C | 20G | - | 可选 |
| GIS 地理信息服务 | 2.5C | 5G | - | 可选 |
| MES 业务前端服务 | 2.5C | 5G | - | 可选 |
| 工作流服务 | 2.5C | 5G | - | 可选 |
| 移动应用服务 | 1.5C | 3G | - | 可选 |
| 视频监控服务 | 9.5C | 19G | 10GB | 可选 |
| 监控运维服务 | 7C | 14G | 75GB | 可选 |
| 辅助服务 | 2.5C | 5G | - | 可选 |

**全部服务总计**: 57.5C/111G 内存, 515GB 存储

---

## ❓ 请您选择部署方案

请告诉我您希望部署哪些服务栈，我将基于您的选择制定具体的 Docker Swarm 实施方案：

1. **基础设施服务** (infrastructure-stack) - 必需
2. **核心业务服务** (core-stack) - 必需  
3. **IoT 平台服务** (iot-stack) - 可选
4. **GIS 地理信息服务** (gis-stack) - 可选
5. **MES 业务前端服务** (mes-stack) - 可选
6. **工作流服务** (workflow-stack) - 可选
7. **移动应用服务** (app-stack) - 可选
8. **视频监控服务** (media-stack) - 可选
9. **监控运维服务** (monitoring-stack) - 可选
10. **辅助服务** (auxiliary-stack) - 可选

例如，您可以回复："我选择部署：1, 2, 3, 9" 来选择基础设施、核心业务、IoT 平台和监控运维服务。
