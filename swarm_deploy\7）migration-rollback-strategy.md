# 7）Docker Swarm 迁移和回滚策略

## 🔄 从 Docker Compose 到 Docker Swarm 迁移方案

### 迁移策略概述

基于您的混合模式需求，设计了渐进式迁移策略：

```mermaid
graph TB
    subgraph "迁移阶段"
        A[阶段1: 环境准备] --> B[阶段2: 基础设施迁移]
        B --> C[阶段3: 核心服务迁移]
        C --> D[阶段4: 业务服务迁移]
        D --> E[阶段5: 监控服务迁移]
        E --> F[阶段6: 验证和优化]
    end
    
    subgraph "并行环境"
        G[Docker Compose<br/>开发测试环境]
        H[Docker Swarm<br/>生产环境]
    end
    
    F --> G
    F --> H
```

### 🎯 迁移前准备

#### 环境评估脚本
```bash
#!/bin/bash
# migration_assessment.sh - 迁移前环境评估

set -e

echo "=== Docker Compose 到 Docker Swarm 迁移评估 ==="
echo

# 检查当前 Docker Compose 环境
check_compose_environment() {
    echo "📋 检查当前 Docker Compose 环境..."
    
    # 检查 compose 文件
    local compose_files=$(find compose-deploy/services -name "docker-compose.yml" | wc -l)
    echo "发现 $compose_files 个 docker-compose.yml 文件"
    
    # 检查运行中的服务
    local running_services=$(docker ps --format "{{.Names}}" | wc -l)
    echo "当前运行 $running_services 个容器"
    
    # 检查数据卷
    local volumes=$(docker volume ls -q | wc -l)
    echo "当前有 $volumes 个数据卷"
    
    # 检查网络
    local networks=$(docker network ls --filter driver=bridge -q | wc -l)
    echo "当前有 $networks 个自定义网络"
    
    echo "✅ Docker Compose 环境检查完成"
    echo
}

# 检查 Swarm 就绪状态
check_swarm_readiness() {
    echo "🔍 检查 Docker Swarm 就绪状态..."
    
    # 检查 Docker 版本
    local docker_version=$(docker version --format '{{.Server.Version}}')
    echo "Docker 版本: $docker_version"
    
    # 检查是否支持 Swarm
    if docker swarm --help > /dev/null 2>&1; then
        echo "✅ Docker Swarm 功能可用"
    else
        echo "❌ Docker Swarm 功能不可用"
        return 1
    fi
    
    # 检查系统资源
    local cpu_cores=$(nproc)
    local memory_gb=$(free -g | awk '/^Mem:/{print $2}')
    local disk_gb=$(df -BG / | awk 'NR==2{print $4}' | sed 's/G//')
    
    echo "系统资源: CPU $cpu_cores 核, 内存 ${memory_gb}GB, 磁盘 ${disk_gb}GB"
    
    # 资源需求检查
    if [ "$cpu_cores" -lt 4 ] || [ "$memory_gb" -lt 8 ] || [ "$disk_gb" -lt 100 ]; then
        echo "⚠️ 系统资源可能不足，建议升级硬件配置"
    else
        echo "✅ 系统资源满足要求"
    fi
    
    echo
}

# 数据迁移评估
assess_data_migration() {
    echo "💾 评估数据迁移需求..."
    
    # 检查数据卷大小
    echo "数据卷使用情况:"
    docker system df -v | grep "Local Volumes" -A 20
    
    # 检查数据库数据
    if docker ps | grep -q postgres; then
        echo "检测到 PostgreSQL 数据库，需要数据备份"
    fi
    
    if docker ps | grep -q redis; then
        echo "检测到 Redis 缓存，需要数据备份"
    fi
    
    if docker ps | grep -q tdengine; then
        echo "检测到 TDengine 时序数据库，需要数据备份"
    fi
    
    echo "✅ 数据迁移评估完成"
    echo
}

# 配置兼容性检查
check_config_compatibility() {
    echo "⚙️ 检查配置兼容性..."
    
    # 检查 docker-compose.yml 版本
    local compose_files=$(find compose-deploy/services -name "docker-compose.yml")
    
    for file in $compose_files; do
        local version=$(grep "version:" "$file" | head -1 | awk '{print $2}' | tr -d '"')
        echo "文件 $file 使用版本: $version"
        
        # 检查是否兼容 Swarm
        if [[ "$version" < "3.0" ]]; then
            echo "⚠️ 版本 $version 不支持 Docker Swarm，需要升级到 3.0+"
        else
            echo "✅ 版本 $version 支持 Docker Swarm"
        fi
    done
    
    echo
}

# 生成迁移计划
generate_migration_plan() {
    echo "📋 生成迁移计划..."
    
    cat > migration_plan.md << 'EOF'
# Docker Compose 到 Docker Swarm 迁移计划

## 迁移时间表

### 阶段1: 环境准备 (1-2天)
- [ ] 备份现有数据
- [ ] 初始化 Swarm 集群
- [ ] 创建网络和存储卷
- [ ] 配置密钥和配置文件

### 阶段2: 基础设施迁移 (1天)
- [ ] 迁移 PostgreSQL 数据库
- [ ] 迁移 Redis 缓存
- [ ] 迁移 RabbitMQ 消息队列
- [ ] 迁移 TDengine 时序数据库
- [ ] 迁移 MinIO 对象存储

### 阶段3: 核心服务迁移 (1天)
- [ ] 迁移授权认证服务
- [ ] 迁移用户权限管理服务
- [ ] 迁移消息推送服务
- [ ] 迁移 Nacos 配置中心
- [ ] 迁移前端网关

### 阶段4: 业务服务迁移 (2-3天)
- [ ] 迁移 IoT 平台服务
- [ ] 验证数据接入和处理
- [ ] 性能测试和优化

### 阶段5: 监控服务迁移 (1天)
- [ ] 迁移 Prometheus 监控
- [ ] 迁移 Grafana 仪表板
- [ ] 迁移告警系统
- [ ] 迁移 Portainer 管理界面

### 阶段6: 验证和优化 (1-2天)
- [ ] 全面功能测试
- [ ] 性能基准测试
- [ ] 监控告警测试
- [ ] 文档更新

## 风险评估

### 高风险项
- 数据库数据迁移
- 网络配置变更
- 服务依赖关系

### 缓解措施
- 完整数据备份
- 分阶段迁移
- 回滚预案

## 成功标准
- 所有服务正常运行
- 数据完整性验证通过
- 性能指标达到预期
- 监控告警正常工作
EOF
    
    echo "✅ 迁移计划已生成: migration_plan.md"
    echo
}

# 执行评估
main() {
    check_compose_environment
    check_swarm_readiness
    assess_data_migration
    check_config_compatibility
    generate_migration_plan
    
    echo "🎉 迁移评估完成！"
    echo "请查看 migration_plan.md 了解详细迁移计划"
}

main
```

### 📦 数据迁移脚本

#### 数据库迁移脚本
```bash
#!/bin/bash
# database_migration.sh - 数据库迁移脚本

set -e

BACKUP_DIR="/tmp/migration_backup"
MIGRATION_DATE=$(date +%Y%m%d_%H%M%S)

# 创建备份目录
mkdir -p "$BACKUP_DIR"

# 日志函数
log_info() { echo "[INFO] $(date '+%Y-%m-%d %H:%M:%S') $1"; }
log_error() { echo "[ERROR] $(date '+%Y-%m-%d %H:%M:%S') $1"; }
log_success() { echo "[SUCCESS] $(date '+%Y-%m-%d %H:%M:%S') $1"; }

# 备份 PostgreSQL 数据
backup_postgresql() {
    log_info "备份 PostgreSQL 数据..."
    
    # 主数据库备份
    if docker ps | grep -q base_postgres; then
        docker exec base_postgres pg_dumpall -U postgres > \
            "$BACKUP_DIR/postgres_main_$MIGRATION_DATE.sql"
        log_success "PostgreSQL 主数据库备份完成"
    fi
    
    # 业务数据库备份
    if docker ps | grep -q base_business_postgres; then
        docker exec base_business_postgres pg_dumpall -U mes > \
            "$BACKUP_DIR/postgres_business_$MIGRATION_DATE.sql"
        log_success "PostgreSQL 业务数据库备份完成"
    fi
}

# 备份 Redis 数据
backup_redis() {
    log_info "备份 Redis 数据..."
    
    if docker ps | grep -q base_redis; then
        # 触发 Redis 保存
        docker exec base_redis redis-cli BGSAVE
        sleep 10
        
        # 复制 RDB 文件
        docker cp base_redis:/data/dump.rdb \
            "$BACKUP_DIR/redis_$MIGRATION_DATE.rdb"
        log_success "Redis 数据备份完成"
    fi
}

# 备份 TDengine 数据
backup_tdengine() {
    log_info "备份 TDengine 数据..."
    
    if docker ps | grep -q tdengine; then
        # 使用 taosdump 备份
        docker exec tdengine taosdump -o "/tmp/tdengine_backup_$MIGRATION_DATE"
        docker cp "tdengine:/tmp/tdengine_backup_$MIGRATION_DATE" \
            "$BACKUP_DIR/"
        log_success "TDengine 数据备份完成"
    fi
}

# 备份配置文件
backup_configs() {
    log_info "备份配置文件..."
    
    # 备份 compose 配置
    tar -czf "$BACKUP_DIR/compose_configs_$MIGRATION_DATE.tar.gz" \
        compose-deploy/
    
    log_success "配置文件备份完成"
}

# 停止 Compose 服务
stop_compose_services() {
    log_info "停止 Docker Compose 服务..."
    
    cd compose-deploy
    ./compose_deploy.sh stop
    
    log_success "Docker Compose 服务已停止"
}

# 迁移数据卷
migrate_volumes() {
    log_info "迁移数据卷..."
    
    # 获取现有卷列表
    local volumes=$(docker volume ls -q)
    
    for volume in $volumes; do
        log_info "处理数据卷: $volume"
        
        # 创建临时容器备份数据
        docker run --rm -v "$volume:/source" -v "$BACKUP_DIR:/backup" \
            busybox tar -czf "/backup/volume_${volume}_$MIGRATION_DATE.tar.gz" -C /source .
        
        log_success "数据卷 $volume 备份完成"
    done
}

# 恢复数据到 Swarm
restore_to_swarm() {
    log_info "恢复数据到 Docker Swarm..."
    
    # 确保 Swarm 基础设施已部署
    if ! docker service ls | grep -q infrastructure; then
        log_error "请先部署 Swarm 基础设施服务"
        return 1
    fi
    
    # 等待数据库服务启动
    log_info "等待数据库服务启动..."
    sleep 60
    
    # 恢复 PostgreSQL 数据
    if [ -f "$BACKUP_DIR/postgres_main_$MIGRATION_DATE.sql" ]; then
        log_info "恢复 PostgreSQL 主数据库..."
        docker exec -i $(docker ps -q -f name=infrastructure_postgres) \
            psql -U postgres < "$BACKUP_DIR/postgres_main_$MIGRATION_DATE.sql"
        log_success "PostgreSQL 主数据库恢复完成"
    fi
    
    if [ -f "$BACKUP_DIR/postgres_business_$MIGRATION_DATE.sql" ]; then
        log_info "恢复 PostgreSQL 业务数据库..."
        docker exec -i $(docker ps -q -f name=infrastructure_business-postgres) \
            psql -U mes < "$BACKUP_DIR/postgres_business_$MIGRATION_DATE.sql"
        log_success "PostgreSQL 业务数据库恢复完成"
    fi
    
    # 恢复 Redis 数据
    if [ -f "$BACKUP_DIR/redis_$MIGRATION_DATE.rdb" ]; then
        log_info "恢复 Redis 数据..."
        docker cp "$BACKUP_DIR/redis_$MIGRATION_DATE.rdb" \
            $(docker ps -q -f name=infrastructure_redis):/data/dump.rdb
        docker service update --force infrastructure_redis
        log_success "Redis 数据恢复完成"
    fi
    
    # 恢复 TDengine 数据
    if [ -d "$BACKUP_DIR/tdengine_backup_$MIGRATION_DATE" ]; then
        log_info "恢复 TDengine 数据..."
        docker cp "$BACKUP_DIR/tdengine_backup_$MIGRATION_DATE" \
            $(docker ps -q -f name=infrastructure_tdengine):/tmp/
        docker exec $(docker ps -q -f name=infrastructure_tdengine) \
            taosdump -i "/tmp/tdengine_backup_$MIGRATION_DATE"
        log_success "TDengine 数据恢复完成"
    fi
}

# 验证数据完整性
verify_data_integrity() {
    log_info "验证数据完整性..."
    
    # 检查 PostgreSQL 数据
    local pg_tables=$(docker exec $(docker ps -q -f name=infrastructure_postgres) \
        psql -U postgres -d gis -t -c "SELECT count(*) FROM information_schema.tables WHERE table_schema='public';")
    log_info "PostgreSQL 主数据库表数量: $pg_tables"
    
    # 检查 Redis 数据
    local redis_keys=$(docker exec $(docker ps -q -f name=infrastructure_redis) \
        redis-cli DBSIZE)
    log_info "Redis 键数量: $redis_keys"
    
    log_success "数据完整性验证完成"
}

# 主迁移流程
main_migration() {
    log_info "开始数据库迁移流程..."
    
    # 备份阶段
    backup_postgresql
    backup_redis
    backup_tdengine
    backup_configs
    migrate_volumes
    
    # 停止 Compose 服务
    stop_compose_services
    
    # 恢复到 Swarm
    restore_to_swarm
    
    # 验证数据
    verify_data_integrity
    
    log_success "数据库迁移完成！"
    log_info "备份文件保存在: $BACKUP_DIR"
}

# 执行迁移
case ${1:-migrate} in
    "backup")
        backup_postgresql
        backup_redis
        backup_tdengine
        backup_configs
        migrate_volumes
        ;;
    "restore")
        restore_to_swarm
        verify_data_integrity
        ;;
    "migrate")
        main_migration
        ;;
    *)
        echo "用法: $0 {backup|restore|migrate}"
        exit 1
        ;;
esac
```

## 🔙 回滚策略

### 服务回滚机制

#### 自动回滚配置
```yaml
# Docker Swarm 服务自动回滚配置
services:
  example-service:
    image: example:latest
    deploy:
      replicas: 3
      update_config:
        parallelism: 1
        delay: 30s
        failure_action: rollback
        monitor: 60s
        max_failure_ratio: 0.3
        order: start-first
      rollback_config:
        parallelism: 1
        delay: 30s
        failure_action: pause
        monitor: 60s
        max_failure_ratio: 0.3
        order: stop-first
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
        window: 120s
```

#### 手动回滚脚本
```bash
#!/bin/bash
# rollback_service.sh - 服务回滚脚本

set -e

# 日志函数
log_info() { echo "[INFO] $(date '+%Y-%m-%d %H:%M:%S') $1"; }
log_error() { echo "[ERROR] $(date '+%Y-%m-%d %H:%M:%S') $1"; }
log_success() { echo "[SUCCESS] $(date '+%Y-%m-%d %H:%M:%S') $1"; }

# 回滚单个服务
rollback_service() {
    local service_name=$1
    
    if [ -z "$service_name" ]; then
        log_error "请指定服务名称"
        return 1
    fi
    
    log_info "开始回滚服务: $service_name"
    
    # 检查服务是否存在
    if ! docker service inspect "$service_name" > /dev/null 2>&1; then
        log_error "服务不存在: $service_name"
        return 1
    fi
    
    # 获取当前服务信息
    local current_image=$(docker service inspect "$service_name" --format '{{.Spec.TaskTemplate.ContainerSpec.Image}}')
    log_info "当前镜像: $current_image"
    
    # 执行回滚
    docker service rollback "$service_name"
    
    # 等待回滚完成
    log_info "等待回滚完成..."
    local timeout=300
    local elapsed=0
    
    while [ $elapsed -lt $timeout ]; do
        local status=$(docker service ps "$service_name" --format '{{.CurrentState}}' | head -1)
        if echo "$status" | grep -q "Running"; then
            log_success "服务回滚完成: $service_name"
            
            # 显示回滚后的镜像
            local rollback_image=$(docker service inspect "$service_name" --format '{{.Spec.TaskTemplate.ContainerSpec.Image}}')
            log_info "回滚后镜像: $rollback_image"
            return 0
        fi
        
        sleep 10
        elapsed=$((elapsed + 10))
    done
    
    log_error "服务回滚超时: $service_name"
    return 1
}

# 回滚整个栈
rollback_stack() {
    local stack_name=$1
    
    if [ -z "$stack_name" ]; then
        log_error "请指定栈名称"
        return 1
    fi
    
    log_info "开始回滚栈: $stack_name"
    
    # 获取栈中的所有服务
    local services=$(docker stack services "$stack_name" --format '{{.Name}}')
    
    if [ -z "$services" ]; then
        log_error "栈中没有服务: $stack_name"
        return 1
    fi
    
    # 逐个回滚服务
    for service in $services; do
        rollback_service "$service"
    done
    
    log_success "栈回滚完成: $stack_name"
}

# 回滚到指定版本
rollback_to_version() {
    local service_name=$1
    local target_image=$2
    
    if [ -z "$service_name" ] || [ -z "$target_image" ]; then
        log_error "用法: rollback_to_version <service_name> <target_image>"
        return 1
    fi
    
    log_info "回滚服务 $service_name 到版本: $target_image"
    
    # 更新服务镜像
    docker service update --image "$target_image" "$service_name"
    
    # 等待更新完成
    log_info "等待版本回滚完成..."
    sleep 60
    
    # 验证回滚结果
    local current_image=$(docker service inspect "$service_name" --format '{{.Spec.TaskTemplate.ContainerSpec.Image}}')
    if [ "$current_image" = "$target_image" ]; then
        log_success "版本回滚成功: $service_name -> $target_image"
    else
        log_error "版本回滚失败: $service_name"
        return 1
    fi
}

# 数据库回滚
rollback_database() {
    local backup_date=$1
    
    if [ -z "$backup_date" ]; then
        log_error "请指定备份日期"
        return 1
    fi
    
    log_info "开始数据库回滚: $backup_date"
    
    # 停止相关服务
    log_info "停止相关服务..."
    docker service scale infrastructure_postgres=0
    docker service scale infrastructure_business-postgres=0
    sleep 30
    
    # 恢复数据库
    local backup_dir="/backups"
    
    if [ -f "$backup_dir/postgres_main_$backup_date.sql" ]; then
        log_info "恢复 PostgreSQL 主数据库..."
        docker service scale infrastructure_postgres=1
        sleep 60
        
        docker exec -i $(docker ps -q -f name=infrastructure_postgres) \
            psql -U postgres < "$backup_dir/postgres_main_$backup_date.sql"
        log_success "PostgreSQL 主数据库回滚完成"
    fi
    
    if [ -f "$backup_dir/postgres_business_$backup_date.sql" ]; then
        log_info "恢复 PostgreSQL 业务数据库..."
        docker service scale infrastructure_business-postgres=1
        sleep 60
        
        docker exec -i $(docker ps -q -f name=infrastructure_business-postgres) \
            psql -U mes < "$backup_dir/postgres_business_$backup_date.sql"
        log_success "PostgreSQL 业务数据库回滚完成"
    fi
    
    log_success "数据库回滚完成"
}

# 完整系统回滚
full_system_rollback() {
    local backup_date=$1
    
    if [ -z "$backup_date" ]; then
        log_error "请指定备份日期"
        return 1
    fi
    
    log_info "开始完整系统回滚: $backup_date"
    
    # 确认操作
    echo "⚠️  警告：此操作将回滚整个系统到 $backup_date 状态"
    echo "这将覆盖所有当前数据，请确认是否继续？"
    read -p "输入 'YES' 确认继续: " confirm
    
    if [ "$confirm" != "YES" ]; then
        log_info "回滚操作已取消"
        return 0
    fi
    
    # 停止所有服务
    log_info "停止所有服务..."
    local stacks=$(docker stack ls --format '{{.Name}}')
    for stack in $stacks; do
        docker stack rm "$stack"
    done
    
    # 等待服务完全停止
    sleep 120
    
    # 恢复数据
    rollback_database "$backup_date"
    
    # 重新部署服务
    log_info "重新部署服务..."
    cd swarm_deploy
    ./swarm_deploy.sh deploy
    
    log_success "完整系统回滚完成"
}

# 健康检查回滚
health_check_rollback() {
    local service_name=$1
    local health_check_url=$2
    local max_attempts=${3:-10}
    
    log_info "执行健康检查回滚: $service_name"
    
    local attempt=1
    while [ $attempt -le $max_attempts ]; do
        log_info "健康检查尝试 $attempt/$max_attempts"
        
        if curl -f -s "$health_check_url" > /dev/null; then
            log_success "服务健康检查通过: $service_name"
            return 0
        fi
        
        sleep 30
        ((attempt++))
    done
    
    log_error "服务健康检查失败，执行自动回滚: $service_name"
    rollback_service "$service_name"
}

# 主程序
case ${1:-help} in
    "service")
        rollback_service "$2"
        ;;
    "stack")
        rollback_stack "$2"
        ;;
    "version")
        rollback_to_version "$2" "$3"
        ;;
    "database")
        rollback_database "$2"
        ;;
    "full")
        full_system_rollback "$2"
        ;;
    "health-check")
        health_check_rollback "$2" "$3" "$4"
        ;;
    "help"|*)
        cat << EOF
回滚脚本使用说明:

用法: $0 COMMAND [OPTIONS]

命令:
  service <service_name>              回滚指定服务
  stack <stack_name>                  回滚整个栈
  version <service_name> <image>      回滚到指定版本
  database <backup_date>              回滚数据库
  full <backup_date>                  完整系统回滚
  health-check <service> <url> [max]  健康检查回滚

示例:
  $0 service infrastructure_postgres
  $0 stack infrastructure
  $0 version core_auth-service auth:v1.0.0
  $0 database 20240127_143000
  $0 full 20240127_143000
  $0 health-check core_auth-service http://localhost:22260/health 5

注意:
  - 回滚操作不可逆，请谨慎操作
  - 建议在回滚前创建当前状态的备份
  - 完整系统回滚会影响所有服务
EOF
        ;;
esac
```

### 🔄 蓝绿部署策略

#### 蓝绿部署脚本
```bash
#!/bin/bash
# blue_green_deployment.sh - 蓝绿部署脚本

set -e

# 配置
BLUE_STACK_SUFFIX="_blue"
GREEN_STACK_SUFFIX="_green"
HEALTH_CHECK_TIMEOUT=300

# 日志函数
log_info() { echo "[INFO] $(date '+%Y-%m-%d %H:%M:%S') $1"; }
log_error() { echo "[ERROR] $(date '+%Y-%m-%d %H:%M:%S') $1"; }
log_success() { echo "[SUCCESS] $(date '+%Y-%m-%d %H:%M:%S') $1"; }

# 获取当前活跃环境
get_active_environment() {
    local stack_name=$1
    
    if docker stack ls | grep -q "${stack_name}${BLUE_STACK_SUFFIX}"; then
        if docker stack ls | grep -q "${stack_name}${GREEN_STACK_SUFFIX}"; then
            # 两个环境都存在，检查哪个在接收流量
            echo "both"
        else
            echo "blue"
        fi
    elif docker stack ls | grep -q "${stack_name}${GREEN_STACK_SUFFIX}"; then
        echo "green"
    else
        echo "none"
    fi
}

# 部署到非活跃环境
deploy_to_inactive() {
    local stack_name=$1
    local stack_file=$2
    local active_env=$(get_active_environment "$stack_name")
    
    case $active_env in
        "blue"|"both")
            local target_env="green"
            local target_stack="${stack_name}${GREEN_STACK_SUFFIX}"
            ;;
        "green"|"none")
            local target_env="blue"
            local target_stack="${stack_name}${BLUE_STACK_SUFFIX}"
            ;;
    esac
    
    log_info "部署到 $target_env 环境: $target_stack"
    
    # 修改栈文件中的服务名称
    local temp_stack_file="/tmp/${target_stack}.yml"
    sed "s/{{ENVIRONMENT}}/$target_env/g" "$stack_file" > "$temp_stack_file"
    
    # 部署栈
    docker stack deploy -c "$temp_stack_file" "$target_stack"
    
    log_success "$target_env 环境部署完成"
    echo "$target_env"
}

# 健康检查
health_check() {
    local stack_name=$1
    local environment=$2
    local timeout=${3:-$HEALTH_CHECK_TIMEOUT}
    
    log_info "执行 $environment
