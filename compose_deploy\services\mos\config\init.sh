#!/bin/bash

#===============================================================================
# 服务授权认证初始化脚本
#===============================================================================
# 功能描述: 收集系统硬件信息用于授权认证和许可证验证
# 执行环境: 在服务的授权认证容器中执行
# 依赖服务: service-authorization 容器
# 调用方式: 在 docker-compose.yml 中作为初始化脚本被调用
#
# 硬件信息收集范围:
#   - CPU 处理器 ID (用于硬件指纹识别)
#   - 系统主板 UUID (用于设备唯一标识)
#   - CPU 微码版本 (用于系统版本验证)
#   - 网络接口 UUID (用于网络设备绑定)
#
# 注意事项:
#   - 此脚本需要特权模式运行以访问硬件信息
#   - 收集的信息用于许可证验证，请确保数据准确性
#   - 脚本执行失败可能影响授权服务启动
#===============================================================================

# 遇到错误立即退出
set -e

#===============================================================================
# 硬件信息收集 (保持原有输出格式和逻辑)
#===============================================================================

# 1. 获取 CPU 处理器 ID (用于硬件指纹识别)
dmidecode -t processor | grep ID | awk -F ': ' '{print $2}' | head -1 &&

# 2. 获取系统主板 UUID (用于设备唯一标识)
dmidecode -t system | grep UUID | awk -F ': ' '{print $2}' &&

# 3. 获取 CPU 微码版本 (用于系统版本验证)
cat /proc/cpuinfo | grep microcode | head -1 &&

# 4. 获取网络接口 UUID (用于网络设备绑定)
cat /tmp/info/* | grep uuid | cut -d "=" -f 2 | uniq | sort
