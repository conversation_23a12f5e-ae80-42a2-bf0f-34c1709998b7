# 综合信息平台

[![Version](https://img.shields.io/badge/version-v1.2.11-blue.svg)](./CHANGELOG.md)
[![Docker](https://img.shields.io/badge/docker-%3E%3D20.10.14-blue.svg)](https://docs.docker.com/engine/release-notes/)
[![Docker Compose](https://img.shields.io/badge/docker--compose-%3E%3D1.27.4-blue.svg)](https://docs.docker.com/compose/release-notes/)

> 面向能源行业的企业级综合管控平台，采用微服务架构，提供完整的数字化解决方案

## 🎯 项目概述

xxxx综合信息平台是一个基于 Docker 容器化部署的微服务平台，集成了用户权限管理、物联网数据接入、GIS地理信息系统、视频监控、制造执行系统等多个业务模块，为能源企业提供一站式的数字化管控解决方案。

### 核心特性

- 🏗️ **微服务架构**：模块化设计，支持按需部署
- 🐳 **容器化部署**：基于 Docker Compose 的标准化部署
- 🔄 **自动化运维**：一键部署、版本升级、数据备份
- 📊 **实时数据处理**：支持海量传感器数据接入和处理
- 🗺️ **可视化展示**：GIS地图、拓扑图、监控面板
- 🔐 **安全可靠**：完善的权限管理和数据备份机制

## 📁 项目结构

```text
compose_deploy/
├── 📂 config/                 # 全局配置目录
│   ├── .config.env            # 全局配置文件
│   └── .env                   # 环境变量文件
├── 📂 libs/                   # 核心库脚本（compose_deploy.sh 依赖）
│   ├── utils.sh               # 工具函数库和系统检查
│   ├── service.sh             # 服务部署管理工具
│   ├── docker.sh              # Docker 操作工具库
│   ├── database.sh            # PostgreSQL 数据库备份工具
│   ├── git.sh                 # Git 版本管理工具
│   ├── psql-executor.sh       # PostgreSQL 和 TDengine 数据库执行器
│   ├── migration.sh           # Hasura GraphQL 数据库迁移工具
│   └── README.md              # 核心库说明
├── 📂 tools/                  # 运维工具脚本
│   ├── images_save.sh         # 镜像批量导出工具
│   ├── images_save_one.sh     # 镜像一键保存工具
│   ├── images_load_one.sh     # 镜像单文件导入工具
│   ├── docker_clean.sh        # Docker 系统清理工具
│   ├── harbor-login-*.sh.x    # Harbor 认证工具
│   ├── restore/               # 数据恢复工具
│   └── README.md              # 运维工具说明
├── 📂 services/               # 微服务目录
│   ├── base/                  # 基础服务（数据库、缓存等）
│   │   ├── config/            # 基础设施配置文件
│   │   │   ├── postgresql.conf    # PostgreSQL 配置
│   │   │   ├── redis.template     # Redis 配置模板
│   │   │   ├── rabbitmq.conf      # RabbitMQ 配置
│   │   │   └── influxdb.conf      # InfluxDB 配置
│   │   └── data/              # 基础设施数据管理
│   │       ├── init/          # 初始化脚本
│   │       └── migrations/    # 版本迁移脚本
│   ├── gis/                   # GIS 地理信息系统
│   │   ├── config/            # GIS 服务配置
│   │   └── data/              # GIS 数据管理
│   │       ├── init/          # 初始化脚本
│   │       └── migrations/    # 版本迁移脚本
│   ├── mes/                   # MES 制造执行系统
│   │   ├── config/            # MES 服务配置
│   │   └── data/              # MES 数据管理
│   │       ├── init/          # 初始化脚本
│   │       └── migrations/    # 版本迁移脚本
│   ├── iot/                   # IoT 物联网服务
│   │   ├── config/            # IoT 服务配置
│   │   └── data/              # IoT 数据管理
│   ├── video/                 # 视频监控服务
│   │   ├── config/            # 视频服务配置
│   │   └── data/              # 视频数据管理
│   ├── workflow/              # 工作流服务
│   │   ├── config/            # 工作流服务配置
│   │   └── data/              # 工作流数据管理
│   ├── mos/                   # MOS 制造运营系统
│   ├── app/                   # 移动应用服务
│   ├── monitoring/            # 监控工具服务
│   ├── ivs/                   # IVS 智能视频服务
│   └── ssh/                   # SSH 远程访问服务
├── 📂 docs/                   # 文档和图片资源
├── 🚀 compose_deploy.sh          # 主部署脚本
├── 📋 CHANGELOG.md               # 版本变更日志
├── 🔄 upgrade.sh                 # 版本升级脚本
├── 🔄 git_pull.sh                # Git代码拉取脚本
├── 📂 scripts/                   # 初始化脚本
│   ├── init.minio.sh             # MinIO初始化脚本
│   ├── init.topo.sh              # Topo拓扑图初始化脚本
│   └── minio/                    # MinIO相关资源
├── 📂 others/                    # 其他配置文件
│   ├── dismissed-tables.yaml     # 已废弃表配置
│   ├── settings.js               # 系统设置
│   └── moni_temp/                # 监控模板
└── � README.md                  # 项目说明（本文件）
```

## 🏗️ 系统架构

### 微服务模块

| 模块 | 说明 | 主要端口 |
|------|------|----------|
| **base** | 基础设施服务 | 内部通信 |
| **mos** | 制造运营系统 | 80, 22260, 14148 |
| **iot** | 物联网平台服务 | 9003 |
| **gis** | 地理信息系统 | 9101 |
| **video** | 视频服务 | 1935, 1980, 8988 |
| **mes** | 制造执行系统 | 9102 |
| **workflow** | 工作流服务 | 8004 |
| **app** | 移动应用服务 | - |
| **ssh** | SSH远程服务 | 2222 |
| **monitoring** | 系统监控服务 | 30000, 39090, 39000 |
| **ivs** | 智能视频服务 | - |

### 技术栈

**前端技术**

- Vue.js / React 前端框架
- Nginx 反向代理和负载均衡
- 静态资源服务

**后端技术**

- Spring Boot 微服务框架
- Node-RED 数据网关
- Hasura GraphQL API

**数据存储**

- PostgreSQL 关系数据库
- TDengine 时序数据库
- Redis 缓存数据库
- MinIO 对象存储

**中间件**

- RabbitMQ 消息队列
- Nacos 配置中心
- SRS 流媒体服务器

### 整体架构设计

本系统采用微服务架构，基于Docker容器化部署，通过Docker Compose进行服务编排。

#### 核心架构特点

- **微服务架构**: 模块化设计，支持按需部署
- **容器化部署**: 基于Docker Compose的标准化部署
- **服务编排**: 使用YAML锚点复用配置
- **数据持久化**: 使用Docker命名卷管理数据

#### 服务分层结构

| 层级 | 服务模块 | 说明 |
|------|----------|------|
| **基础设施层** | base | 数据库、缓存、消息队列等基础组件 |
| **配置管理层** | mos | 用户认证、配置中心、消息推送 |
| **业务服务层** | iot, gis, mes, video, workflow, app, ivs | 各业务功能模块 |
| **监控管理层** | monitoring | 系统监控、容器管理 |
| **辅助服务层** | ssh | 远程调试和运维支持 |

## 🚀 快速开始

### 环境要求

- **操作系统**: CentOS 7+ / Ubuntu 18.04+
- **Docker Engine**: ≥ 20.10.14
- **Docker Compose**: ≥ 1.27.4
- **Git**: ≥ 2.24
- **内存**: ≥ 16GB（推荐 32GB）
- **磁盘**: ≥ 100GB 可用空间

### 1. 系统初始化

```bash
# 执行系统初始化（需要 root 权限）
cd ../common-setups/
chmod +x centos7-init.sh
./centos7-init.sh
reboot
```

### 2. 配置环境变量

#### 2.1 全局配置

编辑 [`config/.config.env`](config/.config.env) 文件：

```bash
# 复制配置模板
cp config/.config.env.template config/.config.env 2>/dev/null || true

# 编辑配置文件
vim config/.config.env
```

#### 2.2 主要配置项

配置文件: `./config/.config.env`

```bash
# 基本配置
export MINECODE=199988
export AUTH_SERVICE_ID=skeleton
export AUTH_SERVICE_NAME=基座平台
export AUTH_PROJECT_ID=199988
export AUTH_PROJECT_NAME=XX煤矿

# 服务开关
DEPLOY_BASE_SERVICE=true
DEPLOY_MOS_SERVICE=true
DEPLOY_IOT_SERVICE=true
DEPLOY_MONITORING_SERVICE=false
```

配置文件: `./base/.env`

```bash
# 数据库密码
POSTGRES_PASSWORD=your_secure_password
BLADEX_POSTGRES_PASSWORD=your_secure_password
REDIS_PASSWORD=your_redis_password
BLADEX_REDIS_PASSWORD=your_redis_password
RABBITMQ_PASSWORD=your_rabbitmq_password
BLADEX_RABBITMQ_PASSWORD=your_rabbitmq_password
MINIO_SECRET_KEY=your_minio_password
BLADEX_MINIO_SECRET_KEY=your_minio_password
```

#### 2.3 配置验证

```bash
# 检查配置文件
find services/ -name ".env" -type f

# 验证配置
./compose_deploy.sh config check
```

### 3. 部署服务

#### 3.1 一键部署

```bash
# 全新部署
./compose_deploy.sh deploy -i

# 启动服务
./compose_deploy.sh up
```

#### 3.2 验证部署

```bash
# 检查状态
./compose_deploy.sh health-check

# 查看日志
./compose_deploy.sh logs <service_name>

# 测试访问
curl -f http://localhost/
```

#### 3.3 开关 SSH 调试服务

```bash
# 1. 开启服务
./compose_deploy.sh ssh start

# 2. 关闭服务
./compose_deploy.sh ssh stop
```

## 🌐 服务端口

| 端口 | 服务 | 说明 |
|------|------|------|
| 80 | 前端主页面 | 系统入口 |
| 2222 | SSH服务 | 远程调试 |
| 5432 | PostgreSQL | 数据库 |
| 9003 | IOT平台 | 数据监控 |
| 22260 | 授权服务 | 用户认证 |
| 1935/1980 | 视频服务 | 流媒体 |
| 30000 | 监控服务 | Portainer |

## 📊 系统监控

### 访问地址

- **主系统**: http://your-server-ip
- **监控面板**: http://your-server-ip:30000 (Portainer)
- **数据监控**: http://your-server-ip:9003 (IOT平台)

### 健康检查

```bash
# 查看资源
docker stats
```

## 🔐 安全配置

### MinIO 存储桶配置

```bash
# 1. 启动SSH代理
./compose_deploy.sh ssh start

# 2. 端口转发访问MinIO管理界面
ssh -L 9000:bladex-minio:9000 -p 2222 root@服务器IP

# 3. 浏览器访问 http://localhost:9000
# 默认账号: minio  密码: bGVhbmlvdA

# 4. 配置存储桶策略后关闭代理
./compose_deploy.sh ssh stop
```

## 🔧 运维管理

### 服务管理

```bash
# 日志查看
./compose_deploy.sh logs <service> [-f] [--tail=N/-n N]

# 容器操作
./compose_deploy.sh exec <service> <command>
./compose_deploy.sh exec -it <service> <command>

# 系统维护
./compose_deploy.sh backup                 # 备份数据
./compose_deploy.sh enable-auto-backup     # 启用自动备份
```

### 版本管理

```bash
# 版本升级
./compose_deploy.sh update [-r] [-t version]

# 查看版本
./compose_deploy.sh version
```

## 🐛 故障排除

### 常见问题

#### 容器启动失败

**症状**: 容器无法启动或频繁重启

**排查步骤**:

```bash
# 检查状态和日志
./compose_deploy.sh logs <service> -f -n 50

# 检查资源
df -h                         # 磁盘空间
free -h                       # 内存使用
netstat -tlnp | grep <port>   # 端口占用
```

**常见原因**:

- 内存不足（推荐 16GB+）
- 磁盘空间不足
- 端口冲突
- 配置文件错误

#### 数据库连接失败

**症状**: 应用无法连接到数据库

**排查步骤**:

```bash
# 连接测试
./compose_deploy.sh exec postgres psql -U postgres -c "SELECT version();"
./compose_deploy.sh exec redis redis-cli ping
./compose_deploy.sh exec tdengine taos -c "show databases;"
```

**常见原因**:

- 数据库容器未启动
- 网络配置错误
- 数据库密码错误
- 数据库初始化失败

#### 端口冲突

**症状**: 服务启动时提示端口被占用

**排查步骤**:

```bash
# 检查端口占用
netstat -tlnp | grep :80
netstat -tlnp | grep :5432

# 查看所有监听端口
ss -tlnp

# 修改配置文件中的端口设置
vim config/.config.env
```

**解决方案**:

- 停止占用端口的服务
- 修改配置文件中的端口映射
- 使用不同的端口号

#### 镜像拉取失败

**症状**: Docker 镜像下载失败或超时

**排查步骤**:

```bash
# 检查网络连接
ping harbor2.qdbdtd.com

# 手动拉取镜像
docker pull harbor2.qdbdtd.com:8088/middleware/postgres:13

# 检查 Harbor 登录状态
docker login harbor2.qdbdtd.com:8088

# 使用镜像拉取工具
./compose_deploy.sh docker pull
```

**解决方案**:

- 检查网络连接
- 重新登录 Harbor 仓库
- 使用离线镜像包

#### 版本升级失败

**症状**: 升级过程中出现错误

**排查步骤**:

```bash
# 检查当前版本
./compose_deploy.sh version

# 检查数据库版本表
docker exec -it base_postgres psql -U postgres -d gis -c "SELECT * FROM public.databasechangelog ORDER BY dateexecuted DESC LIMIT 5;"

# 查看升级日志
docker logs container_name

# 回滚到备份版本（如果有备份）
```

**预防措施**:

- 升级前执行数据备份
- 在测试环境先验证升级过程
- 确保有足够的磁盘空间

#### MinIO 存储桶访问问题

**症状**: 文件上传失败或无法访问

**排查步骤**:

```bash
# 检查 MinIO 服务状态
docker logs bladex-minio

# 启动 SSH 代理访问 MinIO 管理界面
./compose_deploy.sh ssh start
ssh -L 9000:bladex-minio:9000 -p 2222 root@服务器IP

# 浏览器访问 http://localhost:9000
# 账号: minio  密码: <your_minio_password>
```

**解决方案**:

- 配置正确的存储桶策略
- 检查网络连接
- 重启 MinIO 服务

#### TDengine 时序数据库问题

**症状**: IOT 服务无法连接 TDengine 或时序数据写入失败

**排查步骤**:

```bash
# 检查 TDengine 容器状态
docker ps | grep tdengine

# 检查 TDengine 日志
docker logs tdengine

# 测试 TDengine 连接
docker exec -it tdengine taos -h tdengine -s "SHOW DATABASES;"

# 检查时序数据库初始化状态
docker exec -it tdengine taos -h tdengine -s "SELECT TODAY() FROM log.log;"

# 重新导入初始化脚本
docker exec -it tdengine taos -h tdengine -s "SOURCE /home/<USER>/init-taosdb.sql;"
```

**解决方案**:

- 检查 TDengine 配置文件
- 重启 TDengine 服务
- 重新执行初始化脚本

#### Hasura GraphQL 引擎问题

**症状**: GraphQL API 无法访问或查询失败

**排查步骤**:

```bash
# 检查 Hasura 容器状态
docker ps | grep graphql-engine

# 检查 Hasura 健康状态
curl http://localhost:8080/healthz

# 查看 Hasura 日志
docker logs iot_graphql-engine

# 检查数据库连接
docker exec -it iot_graphql-engine curl http://localhost:8080/v1/version

# 启动 Hasura 控制台进行调试
# 在 libs/migration.sh 中调用 start_hasura_console 函数
```

**解决方案**:

- 检查数据库连接配置
- 重启 Hasura 服务
- 使用控制台检查元数据

#### 初始化脚本执行失败

**症状**: MinIO 或 Topo 初始化脚本执行失败

**排查步骤**:

```bash
# 检查 MinIO 初始化
docker volume inspect bladex-minio
ls -la $(docker volume inspect --format '{{ .Mountpoint }}' bladex-minio)

# 手动执行 MinIO 初始化
source scripts/init.minio.sh

# 检查 Topo 初始化
docker volume inspect timescale-monitor-image
ls -la $(docker volume inspect --format '{{ .Mountpoint }}' timescale-monitor-image)

# 手动执行 Topo 初始化
./compose_deploy.sh topo init

# 检查 Git 仓库访问
git clone https://gitee.com/bjbigdata/topo-model.git test-clone
```

**解决方案**:

- 检查网络连接和 Git 访问权限
- 确保 Docker 数据卷已正确创建
- 手动执行初始化脚本

### 性能优化建议

#### 系统资源优化

```bash
# 禁用 swap 提高性能（在 init_docker 函数中自动执行）
echo "vm.swappiness=0" >> /etc/sysctl.conf
sysctl -p

# 定期清理 Docker 资源
cd tools/
./docker_clean.sh

# 监控系统资源使用情况
docker stats --no-stream
htop
df -h

# 检查 Docker 磁盘使用
docker system df
```

#### 数据库性能优化

```bash
# 调整 PostgreSQL 配置
vim services/base/config/postgresql.conf

# 重启数据库服务
docker restart base_postgres
docker restart base_business_postgres

# 监控数据库性能
docker exec -it base_postgres psql -U postgres -c "SELECT * FROM pg_stat_activity;"

# 检查数据库连接数
docker exec -it base_postgres psql -U postgres -c "SELECT count(*) FROM pg_stat_activity;"

# 查看数据库大小
docker exec -it base_postgres psql -U postgres -c "SELECT pg_size_pretty(pg_database_size('gis'));"
```

#### 容器资源限制

```bash
# 查看容器资源使用情况
docker stats --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.MemPerc}}"

# 检查 Hasura GraphQL Engine 内存使用（默认限制 10G）
docker exec -it iot_graphql-engine cat /proc/meminfo

# 调整服务资源限制（在对应的 docker-compose.yml 中配置）
# 例如：在 services/iot/docker-compose.yml 中调整 Hasura 内存限制
```

### 日志管理

#### 查看服务日志

```bash
# 查看所有容器日志
docker logs -f container_name

# 查看最近的日志
docker logs --tail 100 container_name

# 查看特定时间段的日志
docker logs --since "2024-01-01T00:00:00" container_name
```

#### 日志清理

```bash
# 清理 Docker 日志
docker system prune --volumes

# 清理应用日志（如果日志过大）
find /var/lib/docker/containers/ -name "*.log" -exec truncate -s 0 {} \;
```

## 📚 相关文档

### 📖 主要文档

- [服务详细说明](./services/README.md) - 各微服务模块的详细配置和说明
- [运维工具使用](./tools/README.md) - 镜像管理、数据恢复等运维工具
- [核心库说明](./libs/README.md) - 部署脚本依赖的核心库函数
- [版本变更日志](./CHANGELOG.md) - 详细的版本更新记录

### 🔧 服务配置文档

- [基础服务说明](./services/base/README.md) - 基础设施服务配置
- [IOT服务说明](./services/iot/README.md) - IOT物联网平台配置
- [MOS服务说明](./services/mos/README.md) - MOS制造运营系统配置
- [GIS服务说明](./services/gis/README.md) - GIS地理信息系统配置
- [MES服务说明](./services/mes/README.md) - MES制造执行系统配置
- [视频服务说明](./services/video/README.md) - 视频监控系统配置
- [IVS智能视频服务说明](./services/ivs/README.md) - 智能视频分析配置
- [工作流服务说明](./services/workflow/README.md) - 工作流引擎配置
- [移动应用服务说明](./services/app/README.md) - 移动应用服务配置
- [监控服务说明](./services/monitoring/README.md) - 系统监控工具配置
- [SSH服务说明](./services/ssh/README.md) - SSH端口转发和调试说明

### 🛠️ 工具和恢复文档

- [镜像管理工具](./tools/README.md#镜像管理工具) - Docker镜像导出导入工具
- [系统清理工具](./tools/README.md#系统清理工具) - Docker资源清理工具
- [数据库恢复说明](./tools/restore/postgres/README.md) - PostgreSQL数据库恢复流程

### 📋 脚本和配置

- [主部署脚本](./compose_deploy.sh) - 核心部署管理脚本
- [版本升级脚本](./upgrade.sh) - 数据卷升级迁移脚本
- [Git拉取脚本](./git_pull.sh) - 代码更新脚本
- [MinIO初始化脚本](./scripts/init.minio.sh) - MinIO资源初始化
- [Topo初始化脚本](./scripts/init.topo.sh) - Topo模型库初始化

## 🤝 技术支持

如遇到技术问题，请按以下步骤处理：

1. 查看相关文档和日志
2. 检查系统资源和网络连接
3. 联系技术支持团队
