# 微服务部署管理工程

本工程提供了三种不同的微服务部署管理方案，分别适用于不同的部署场景和管理需求。每种实现都有其独特的优势和适用场景。

## 🏗️ 一、架构概览

```text
./
├── compose_deploy/     # Docker Compose 方式 - 传统多文件部署
├── simple_deploy/      # 单文件程序 - 类似 PocketBase 的统一管理
└── swarm_deploy/       # Docker Swarm 方式 - 集群化部署
```

## 📋 二、三种实现对比

| 特性 | Docker Compose | 单文件程序 | Docker Swarm |
|------|----------------|------------|--------------|
| **部署复杂度** | 中等 | 简单 | 复杂 |
| **管理界面** | 命令行 | Web界面 | 命令行 |
| **配置管理** | 多文件 | 数据库统一 | Stack配置 |
| **扩展性** | 单机 | 单机 | 集群 |
| **学习成本** | 低 | 低 | 高 |
| **适用场景** | 开发/测试 | 小型生产 | 大型生产 |

---

## 🐳 三、Docker Compose 实现 (`compose_deploy/`)

### 3.1 特点

- **成熟稳定**：基于传统的 Docker Compose 多文件部署方案
- **功能完整**：包含完整的微服务生态系统
- **脚本化管理**：通过 Shell 脚本实现自动化部署和运维

### 3.2 技术架构

- **部署工具**：Docker Compose + Shell 脚本
- **配置管理**：环境变量文件 (`.env`) + 配置文件
- **服务编排**：多个 `docker-compose.yml` 文件
- **数据库**：PostgreSQL + Redis + InfluxDB + TDengine

### 3.3 核心组件

```text
compose_deploy/
├── 🚀 compose_deploy.sh          # 主部署脚本
├── 📋 CHANGELOG.md               # 版本变更日志
├── 🔄 upgrade.sh                 # 版本升级脚本
├── 🔄 git_pull.sh                # Git代码拉取脚本
├── 📂 config/                    # 全局配置目录
│   └── .config.env               # 全局配置文件
├── 📂 libs/                      # 核心库脚本
│   ├── utils.sh                  # 工具函数库
│   ├── service.sh                # 服务操作封装
│   ├── docker.sh                 # Docker管理工具
│   ├── database.sh               # 数据库备份工具
│   ├── git.sh                    # Git版本管理
│   ├── psql-executor.sh          # 数据库执行器
│   ├── migration.sh              # 数据迁移工具
│   └── README.md                 # 核心库说明
├── 📂 services/                  # 微服务目录
│   ├── base/                     # 基础服务（数据库、缓存等）
│   ├── mos/                      # MOS基座后管系统
│   ├── mes/                      # MES业务前端系统
│   ├── iot/                      # IoT物联网服务
│   ├── gis/                      # GIS地理信息系统
│   ├── video/                    # 视频监控服务
│   ├── workflow/                 # 工作流服务
│   ├── app/                      # 移动应用服务
│   ├── ssh/                      # SSH远程访问服务
│   ├── monitoring/               # 监控工具服务
│   └── ivs/                      # IVS智能视频服务
├── 📂 tools/                     # 运维工具
│   ├── images_save.sh            # 镜像批量导出工具（含清理功能）
│   ├── images_load_one.sh        # 镜像导入工具
│   ├── images_save_one.sh        # 单文件镜像保存工具
│   ├── docker_clean.sh           # Docker清理工具
│   ├── harbor-login-deploy.sh.x  # Harbor 1.x认证工具
│   ├── harbor2-login-deploy.sh.x # Harbor 2.x认证工具
│   ├── restore/                  # 数据恢复工具
│   └── README.md                 # 运维工具说明
├── 📂 scripts/                   # 初始化脚本
│   ├── init.minio.sh             # MinIO初始化脚本
│   ├── init.topo.sh              # Topo拓扑图初始化脚本
│   └── minio/                    # MinIO相关资源
└── 📂 others/                    # 其他配置文件
    ├── dismissed-tables.yaml     # 已废弃表配置
    ├── settings.js               # 系统设置
    └── moni_temp/                # 监控模板
```

### 3.4 快速开始

```bash
cd compose_deploy/
# 1. 修改配置
vim config/.config.env
# 2. 初始化部署
./compose_deploy.sh deploy -i
# 3. 启动服务
./compose_deploy.sh up
# 4. 查看服务状态
docker ps
# 5. 访问系统
# 浏览器访问: http://your-server-ip
```

### 3.5 核心功能特性

#### 🔧 3.5.1 主要管理命令

- **部署管理**: `deploy -i` 全新环境部署
- **服务控制**: `start/stop/restart` 服务生命周期管理
- **版本升级**: `update [-r] [-g -t <tag>]` 支持滚动升级
- **数据备份**: `backup` 手动备份，`enable-auto-backup` 自动备份
- **系统清理**: `prune` 彻底清理所有数据

#### 🛠️ 3.5.2 子服务管理

- **配置管理**: `config check` 环境配置检查
- **SSH服务**: `ssh start/stop` SSH调试服务管理
- **Nginx服务**: `nginx rebuild` 前端网关重建
- **Topo服务**: `topo init` 拓扑图数据初始化
- **Docker管理**: `docker init/pull` Docker环境和镜像管理

#### 📊 3.5.3 运维工具集

- **镜像管理**: 批量导出导入Docker镜像，支持离线部署
  - `images_save.sh` - 批量导出所有镜像到独立文件（含自动清理功能）
  - `images_save_one.sh` - 一键导出所有镜像到单个文件
  - `images_load_one.sh` - 从tar文件导入镜像
- **数据恢复**: PostgreSQL数据库备份恢复工具
  - 支持GIS和MES数据库的备份恢复
  - 版本升级SQL脚本管理
  - 灾难恢复流程
- **系统清理**: Docker资源清理，释放磁盘空间
  - `docker_clean.sh` - 清理退出容器、无用镜像、悬空卷
- **代码管理**: Git代码拉取和版本管理
  - 支持版本标签切换
  - 本地修改保护和恢复
- **认证工具**: Harbor镜像仓库自动登录
  - `harbor-login-deploy.sh.x` - Harbor 1.x版本支持
  - `harbor2-login-deploy.sh.x` - Harbor 2.x版本支持
- **初始化脚本**:
  - `init.minio.sh` - MinIO对象存储资源初始化
  - `init.topo.sh` - Topo拓扑图模型库初始化

#### 🔧 3.5.4 核心库功能

- **服务管理**: 完整的微服务生命周期管理
- **Docker管理**: 网络、数据卷、镜像的统一管理
- **数据库工具**: PostgreSQL和TDengine的连接、执行、版本管理
- **迁移工具**: Hasura GraphQL控制台和数据库迁移
- **系统检查**: 环境要求验证和配置检查

### 3.6 适用场景

- 开发和测试环境
- 传统运维团队
- 需要精细化控制的场景
- 已有 Docker Compose 经验的团队

---

## 🚀 四、单文件程序实现 (`simple_deploy/`)

### 4.1 特点

- **单文件部署**：类似 PocketBase，一个可执行文件包含所有功能
- **Web管理界面**：现代化的 Web UI，支持实时监控
- **统一配置管理**：所有配置存储在 SQLite 数据库中
- **动态服务编排**：支持服务的动态启停和依赖管理

### 4.2 技术架构

- **后端**：Go + Gin + GORM + SQLite
- **前端**：Vue.js + Element Plus
- **容器管理**：Docker Engine API
- **数据存储**：SQLite 数据库

### 4.3 架构设计

```
┌─────────────────────────────────────┐
│         Web UI (Vue.js)             │
├─────────────────────────────────────┤
│         REST API (Gin)              │
├─────────────────────────────────────┤
│  Service Manager │ Config Manager   │
│  Container Mgr   │ Dependency Mgr   │
├─────────────────────────────────────┤
│        Docker Engine API            │
├─────────────────────────────────────┤
│      SQLite Database (GORM)         │
└─────────────────────────────────────┘
```

### 4.4 核心功能

- ✅ 服务启动/停止/重启
- ✅ 服务组批量操作  
- ✅ 实时状态监控
- ✅ 服务日志查看
- ✅ 依赖关系管理
- ✅ 配置模板支持
- ✅ 从 Docker Compose 导入配置

### 4.5 快速开始

```bash
cd simple_deploy/

# 方式一：使用快速启动脚本
./quick-start.sh

# 方式二：手动构建
go build -o simple_deploy cmd/main.go
./simple_deploy import ../compose_deploy  # 导入现有配置
./simple_deploy serve                     # 启动Web服务

# 访问 http://localhost:8080
```

### 4.6 适用场景

- 小型生产环境
- 需要图形化管理界面的场景
- 简化运维复杂度的需求
- 快速原型和演示

---

## ⚡ 五、Docker Swarm 实现 (`swarm_deploy/`)

### 5.1 特点

- **集群化部署**：支持多节点集群部署
- **高可用性**：内置服务发现和负载均衡
- **滚动更新**：支持零停机更新
- **资源调度**：自动化的资源分配和调度

### 5.2 技术架构

- **编排工具**：Docker Swarm Mode
- **网络**：Overlay 网络
- **配置管理**：Docker Config + Secret
- **服务发现**：内置 DNS 服务发现

### 5.3 核心组件

```text
swarm_deploy/
├── compose_deploy_swarm.sh      # Swarm部署脚本
├── swarm_config.sh           # 配置管理
├── docker-stack_base.yml     # 基础服务Stack
└── docker-stack_monitoring.yml # 监控服务Stack
```

### 5.4 主要功能

- Swarm 集群初始化
- Stack 部署和管理
- 滚动更新支持
- 配置版本化管理
- 服务健康检查

### 5.5 快速开始

```bash
cd swarm_deploy/
# 1. 初始化Swarm集群
./compose_deploy_swarm.sh init

# 2. 部署基础服务
./compose_deploy_swarm.sh deploy base docker-stack_base.yml

# 3. 部署监控服务
./compose_deploy_swarm.sh deploy monitoring docker-stack_monitoring.yml
```

### 5.6 适用场景

- 大型生产环境
- 需要高可用性的场景
- 多节点集群部署
- 企业级应用

---

## 🎯 六、选择建议

### 6.1 选择 Docker Compose 如果

- 团队熟悉传统运维方式
- 需要精细化控制每个服务
- 开发测试环境
- 单机部署场景

### 6.2 选择单文件程序如果

- 希望简化运维复杂度
- 需要图形化管理界面
- 小型生产环境
- 快速部署和演示

### 6.3 选择 Docker Swarm 如果

- 需要集群化部署
- 要求高可用性
- 大型生产环境
- 有容器编排经验

## 🔧 七、通用要求

### 7.1 系统要求

- Docker Engine 20.10.14+
- Docker Compose 1.27.4+
- Git 2.24+
- Linux/macOS 系统

### 7.2 网络端口

- **5432**: PostgreSQL 数据库
- **6379**: Redis 缓存
- **5672**: RabbitMQ 消息队列
- **8001**: 前端管理界面
- **8080**: Simple Deploy Web界面

---

## 📚 八、更多信息

每个实现目录都包含详细的 README.md 文件，请查看具体实现的文档获取更多详细信息：

- [Docker Compose 实现详情](./compose_deploy/README.md)
- [单文件程序实现详情](./simple_deploy/README.md)
- [Docker Swarm 实现详情](./swarm_deploy/)

## 🤝 九、贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目。

## 📄 十、许可证

MIT License
