#!/bin/bash

# ====================================================
# 部署路径配置
# ====================================================
# DEPLOY_PATH 从 ../compose_deploy.sh 读取  (部分在docker.sh中使用)
# shellcheck disable=SC2034
BASE_SERVICE_PATH="$DEPLOY_PATH/services/base"
MOS_SERVICE_PATH="$DEPLOY_PATH/services/mos"
IOT_SERVICE_PATH="$DEPLOY_PATH/services/iot"
WORK_FLOW_PATH="$DEPLOY_PATH/services/workflow"
MES_SERVICE_PATH="$DEPLOY_PATH/services/mes"
GIS_SERVICE_PATH="$DEPLOY_PATH/services/gis"
SSH_SERVICE_PATH="$DEPLOY_PATH/services/ssh"
APP_SERVICE_PATH="$DEPLOY_PATH/services/app"
VIDEO_SERVICE_PATH="$DEPLOY_PATH/services/video"

# 数据库备份路径
BACKUP_DB_PATH="$BACKUP_BASE_PATH/db"

# ====================================================
# 系统最低要求配置
# ====================================================
MIN_DOCKER_VERSION='25.0.0'            # 最低Docker版本要求
MIN_DOCKER_COMPOSE_VERSION='2.24.0'    # 最低Docker Compose版本要求
MIN_RAM=6144                           # 最低内存要求(MB)

# ====================================================
# Docker配置
# ====================================================
# Docker网络配置
DOCKER_NETWORKS=("$DOCKER_NETWORK")

# Docker基础镜像
DOCKER_BUSY_BOX_IMAGE=harbor2.qdbdtd.com:8088/middleware/busybox:1.32

# Docker命名卷列表
DOCKER_VOLUMES=(
  "base-red-node"
  "base-redis"
  "base-rabbitmq"
  "base-postgres"
  "base-business-postgres"
  "base-opengauss"
  "base-opengauss-backup"
  "base-opengauss-backup-log"
  "base-business-opengauss"
  "base-business-opengauss-backup"
  "base-business-opengauss-backup-log"
  "base-influxdb"
  "base-tdengine-etc"
  "base-tdengine-data"
  "base-tdengine-log"
  "bladex-minio"
  "base-sftpgo-data"
  "base-sftpgo-home"
  "data-monitor-image"
  "data-monitor-log"
  "data-storage-log"
  "data-warn-log"
  "converter-monitor-log"
  "gis-biz-log"
  "mes-biz-log"
  "video-redis"
  "base-workflow"
  "nacos-log"
)

# 需要备份的 Docker 卷
DOCKER_BACKUP_VOLUMES=(
  "base-red-node"
  "base-redis"
  "video-redis"
  "base-postgres"
  "base-business-postgres"
  "base-opengauss"
  "base-business-opengauss"
  "base-tdengine-data"
  "base-ftp"
  "base-sftpgo-data"
  "base-sftpgo-home"
  "bladex-minio"
  "data-monitor-image"
)

# ====================================================
# 终端颜色配置
# ====================================================
COLOR_NONE='\033[0m'      # 重置颜色
COLOR_RED='\033[0;31m'    # 错误红色
COLOR_GREEN='\033[0;32m'  # 成功绿色
COLOR_BLUE='\033[1;34m'   # 信息蓝色
COLOR_YELLOW='\033[1;33m' # 警告黄色

# ====================================================
# 实用函数
# ====================================================

#-------------------------------------------------------------------------------
# 函数名: echo_error
# 功能: 输出错误信息
# 参数:
#   $1: 错误消息
# 返回: 无
#-------------------------------------------------------------------------------
echo_error () {
  echo -e "${COLOR_RED}[ERROR]: $1${COLOR_NONE}"
}

#-------------------------------------------------------------------------------
# 函数名: echo_ok
# 功能: 输出成功信息
# 参数:
#   $1: 成功消息
# 返回: 无
#-------------------------------------------------------------------------------
echo_ok () {
  echo -e "${COLOR_GREEN}[OK]: $1${COLOR_NONE}"
}

#-------------------------------------------------------------------------------
# 函数名: echo_yellow
# 功能: 输出警告/提示信息
# 参数:
#   $1: 警告消息
# 返回: 无
#-------------------------------------------------------------------------------
echo_yellow () {
  echo -e "${COLOR_YELLOW}$1${COLOR_NONE}"
}

# ====================================================
# 版本比较工具函数
# ====================================================

#-------------------------------------------------------------------------------
# 函数名: ver
# 功能: 将版本号转换为可比较的数字
# 参数:
#   $@: 版本字符串 (如 "1.2.3")
# 返回: 数字格式的版本号 (如 1002003)
#-------------------------------------------------------------------------------
function ver () {
  echo "$@" | awk -F. '{ printf("%d%03d%03d", $1,$2,$3); }'
}

#-------------------------------------------------------------------------------
# 函数名: index_of
# 功能: 在数组中查找元素的索引
# 参数:
#   $1: 要查找的值
#   $@: 数组元素
# 返回: 元素的索引或-1(未找到)
#-------------------------------------------------------------------------------
index_of () {
  local value="$1"
  shift
  local arr=("$@")
  for i in "${!arr[@]}"; do
    if [[ "${arr[$i]}" = "${value}" ]]; then
      echo "$i"
      return 0
    fi
  done
  echo -1
}

#-------------------------------------------------------------------------------
# 函数名: compare_version
# 功能: 比较两个版本号
# 参数:
#   $1: 版本A
#   $2: 版本B
# 返回: 0(相等), 1(A>B), -1(A<B)
#-------------------------------------------------------------------------------
compare_version () {
  local a=$1 b=$2
  if [[ "$a" == "$b" ]]; then
    echo 0
    return
  fi
  local arr=("$@")
  IFS=$'\n'
  sorted=($(sort -V <<<"${arr[*]}"))
  unset IFS
  for i in "${!sorted[@]}"; do
    if [[ "${sorted[$i]}" = "$a" ]]; then
      local ai="$i"
    elif [[ "${sorted[$i]}" = "$b" ]]; then
      local bi="$i"
    fi
  done
  if [[ ai -gt bi ]]; then
    echo 1
  else
    echo -1
  fi
}

# ====================================================
# 系统检查函数
# ====================================================

#-------------------------------------------------------------------------------
# 函数名: check_system
# 功能: 检查系统是否符合最低要求
# 参数: 无
# 返回: 无
#-------------------------------------------------------------------------------
check_system () {
  echo_yellow "正在检查系统"

  # 获取Docker版本
  DOCKER_VERSION=$(docker version --format '{{.Server.Version}}' 2>/dev/null || docker --version | sed 's/Docker version \([^,]*\).*/\1/')

  # 获取Docker Compose版本
  DOCKER_COMPOSE_VERSION=$(docker compose version --short 2>/dev/null || docker compose --version 2>/dev/null | sed 's/.*version \([v]*[0-9][0-9.]*\).*/\1/' | sed 's/^v//' || echo "")

  # 获取Docker可用内存 - 优先使用系统内存，然后尝试Docker容器检测
  RAM_AVAILABLE_IN_DOCKER=$(free -m 2>/dev/null | awk '/Mem/ {print $2}' || docker run --rm "$DOCKER_BUSY_BOX_IMAGE" free -m 2>/dev/null | awk '/Mem/ {print $2}' || echo "")

  # 检查 Docker 版本
  if [[ -n "$DOCKER_VERSION" ]]; then
    if [[ "$(ver "$DOCKER_VERSION")" -lt "$(ver "$MIN_DOCKER_VERSION")" ]]; then
      echo_error "期望最低 Docker 版本为 $MIN_DOCKER_VERSION，但当前版本为 $DOCKER_VERSION"
      echo_yellow "是否继续？（输入 yes 继续）"
      read; if [[ "$REPLY" != yes ]]; then exit 1; fi
    else
      echo_ok "Docker版本检查通过: $DOCKER_VERSION"
    fi
  else
    echo_error "无法获取 Docker 版本信息，请确保 Docker 已正确安装并运行"
    echo_yellow "是否继续？（输入 yes 继续）"
    read; if [[ "$REPLY" != yes ]]; then exit 1; fi
  fi

  # 检查 Docker Compose 版本
  if [[ -n "$DOCKER_COMPOSE_VERSION" ]]; then
    if [[ "$(ver "$DOCKER_COMPOSE_VERSION")" -lt "$(ver "$MIN_DOCKER_COMPOSE_VERSION")" ]]; then
      echo_error "期望最低 Docker Compose 版本为 $MIN_DOCKER_COMPOSE_VERSION，但当前版本为 $DOCKER_COMPOSE_VERSION"
      echo_yellow "是否继续？（输入 yes 继续）"
      read; if [[ "$REPLY" != yes ]]; then exit 1; fi
    else
      echo_ok "Docker Compose版本检查通过: $DOCKER_COMPOSE_VERSION"
    fi
  else
    echo_error "无法获取 Docker Compose 版本信息，请确保 Docker Compose 已正确安装"
    echo_yellow "是否继续？（输入 yes 继续）"
    read; if [[ "$REPLY" != yes ]]; then exit 1; fi
  fi

  # 检查内存
  if [[ -n "$RAM_AVAILABLE_IN_DOCKER" && "$RAM_AVAILABLE_IN_DOCKER" -gt 0 ]]; then
    if [[ "$RAM_AVAILABLE_IN_DOCKER" -lt "$MIN_RAM" ]]; then
      echo_error "期望Docker可用内存至少为 $MIN_RAM MB，但当前只有 $RAM_AVAILABLE_IN_DOCKER MB"
      echo_yellow "是否继续？（输入 yes 继续）"
      read; if [[ "$REPLY" != yes ]]; then exit 1; fi
    else
      echo_ok "内存检查通过: ${RAM_AVAILABLE_IN_DOCKER}MB 可用"
    fi
  else
    echo_error "无法获取 Docker 可用内存信息"
    echo_yellow "是否继续？（输入 yes 继续）"
    read; if [[ "$REPLY" != yes ]]; then exit 1; fi
  fi
}

#-------------------------------------------------------------------------------
# 函数名: check_config_env
# 功能: 检查环境配置的完整性和正确性
# 参数: 无
# 返回: 无（检查失败时直接退出程序）
# 说明: 综合检查部署机构 ID、CMS上游服务、授权服务等关键配置项
#       确保部署环境配置满足系统运行要求
#-------------------------------------------------------------------------------
check_config_env () {
  # 检查部署机构 ID
  check_mine_code
  if [ $? -ne 0 ]; then
    echo_error "部署机构 ID 输入错误，应为 6 位数字，请与管理员沟通在 CMS 系统中创建新矿编码"
    exit 1
  fi

  # 检查CMS上游服务 (已启用)
  # check_cms_upstream
  # if [ $? -ne 0 ]; then
  #   echo_error "CMS Upstream 连接失败，请检查是否配置是否正确. $UPSTREAM_CMS"
  #   exit 1
  # fi

  # 检查授权服务
  check_auth_service
  if [ $? -ne 0 ]; then
    echo_error "缺少授权配置信息，请联系管理员在CMS中配置. AUTH_SERVICE_NAME, AUTH_PROJECT_NAME"
    exit 1
  fi
}

#-------------------------------------------------------------------------------
# 函数名: check_mine_code
# 功能: 检查部署机构 ID格式
# 参数: 无
# 返回: 无（通过返回值表示检查结果，0表示成功，1表示失败）
#-------------------------------------------------------------------------------
check_mine_code () {
  echo_yellow "正在检查部署机构 ID"
  echo "部署机构 ID: $MINECODE"
  if [ "$DEPLOY_MOS_SERVICE" = false ]; then return 0; fi
  [[ "$MINECODE" =~ ^[0-9]{6}$ ]] && return 0 || return 1
}

#-------------------------------------------------------------------------------
# 函数名: check_cms_upstream
# 功能: 检查CMS上游服务
# 参数: 无
# 返回: 无（通过返回值表示检查结果，0表示成功，1表示失败）
#-------------------------------------------------------------------------------
check_cms_upstream () {
  echo_yellow "正在检查 CMS Upstream"
  echo "CMS Upstream: $UPSTREAM_CMS"
  curl -s "$UPSTREAM_CMS" > /dev/null && return 0 || return 1
}

#-------------------------------------------------------------------------------
# 函数名: check_auth_service
# 功能: 检查授权服务配置是否完整
# 参数: 无
# 返回: 0(配置完整), 1(配置缺失)
# 说明: 验证 AUTH_SERVICE_NAME 和 AUTH_PROJECT_NAME 环境变量是否已设置
#-------------------------------------------------------------------------------
check_auth_service () {
  echo_yellow "正在检查授权服务名称 AUTH_SERVICE_NAME, AUTH_PROJECT_NAME"
  if [ -z "$AUTH_SERVICE_NAME" ] || [ -z "$AUTH_PROJECT_NAME" ]; then
    return 1
  fi
  return 0
}
