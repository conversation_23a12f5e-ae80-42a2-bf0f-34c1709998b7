{"protocol": "sftp", "port": 22, "remotePath": "/home/<USER>", "uploadOnSave": false, "useTempFile": true, "openSsh": false, "ignore": [".vscode", "server_setups", "simple_deploy", "swarm_deploy", "README.md", "Rules.md"], "syncOption": {"delete": true, "update": true}, "profiles": {"dev": {"name": "jnnyj-dev", "host": "***********", "username": "root", "privateKeyPath": "D:/Users/<USER>/.ssh/id_rsa_bdtd", "remotePath": "/home/<USER>"}, "test": {"name": "nixos-test", "host": "***************", "username": "purez", "privateKeyPath": "D:/Users/<USER>/.ssh/id_rsa_bdtd", "remotePath": "/home/<USER>/workspace/compose_deploy"}}, "defaultProfile": "dev"}