#!/bin/bash

#===============================================================================
# Git 操作工具库
#===============================================================================
# 功能描述: 提供 Git 版本管理和部署材料更新功能
# 依赖模块: utils.sh (echo_error, echo_yellow, echo_ok 函数)
#===============================================================================

#-------------------------------------------------------------------------------
# 函数名: git_update
# 功能: 更新部署材料到指定的 Git 标签版本
# 参数: $1 - Git 标签名称
# 返回: 0 - 成功，1 - 失败
#
# 工作流程:
#   1. 验证标签参数
#   2. 从远程仓库获取最新信息
#   3. 检查指定标签是否存在
#   4. 保存本地修改（如果有）
#   5. 切换到指定标签
#   6. 恢复本地修改（如果之前有保存）
#-------------------------------------------------------------------------------
git_update() {
  local tag=$1
  local found_tag
  local git_stash=0

  # 参数验证：检查标签参数是否为空
  if [ -z "$tag" ]; then
    echo_error "git checkout 缺少版本号"
    return 1
  fi

  echo "[GIT]: 正在更新部署材料..."

  # 从远程仓库获取最新信息
  if ! git fetch; then
    echo_error "git fetch 失败，请检查网络连接"
    return 1
  fi

  # 检查指定标签是否存在
  found_tag=$(git tag -l "$tag" | wc -l)
  if [ $found_tag -eq 0 ]; then
    echo_error "无法找到版本 $tag，请联系管理员"
    return 1
  fi

  # 检查工作区是否有未提交的修改
  git diff-index --quiet HEAD --
  if [ $? -ne 0 ]; then
    echo_yellow "[GIT]: 检测到本地文件修改"

    # 暂存本地修改
    if git stash; then
      git_stash=1
      echo_ok "git stash 成功"
    else
      echo_error "git stash 失败"
      return 1
    fi
  fi

  # 切换到指定标签
  if git checkout "$tag"; then
    echo_ok "git checkout 成功"

    # 如果之前有暂存修改，则恢复
    if [ $git_stash -eq 1 ]; then
      if git stash pop; then
        echo_ok "git stash pop 成功"
        return 0
      else
        echo_error "git stash pop 失败，请联系管理员"
        return 1
      fi
    fi
    return 0
  else
    # checkout 失败时，尝试恢复暂存的修改
    if [ $git_stash -eq 1 ]; then
      git stash pop
    fi
    echo_error "git checkout 失败，请联系管理员"
    return 1
  fi
}

