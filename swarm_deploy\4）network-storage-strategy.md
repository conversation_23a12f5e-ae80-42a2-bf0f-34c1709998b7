# 4）Docker Swarm 网络和存储策略

## 🌐 网络架构设计

### 网络拓扑结构

基于您选择的服务栈，设计了分层网络架构：

```mermaid
graph TB
    subgraph "外部网络层"
        Internet[互联网]
        Users[用户终端]
        Admin[管理员]
    end
    
    subgraph "负载均衡层"
        LB[外部负载均衡器]
        Firewall[防火墙]
    end
    
    subgraph "Docker Swarm 集群"
        subgraph "Ingress 网络 (ingress)"
            IngressLB[Swarm 内置负载均衡]
        end
        
        subgraph "前端网络 (frontend-net)"
            Gateway[前端网关 :80]
            WebUI[Web 界面]
        end
        
        subgraph "业务网络 (middle)"
            subgraph "认证层"
                Auth[授权服务 :22260]
                Nacos[配置中心 :8848]
            end
            
            subgraph "应用层"
                Core[核心服务]
                IoT[IoT 服务]
                Monitor[监控服务]
            end
            
            subgraph "数据层"
                PG[(PostgreSQL :5432)]
                Redis[(Redis :6379)]
                RabbitMQ[(RabbitMQ :5672)]
                TDengine[(TDengine :6030)]
                MinIO[(MinIO :9000)]
            end
        end
        
        subgraph "管理网络 (admin-net)"
            SSH[SSH 服务 :2222]
            Portainer[Portainer :9000]
            Grafana[Grafana :3000]
        end
    end
    
    Internet --> Firewall
    Users --> Firewall
    Admin --> Firewall
    Firewall --> LB
    LB --> IngressLB
    IngressLB --> Gateway
    Gateway --> Auth
    Auth --> Core
    Core --> IoT
    IoT --> PG
    IoT --> Redis
    IoT --> RabbitMQ
    IoT --> TDengine
    Core --> MinIO
    Admin --> SSH
    Admin --> Portainer
    Admin --> Grafana
```

### 网络配置详情

#### 1. **ingress 网络** (内置)
```yaml
# Docker Swarm 内置 ingress 网络
# 自动创建，用于外部流量路由
networks:
  ingress:
    external: true
    name: ingress
```

**特点**:
- Swarm 内置网络，自动创建
- 处理外部流量到服务的路由
- 支持负载均衡和服务发现
- 加密传输（默认启用）

#### 2. **middle 网络** (主业务网络)
```yaml
networks:
  middle:
    driver: overlay
    driver_opts:
      encrypted: "true"
    attachable: true
    ipam:
      driver: default
      config:
        - subnet: ********/24
          gateway: ********
    labels:
      - "network.description=主业务网络"
      - "network.environment=production"
```

**特点**:
- 所有业务服务的主要通信网络
- 启用加密传输
- 支持服务发现和负载均衡
- 可附加到外部容器（用于调试）

#### 3. **admin-net 网络** (管理网络)
```yaml
networks:
  admin-net:
    driver: overlay
    driver_opts:
      encrypted: "true"
    attachable: false
    ipam:
      driver: default
      config:
        - subnet: ********/24
          gateway: ********
    labels:
      - "network.description=管理网络"
      - "network.access=admin-only"
```

**特点**:
- 管理和监控服务专用网络
- 更严格的访问控制
- 不允许外部容器附加
- 独立的 IP 地址段

#### 4. **monitoring-net 网络** (监控网络)
```yaml
networks:
  monitoring-net:
    driver: overlay
    driver_opts:
      encrypted: "true"
    attachable: true
    ipam:
      driver: default
      config:
        - subnet: ********/24
          gateway: ********
    labels:
      - "network.description=监控网络"
      - "network.purpose=metrics-collection"
```

**特点**:
- 监控数据收集专用网络
- 连接所有需要监控的服务
- 支持 Prometheus 服务发现
- 优化监控数据传输

### 网络安全策略

#### 防火墙规则
```bash
#!/bin/bash
# firewall_rules.sh - 网络安全规则配置

# 基础规则
iptables -P INPUT DROP
iptables -P FORWARD DROP
iptables -P OUTPUT ACCEPT

# 允许本地回环
iptables -A INPUT -i lo -j ACCEPT
iptables -A OUTPUT -o lo -j ACCEPT

# 允许已建立的连接
iptables -A INPUT -m state --state ESTABLISHED,RELATED -j ACCEPT

# SSH 访问（限制 IP）
iptables -A INPUT -p tcp --dport 2222 -s ***********/24 -j ACCEPT

# HTTP/HTTPS 访问
iptables -A INPUT -p tcp --dport 80 -j ACCEPT
iptables -A INPUT -p tcp --dport 443 -j ACCEPT

# Docker Swarm 端口
iptables -A INPUT -p tcp --dport 2377 -s 10.0.0.0/8 -j ACCEPT  # 集群管理
iptables -A INPUT -p tcp --dport 7946 -s 10.0.0.0/8 -j ACCEPT  # 节点通信
iptables -A INPUT -p udp --dport 7946 -s 10.0.0.0/8 -j ACCEPT  # 节点通信
iptables -A INPUT -p udp --dport 4789 -s 10.0.0.0/8 -j ACCEPT  # Overlay 网络

# 管理端口（限制访问）
iptables -A INPUT -p tcp --dport 3000 -s ***********/24 -j ACCEPT  # Grafana
iptables -A INPUT -p tcp --dport 9000 -s ***********/24 -j ACCEPT  # Portainer
iptables -A INPUT -p tcp --dport 9090 -s ***********/24 -j ACCEPT  # Prometheus

# 拒绝其他连接
iptables -A INPUT -j DROP
```

#### 网络隔离策略
```yaml
# 网络访问控制标签
services:
  # 数据库服务 - 仅内部访问
  postgres:
    networks:
      - middle
    deploy:
      labels:
        - "network.access=internal-only"
        - "network.expose=false"
  
  # 前端网关 - 公开访问
  frontend-gateway:
    networks:
      - middle
      - ingress
    ports:
      - "80:80"
    deploy:
      labels:
        - "network.access=public"
        - "network.expose=true"
  
  # 管理服务 - 受限访问
  grafana:
    networks:
      - admin-net
      - monitoring-net
    ports:
      - target: 3000
        published: 3000
        mode: host
    deploy:
      labels:
        - "network.access=admin-only"
        - "network.expose=restricted"
```

## 💾 存储架构设计

### 存储分层策略

```mermaid
graph TB
    subgraph "存储层次结构"
        subgraph "热数据存储 (SSD)"
            A[数据库数据卷]
            B[缓存数据卷]
            C[应用日志卷]
        end
        
        subgraph "温数据存储 (SAS)"
            D[监控数据卷]
            E[备份数据卷]
            F[静态资源卷]
        end
        
        subgraph "冷数据存储 (SATA)"
            G[归档日志卷]
            H[历史备份卷]
            I[大文件存储卷]
        end
    end
    
    subgraph "存储节点分布"
        subgraph "Manager 节点"
            M1[Manager-1<br/>关键数据]
            M2[Manager-2<br/>配置备份]
            M3[Manager-3<br/>监控数据]
        end
        
        subgraph "Worker 节点"
            W1[Worker-1<br/>应用数据]
            W2[Worker-2<br/>缓存数据]
            W3[Worker-3<br/>日志数据]
        end
    end
    
    A --> M1
    B --> W2
    C --> W3
    D --> M3
    E --> M2
    F --> W1
    G --> H
    H --> I
```

### 数据卷配置

#### 🗄️ 数据库存储卷
```yaml
volumes:
  # PostgreSQL 主数据库
  base-postgres:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /data/postgres/main
    labels:
      - "volume.type=database"
      - "volume.backup=daily"
      - "volume.retention=30d"
      - "volume.size=50GB"
      - "volume.performance=high"

  # PostgreSQL 业务数据库
  base-business-postgres:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /data/postgres/business
    labels:
      - "volume.type=database"
      - "volume.backup=daily"
      - "volume.retention=30d"
      - "volume.size=50GB"
      - "volume.performance=high"

  # TDengine 时序数据库
  base-tdengine-data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /data/tdengine/data
    labels:
      - "volume.type=timeseries"
      - "volume.backup=incremental"
      - "volume.retention=90d"
      - "volume.size=100GB"
      - "volume.performance=high"

  base-tdengine-log:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /data/tdengine/logs
    labels:
      - "volume.type=logs"
      - "volume.backup=weekly"
      - "volume.retention=7d"
      - "volume.size=10GB"
      - "volume.performance=medium"
```

#### 🔄 缓存存储卷
```yaml
volumes:
  # Redis 缓存数据
  base-redis:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /data/redis
    labels:
      - "volume.type=cache"
      - "volume.backup=snapshot"
      - "volume.retention=7d"
      - "volume.size=10GB"
      - "volume.performance=high"

  # RabbitMQ 消息数据
  base-rabbitmq:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /data/rabbitmq
    labels:
      - "volume.type=queue"
      - "volume.backup=config"
      - "volume.retention=7d"
      - "volume.size=10GB"
      - "volume.performance=medium"
```

#### 📦 对象存储卷
```yaml
volumes:
  # MinIO 对象存储
  bladex-minio:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /data/minio
    labels:
      - "volume.type=object-storage"
      - "volume.backup=distributed"
      - "volume.retention=365d"
      - "volume.size=200GB"
      - "volume.performance=medium"

  # FTP 文件存储
  base-ftp:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /data/ftp
    labels:
      - "volume.type=file-storage"
      - "volume.backup=sync"
      - "volume.retention=30d"
      - "volume.size=50GB"
      - "volume.performance=medium"
```

#### 📊 监控存储卷
```yaml
volumes:
  # Prometheus 监控数据
  monitoring-prometheus-data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /data/prometheus
    labels:
      - "volume.type=metrics"
      - "volume.backup=weekly"
      - "volume.retention=90d"
      - "volume.size=50GB"
      - "volume.performance=medium"

  # Grafana 仪表板数据
  monitoring-grafana-data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /data/grafana
    labels:
      - "volume.type=dashboard"
      - "volume.backup=config"
      - "volume.retention=30d"
      - "volume.size=5GB"
      - "volume.performance=low"

  # Portainer 管理数据
  monitoring-portainer-data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /data/portainer
    labels:
      - "volume.type=management"
      - "volume.backup=config"
      - "volume.retention=30d"
      - "volume.size=2GB"
      - "volume.performance=low"
```

### 存储性能优化

#### 磁盘 I/O 优化
```bash
#!/bin/bash
# storage_optimization.sh - 存储性能优化脚本

# 创建存储目录结构
create_storage_structure() {
    echo "📁 创建存储目录结构..."
    
    # 高性能存储（SSD）
    mkdir -p /data/{postgres,redis,tdengine/data}
    
    # 中等性能存储（SAS）
    mkdir -p /data/{rabbitmq,minio,prometheus}
    
    # 低性能存储（SATA）
    mkdir -p /data/{logs,backups,archives}
    
    # 设置权限
    chown -R 999:999 /data/postgres
    chown -R 999:999 /data/redis
    chown -R 999:999 /data/tdengine
    chown -R 472:472 /data/grafana
    
    echo "✅ 存储目录结构创建完成"
}

# 磁盘性能调优
optimize_disk_performance() {
    echo "⚡ 优化磁盘性能..."
    
    # 设置 I/O 调度器
    echo mq-deadline > /sys/block/sda/queue/scheduler
    
    # 调整读写缓存
    echo 8192 > /sys/block/sda/queue/read_ahead_kb
    
    # 设置文件系统挂载选项
    mount -o remount,noatime,nodiratime /data
    
    # 调整虚拟内存参数
    echo 'vm.swappiness=10' >> /etc/sysctl.conf
    echo 'vm.dirty_ratio=15' >> /etc/sysctl.conf
    echo 'vm.dirty_background_ratio=5' >> /etc/sysctl.conf
    
    sysctl -p
    
    echo "✅ 磁盘性能优化完成"
}

# 存储监控设置
setup_storage_monitoring() {
    echo "📊 设置存储监控..."
    
    # 创建磁盘使用监控脚本
    cat > /usr/local/bin/disk_monitor.sh << 'EOF'
#!/bin/bash
# 磁盘使用率监控

THRESHOLD=85
EMAIL="<EMAIL>"

df -h | grep -vE '^Filesystem|tmpfs|cdrom' | awk '{ print $5 " " $1 }' | while read output;
do
    usage=$(echo $output | awk '{ print $1}' | cut -d'%' -f1)
    partition=$(echo $output | awk '{ print $2 }')
    
    if [ $usage -ge $THRESHOLD ]; then
        echo "警告: 分区 \"$partition\" 使用率达到 $usage%" | \
        mail -s "磁盘空间警告" $EMAIL
    fi
done
EOF
    
    chmod +x /usr/local/bin/disk_monitor.sh
    
    # 添加到 crontab
    echo "*/10 * * * * /usr/local/bin/disk_monitor.sh" | crontab -
    
    echo "✅ 存储监控设置完成"
}

# 执行优化
create_storage_structure
optimize_disk_performance
setup_storage_monitoring
```

### 备份和恢复策略

#### 自动备份脚本
```bash
#!/bin/bash
# backup_strategy.sh - 数据备份策略

BACKUP_BASE="/backups"
DATE=$(date +%Y%m%d_%H%M%S)
RETENTION_DAYS=30

# 数据库备份
backup_databases() {
    echo "🗄️ 开始数据库备份..."
    
    # PostgreSQL 主数据库备份
    docker exec infrastructure_postgres pg_dump -U postgres gis > \
        "$BACKUP_BASE/postgres_main_$DATE.sql"
    
    # PostgreSQL 业务数据库备份
    docker exec infrastructure_business-postgres pg_dump -U mes mes > \
        "$BACKUP_BASE/postgres_business_$DATE.sql"
    
    # Redis 备份
    docker exec infrastructure_redis redis-cli BGSAVE
    docker cp infrastructure_redis:/data/dump.rdb \
        "$BACKUP_BASE/redis_$DATE.rdb"
    
    # TDengine 备份
    docker exec infrastructure_tdengine taosdump -o "$BACKUP_BASE/tdengine_$DATE"
    
    echo "✅ 数据库备份完成"
}

# 配置文件备份
backup_configs() {
    echo "⚙️ 开始配置文件备份..."
    
    # Docker Configs 备份
    mkdir -p "$BACKUP_BASE/configs_$DATE"
    docker config ls --format "{{.Name}}" | while read config; do
        docker config inspect "$config" --format "{{.Spec.Data}}" | \
            base64 -d > "$BACKUP_BASE/configs_$DATE/$config"
    done
    
    # Docker Secrets 备份（仅备份名称列表）
    docker secret ls --format "{{.Name}}" > \
        "$BACKUP_BASE/secrets_list_$DATE.txt"
    
    echo "✅ 配置文件备份完成"
}

# 应用数据备份
backup_application_data() {
    echo "📦 开始应用数据备份..."
    
    # MinIO 数据备份
    tar -czf "$BACKUP_BASE/minio_$DATE.tar.gz" /data/minio/
    
    # 监控数据备份
    tar -czf "$BACKUP_BASE/monitoring_$DATE.tar.gz" \
        /data/prometheus/ /data/grafana/
    
    echo "✅ 应用数据备份完成"
}

# 清理旧备份
cleanup_old_backups() {
    echo "🧹 清理旧备份文件..."
    
    find "$BACKUP_BASE" -name "*.sql" -mtime +$RETENTION_DAYS -delete
    find "$BACKUP_BASE" -name "*.rdb" -mtime +$RETENTION_DAYS -delete
    find "$BACKUP_BASE" -name "*.tar.gz" -mtime +$RETENTION_DAYS -delete
    find "$BACKUP_BASE" -type d -name "configs_*" -mtime +$RETENTION_DAYS -exec rm -rf {} +
    find "$BACKUP_BASE" -type d -name "tdengine_*" -mtime +$RETENTION_DAYS -exec rm -rf {} +
    
    echo "✅ 旧备份清理完成"
}

# 执行备份
mkdir -p "$BACKUP_BASE"
backup_databases
backup_configs
backup_application_data
cleanup_old_backups

echo "🎉 备份任务完成！备份位置: $BACKUP_BASE"
```

#### 恢复脚本
```bash
#!/bin/bash
# restore_strategy.sh - 数据恢复策略

BACKUP_BASE="/backups"

# 数据库恢复
restore_databases() {
    local backup_date=$1
    
    echo "🔄 开始数据库恢复..."
    
    # PostgreSQL 主数据库恢复
    if [ -f "$BACKUP_BASE/postgres_main_$backup_date.sql" ]; then
        docker exec -i infrastructure_postgres psql -U postgres -d gis < \
            "$BACKUP_BASE/postgres_main_$backup_date.sql"
        echo "✅ PostgreSQL 主数据库恢复完成"
    fi
    
    # PostgreSQL 业务数据库恢复
    if [ -f "$BACKUP_BASE/postgres_business_$backup_date.sql" ]; then
        docker exec -i infrastructure_business-postgres psql -U mes -d mes < \
            "$BACKUP_BASE/postgres_business_$backup_date.sql"
        echo "✅ PostgreSQL 业务数据库恢复完成"
    fi
    
    # Redis 恢复
    if [ -f "$BACKUP_BASE/redis_$backup_date.rdb" ]; then
        docker cp "$BACKUP_BASE/redis_$backup_date.rdb" \
            infrastructure_redis:/data/dump.rdb
        docker restart infrastructure_redis
        echo "✅ Redis 数据恢复完成"
    fi
    
    # TDengine 恢复
    if [ -d "$BACKUP_BASE/tdengine_$backup_date" ]; then
        docker exec infrastructure_tdengine taosdump -i "$BACKUP_BASE/tdengine_$backup_date"
        echo "✅ TDengine 数据恢复完成"
    fi
}

# 配置恢复
restore_configs() {
    local backup_date=$1
    
    echo "⚙️ 开始配置恢复..."
    
    if [ -d "$BACKUP_BASE/configs_$backup_date" ]; then
        for config_file in "$BACKUP_BASE/configs_$backup_date"/*; do
            config_name=$(basename "$config_file")
            docker config create "${config_name}_restored" "$config_file"
        done
        echo "✅ 配置文件恢复完成"
    fi
}

# 使用示例
if [ $# -eq 0 ]; then
    echo "用法: $0 <backup_date>"
    echo "示例: $0 20240127_143000"
    exit 1
fi

restore_databases $1
restore_configs $1

echo "🎉 数据恢复完成！"
```

这个网络和存储策略为您的 Docker Swarm 集群提供了完整的基础设施规划，包括分层网络架构、安全策略、存储优化和备份恢复机制，确保系统的高可用性和数据安全性。
