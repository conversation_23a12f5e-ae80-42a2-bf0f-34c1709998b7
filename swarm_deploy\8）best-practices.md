# 8）Docker Swarm 最佳实践指南

## 🎯 部署最佳实践

### 环境规划

#### 1. 硬件资源规划
```bash
# 生产环境推荐配置
Manager 节点 (3个):
- CPU: 8核心
- 内存: 32GB
- 磁盘: 500GB SSD
- 网络: 千兆网卡

Worker 节点 (≥2个):
- CPU: 16核心  
- 内存: 64GB
- 磁盘: 1TB SSD + 2TB HDD
- 网络: 千兆网卡
```

#### 2. 网络规划
```yaml
# 网络分段建议
networks:
  # 前端网络 - 面向用户
  frontend:
    subnet: 10.0.1.0/24
    
  # 业务网络 - 应用服务
  middle:
    subnet: 10.0.2.0/24
    
  # 数据网络 - 数据库服务
  backend:
    subnet: 10.0.3.0/24
    
  # 管理网络 - 运维管理
  admin:
    subnet: 10.0.4.0/24
```

### 服务设计原则

#### 1. 单一职责原则
```yaml
# ✅ 好的设计 - 每个服务职责单一
services:
  auth-service:
    image: auth:latest
    # 只负责用户认证
    
  user-service:
    image: user:latest
    # 只负责用户管理
    
  notification-service:
    image: notification:latest
    # 只负责消息通知

# ❌ 避免的设计 - 单个服务承担多个职责
services:
  monolith-service:
    image: monolith:latest
    # 包含认证、用户管理、通知等多个功能
```

#### 2. 无状态设计
```yaml
# ✅ 无状态服务设计
services:
  api-service:
    image: api:latest
    deploy:
      replicas: 3  # 可以水平扩展
    environment:
      - SESSION_STORE=redis://redis:6379  # 会话存储外部化

# ❌ 有状态设计
services:
  api-service:
    image: api:latest
    deploy:
      replicas: 1  # 无法扩展
    volumes:
      - ./sessions:/app/sessions  # 会话存储在本地
```

#### 3. 健康检查配置
```yaml
services:
  web-service:
    image: web:latest
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s      # 检查间隔
      timeout: 10s       # 超时时间
      retries: 3         # 重试次数
      start_period: 60s  # 启动宽限期
    deploy:
      replicas: 2
      update_config:
        failure_action: rollback  # 失败时自动回滚
        monitor: 60s
```

## 🔐 安全最佳实践

### 密钥管理

#### 1. 密钥分类管理
```bash
# 按敏感级别分类
# 高敏感 - 数据库密码、API 密钥
echo "super_secret_db_password" | docker secret create db_password -

# 中敏感 - 服务间通信密钥
echo "service_communication_key" | docker secret create service_key -

# 低敏感 - 配置参数
docker config create app_config ./config.yml
```

#### 2. 密钥轮换策略
```bash
#!/bin/bash
# 密钥轮换脚本示例

rotate_secret() {
    local secret_name=$1
    local new_value=$2
    
    # 创建新版本密钥
    echo "$new_value" | docker secret create "${secret_name}_new" -
    
    # 更新服务使用新密钥
    docker service update \
        --secret-rm "$secret_name" \
        --secret-add "source=${secret_name}_new,target=/run/secrets/$secret_name" \
        "$service_name"
    
    # 删除旧密钥
    sleep 60
    docker secret rm "$secret_name"
    
    # 重命名新密钥
    docker secret create "$secret_name" < <(echo "$new_value")
    docker secret rm "${secret_name}_new"
}
```

### 网络安全

#### 1. 网络隔离
```yaml
# 网络访问控制
services:
  # 数据库服务 - 仅内部访问
  database:
    networks:
      - backend  # 只连接后端网络
    deploy:
      placement:
        constraints:
          - node.role == manager  # 只在管理节点运行
  
  # API 服务 - 内外网访问
  api:
    networks:
      - frontend  # 连接前端网络
      - backend   # 连接后端网络
    ports:
      - "8080:8080"  # 对外暴露端口
```

#### 2. TLS 加密
```yaml
# 启用 TLS 加密
services:
  nginx:
    image: nginx:latest
    configs:
      - source: nginx_ssl_config
        target: /etc/nginx/nginx.conf
    secrets:
      - source: ssl_certificate
        target: /etc/ssl/certs/server.crt
      - source: ssl_private_key
        target: /etc/ssl/private/server.key
```

## 📊 性能优化

### 资源配置

#### 1. 资源限制和预留
```yaml
services:
  high-cpu-service:
    image: cpu-intensive:latest
    deploy:
      resources:
        limits:
          cpus: '4.0'      # CPU 限制
          memory: 8G       # 内存限制
        reservations:
          cpus: '2.0'      # CPU 预留
          memory: 4G       # 内存预留
```

#### 2. 节点亲和性
```yaml
services:
  database:
    image: postgres:latest
    deploy:
      placement:
        constraints:
          - node.labels.storage == ssd  # 部署到 SSD 节点
          - node.role == manager        # 部署到管理节点
        preferences:
          - spread: node.labels.zone    # 跨可用区分布
```

### 扩缩容策略

#### 1. 水平扩展配置
```yaml
services:
  web-service:
    image: web:latest
    deploy:
      replicas: 3
      update_config:
        parallelism: 1     # 并行更新数量
        delay: 30s         # 更新间隔
        order: start-first # 启动顺序
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
```

#### 2. 自动扩缩容脚本
```bash
#!/bin/bash
# 基于 CPU 使用率的自动扩缩容

SERVICE_NAME="web-service"
MIN_REPLICAS=2
MAX_REPLICAS=10
CPU_THRESHOLD_UP=80
CPU_THRESHOLD_DOWN=30

# 获取当前 CPU 使用率
get_cpu_usage() {
    # 通过 Prometheus API 获取 CPU 使用率
    curl -s "http://prometheus:9090/api/v1/query?query=avg(rate(container_cpu_usage_seconds_total{container_label_com_docker_swarm_service_name=\"$SERVICE_NAME\"}[5m]))*100" | \
    jq -r '.data.result[0].value[1]' 2>/dev/null || echo "0"
}

# 获取当前副本数
get_current_replicas() {
    docker service inspect "$SERVICE_NAME" --format '{{.Spec.Mode.Replicated.Replicas}}'
}

# 扩缩容逻辑
auto_scale() {
    local cpu_usage=$(get_cpu_usage)
    local current_replicas=$(get_current_replicas)
    
    if (( $(echo "$cpu_usage > $CPU_THRESHOLD_UP" | bc -l) )); then
        if [ "$current_replicas" -lt "$MAX_REPLICAS" ]; then
            local new_replicas=$((current_replicas + 1))
            docker service scale "$SERVICE_NAME=$new_replicas"
            echo "扩容: $current_replicas -> $new_replicas (CPU: $cpu_usage%)"
        fi
    elif (( $(echo "$cpu_usage < $CPU_THRESHOLD_DOWN" | bc -l) )); then
        if [ "$current_replicas" -gt "$MIN_REPLICAS" ]; then
            local new_replicas=$((current_replicas - 1))
            docker service scale "$SERVICE_NAME=$new_replicas"
            echo "缩容: $current_replicas -> $new_replicas (CPU: $cpu_usage%)"
        fi
    fi
}
```

## 🔄 运维最佳实践

### 部署策略

#### 1. 滚动更新
```yaml
services:
  app:
    image: app:v2.0
    deploy:
      replicas: 4
      update_config:
        parallelism: 2        # 每次更新 2 个副本
        delay: 30s           # 更新间隔 30 秒
        failure_action: rollback  # 失败时回滚
        monitor: 60s         # 监控时间
        max_failure_ratio: 0.25   # 最大失败率 25%
        order: start-first   # 先启动新副本再停止旧副本
```

#### 2. 蓝绿部署
```bash
#!/bin/bash
# 蓝绿部署脚本

STACK_NAME="myapp"
NEW_VERSION="v2.0"

# 部署绿色环境
deploy_green() {
    # 修改 stack 文件中的镜像版本
    sed "s/{{VERSION}}/green-$NEW_VERSION/g" stack.yml > stack-green.yml
    
    # 部署绿色环境
    docker stack deploy -c stack-green.yml "${STACK_NAME}-green"
    
    # 等待绿色环境就绪
    wait_for_healthy "${STACK_NAME}-green"
}

# 切换流量
switch_traffic() {
    # 更新负载均衡器配置，将流量切换到绿色环境
    docker service update --env-add "UPSTREAM_TARGET=green" lb-service
}

# 清理蓝色环境
cleanup_blue() {
    docker stack rm "${STACK_NAME}-blue"
}
```

### 监控和告警

#### 1. 关键指标监控
```yaml
# Prometheus 监控规则
groups:
  - name: swarm_health
    rules:
      # 节点健康检查
      - alert: NodeDown
        expr: up{job="node-exporter"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "节点离线"
          description: "节点 {{ $labels.instance }} 已离线超过 1 分钟"
      
      # 服务副本检查
      - alert: ServiceReplicasMismatch
        expr: |
          (
            sum(docker_swarm_service_replicas_desired) by (service_name) -
            sum(docker_swarm_service_replicas_running) by (service_name)
          ) > 0
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "服务副本数不匹配"
          description: "服务 {{ $labels.service_name }} 期望副本数与实际运行副本数不匹配"
```

#### 2. 日志管理
```yaml
# 日志收集配置
services:
  app:
    image: app:latest
    logging:
      driver: "json-file"
      options:
        max-size: "100m"
        max-file: "3"
        labels: "service,version"
    deploy:
      labels:
        - "logging.enabled=true"
        - "logging.level=info"
```

### 备份和恢复

#### 1. 数据备份策略
```bash
#!/bin/bash
# 数据备份最佳实践

# 1. 数据库备份
backup_database() {
    local db_service="infrastructure_postgres"
    local backup_file="/backups/postgres_$(date +%Y%m%d_%H%M%S).sql"
    
    # 创建一致性备份
    docker exec $(docker ps -q -f name=$db_service) \
        pg_dump -U postgres -h localhost --clean --if-exists \
        --format=custom --compress=9 --verbose \
        --file=/tmp/backup.sql database_name
    
    # 复制备份文件
    docker cp $(docker ps -q -f name=$db_service):/tmp/backup.sql "$backup_file"
    
    # 验证备份文件
    if [ -f "$backup_file" ] && [ -s "$backup_file" ]; then
        echo "数据库备份成功: $backup_file"
    else
        echo "数据库备份失败"
        return 1
    fi
}

# 2. 配置备份
backup_configs() {
    local backup_dir="/backups/configs_$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$backup_dir"
    
    # 备份 Docker Configs
    docker config ls --format "{{.Name}}" | while read config; do
        docker config inspect "$config" --format "{{.Spec.Data}}" | \
            base64 -d > "$backup_dir/$config"
    done
    
    # 备份 Secrets 列表（不包含实际内容）
    docker secret ls --format "{{.Name}}" > "$backup_dir/secrets_list.txt"
}

# 3. 自动备份调度
setup_backup_schedule() {
    # 添加到 crontab
    cat << 'EOF' | crontab -
# 每日凌晨 2 点备份数据库
0 2 * * * /path/to/backup_database.sh

# 每周日凌晨 3 点备份配置
0 3 * * 0 /path/to/backup_configs.sh

# 每月 1 号清理 30 天前的备份
0 4 1 * * find /backups -type f -mtime +30 -delete
EOF
}
```

## 🚨 故障处理

### 常见问题诊断

#### 1. 服务启动失败
```bash
# 诊断步骤
# 1. 查看服务状态
docker service ps <service_name> --no-trunc

# 2. 查看服务日志
docker service logs <service_name> --tail 100

# 3. 检查资源约束
docker service inspect <service_name> --format '{{.Spec.TaskTemplate.Resources}}'

# 4. 检查节点资源
docker node ls
docker node inspect <node_id> --format '{{.Status.State}}'
```

#### 2. 网络连接问题
```bash
# 网络诊断
# 1. 检查网络配置
docker network ls
docker network inspect <network_name>

# 2. 测试服务间连通性
docker exec -it <container_id> ping <target_service>
docker exec -it <container_id> nslookup <target_service>

# 3. 检查端口监听
docker exec -it <container_id> netstat -tlnp
```

#### 3. 性能问题诊断
```bash
# 性能诊断
# 1. 检查资源使用
docker stats

# 2. 检查服务负载
docker service ps <service_name>

# 3. 分析慢查询（数据库）
docker exec -it postgres_container psql -c "
SELECT query, mean_time, calls 
FROM pg_stat_statements 
ORDER BY mean_time DESC 
LIMIT 10;"
```

### 应急响应流程

#### 1. 服务故障响应
```bash
#!/bin/bash
# 应急响应脚本

emergency_response() {
    local service_name=$1
    
    echo "🚨 开始应急响应: $service_name"
    
    # 1. 立即扩容
    docker service scale "$service_name=5"
    
    # 2. 检查健康状态
    for i in {1..10}; do
        if check_service_health "$service_name"; then
            echo "✅ 服务恢复正常"
            break
        fi
        sleep 30
    done
    
    # 3. 如果仍然失败，回滚到上一版本
    if ! check_service_health "$service_name"; then
        echo "🔄 执行服务回滚"
        docker service rollback "$service_name"
    fi
    
    # 4. 发送告警通知
    send_alert "服务故障" "服务 $service_name 发生故障，已执行应急响应"
}
```

#### 2. 数据恢复流程
```bash
#!/bin/bash
# 数据恢复流程

disaster_recovery() {
    local backup_date=$1
    
    echo "🔄 开始灾难恢复: $backup_date"
    
    # 1. 停止相关服务
    docker service scale infrastructure_postgres=0
    
    # 2. 恢复数据
    restore_database "$backup_date"
    
    # 3. 验证数据完整性
    if verify_data_integrity; then
        echo "✅ 数据恢复成功"
    else
        echo "❌ 数据恢复失败，需要人工干预"
        return 1
    fi
    
    # 4. 重启服务
    docker service scale infrastructure_postgres=1
    
    # 5. 全面健康检查
    ./scripts/health_check.sh
}
```

## 📚 开发工作流

### 环境管理

#### 1. 多环境配置
```bash
# 环境配置管理
environments/
├── development.env     # 开发环境
├── staging.env        # 测试环境
├── production.env     # 生产环境
└── common.env         # 通用配置

# 环境切换脚本
switch_environment() {
    local env=$1
    
    if [ ! -f "environments/$env.env" ]; then
        echo "环境配置文件不存在: $env.env"
        return 1
    fi
    
    # 备份当前配置
    cp .env .env.backup
    
    # 合并通用配置和环境特定配置
    cat environments/common.env environments/$env.env > .env
    
    echo "已切换到 $env 环境"
}
```

#### 2. CI/CD 集成
```yaml
# .gitlab-ci.yml 示例
stages:
  - test
  - build
  - deploy

variables:
  DOCKER_REGISTRY: "harbor.example.com"
  PROJECT_NAME: "comprehensive-platform"

test:
  stage: test
  script:
    - docker-compose -f docker-compose.test.yml up --abort-on-container-exit
    - docker-compose -f docker-compose.test.yml down

build:
  stage: build
  script:
    - docker build -t $DOCKER_REGISTRY/$PROJECT_NAME:$CI_COMMIT_SHA .
    - docker push $DOCKER_REGISTRY/$PROJECT_NAME:$CI_COMMIT_SHA
  only:
    - main
    - develop

deploy_staging:
  stage: deploy
  script:
    - ./swarm_deploy.sh deploy-stack infrastructure
    - ./swarm_deploy.sh deploy-stack core
    - ./scripts/health_check.sh
  environment:
    name: staging
  only:
    - develop

deploy_production:
  stage: deploy
  script:
    - ./swarm_deploy.sh deploy
    - ./scripts/health_check.sh
  environment:
    name: production
  when: manual
  only:
    - main
```

这些最佳实践涵盖了 Docker Swarm 部署的各个方面，从规划设计到运维管理，为您的综合信息平台提供了完整的指导方案。
