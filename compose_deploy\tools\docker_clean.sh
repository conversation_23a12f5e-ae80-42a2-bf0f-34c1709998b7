#!/bin/bash
set -e  # 遇到错误时退出脚本

echo "开始清理Docker无用资源..."

# 1. 清理已退出的容器
echo "步骤1: 清理已退出的容器..."
docker ps -a | grep "Exited" | awk '{print $1}' | xargs docker stop
docker ps -a | grep "Exited" | awk '{print $1}' | xargs docker rm

# 2. 清理名称为none的镜像
echo "步骤2: 清理名称为none的镜像..."
docker images | grep none | awk '{print $3}' | xargs docker rmi

# 3. 清理悬空卷
echo "步骤3: 清理悬空卷..."
docker volume rm $(docker volume ls -qf dangling=true) || true  # 忽略可能的错误

# 4. 执行系统级清理
echo "步骤4: 执行系统级清理..."
docker system prune --all --force

echo "Docker 资源清理完成！"
