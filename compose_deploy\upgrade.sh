#!/bin/bash

#===============================================================================
# xxxx综合信息平台 - 版本升级脚本 v1.0
#===============================================================================
# 功能描述: 将旧版本的数据卷从宿主机目录迁移到 Docker 命名卷
# 适用版本: v1.0 升级脚本
# 创建时间: 2024年
#
# 警告: 请在完全知晓以下脚本行为的情况下执行
#       此脚本会修改数据存储方式，执行前请确保已备份重要数据
#===============================================================================

# 遇到错误立即退出
set -e

#===============================================================================
# 全局变量配置
#===============================================================================

# 迁移临时目录 - 用于存储备份文件
MIGRATION_TEMP_PATH=/tmp/gkpt-compose_deploy/migration

# BusyBox 镜像 - 用于数据备份和恢复操作
DOCKER_BUSY_BOX_IMAGE=harbor2.qdbdtd.com:8088/middleware/busybox:1.32

#===============================================================================
# 工具函数定义
#===============================================================================

#-------------------------------------------------------------------------------
# 函数名: create_volume
# 功能: 创建 Docker 命名卷（如果不存在）
# 参数: $1 - 卷名称
# 返回: 无
#-------------------------------------------------------------------------------
create_volume() {
  local vol_name=$1

  # 检查卷是否已存在
  local vol_flag=$(docker volume ls -f name="$vol_name" | grep -w "$vol_name"\$ | wc -l)

  if [ "$vol_flag" -eq 0 ]; then
    echo "创建 Docker 卷: $vol_name"
    docker volume create --name="$vol_name"
  else
    echo "Docker 卷已存在: $vol_name"
  fi
}

#-------------------------------------------------------------------------------
# 函数名: backup_volume
# 功能: 将宿主机目录数据备份到临时文件
# 参数: $1 - 卷名称
#       $2 - 源数据路径（宿主机路径）
# 返回: 无
#-------------------------------------------------------------------------------
backup_volume() {
  local vol_name=$1
  local vol_path=$2
  local backup_file_name=$vol_name.tar

  echo "正在备份数据卷: $vol_name"
  echo "  源路径: $vol_path"
  echo "  备份文件: $backup_file_name"

  # 使用 BusyBox 容器创建 tar 备份文件
  docker run --rm \
    -v "$vol_path":/backup_source:ro \
    -v "$MIGRATION_TEMP_PATH":/backup_dist \
    "${DOCKER_BUSY_BOX_IMAGE}" \
    tar cf /backup_dist/"$backup_file_name" /backup_source

  echo "  备份完成: $MIGRATION_TEMP_PATH/$backup_file_name"
}

#-------------------------------------------------------------------------------
# 函数名: restore_volume
# 功能: 从备份文件恢复数据到 Docker 命名卷
# 参数: $1 - 卷名称
#       $2 - 备份文件路径
# 返回: 无
#-------------------------------------------------------------------------------
restore_volume() {
  local vol_name=$1
  local restore_from=$2

  echo "正在恢复数据到卷: $vol_name"
  echo "  备份文件: $restore_from"

  # 使用 BusyBox 容器从 tar 文件恢复数据到命名卷
  docker run -i --rm --name restore_helper \
    --mount source="$vol_name",target=/restore_target \
    -v "$restore_from":/restore_source/backup.tar \
    "${DOCKER_BUSY_BOX_IMAGE}" \
    tar xf /restore_source/backup.tar --directory /restore_target/ --strip 1

  echo "  恢复完成: $vol_name"
}

#-------------------------------------------------------------------------------
# 函数名: cbr (Create-Backup-Restore)
# 功能: 完整的数据迁移流程 - 创建卷、备份数据、恢复数据
# 参数: $1 - 卷名称
#       $2 - 源数据路径（宿主机路径）
# 返回: 无
#-------------------------------------------------------------------------------
cbr() {
  local vol_name=$1
  local vol_path=$2
  local backup_file_path=$MIGRATION_TEMP_PATH/$vol_name.tar

  echo ""
  echo "=========================================="
  echo "开始迁移数据卷: $vol_name"
  echo "=========================================="

  # 检查源路径是否存在
  if [ ! -d "$vol_path" ]; then
    echo "警告: 源路径不存在，跳过迁移: $vol_path"
    return 0
  fi

  # 执行完整的迁移流程
  create_volume "$vol_name"
  backup_volume "$vol_name" "$vol_path"
  restore_volume "$vol_name" "$backup_file_path"

  echo "数据卷迁移完成: $vol_name"
  echo "=========================================="
}

#===============================================================================
# 主程序执行
#===============================================================================

echo ""
echo "==============================================================================="
echo "xxxx综合信息平台 - 数据卷升级脚本 v1.0"
echo "==============================================================================="
echo "开始执行数据卷迁移..."
echo "临时目录: $MIGRATION_TEMP_PATH"
echo "BusyBox 镜像: $DOCKER_BUSY_BOX_IMAGE"
echo ""

# 创建临时目录
echo "创建临时目录: $MIGRATION_TEMP_PATH"
mkdir -p "$MIGRATION_TEMP_PATH"

#===============================================================================
# 基础服务数据卷迁移
#===============================================================================

echo ""
echo "==============================================================================="
echo "开始迁移基础服务数据卷..."
echo "==============================================================================="

# RabbitMQ 消息队列 - 仅创建卷，无需迁移数据
echo "创建 RabbitMQ 数据卷..."
create_volume 'base-rabbitmq'

# Node-RED 数据网关
cbr 'base-red-node' '/data/red-node-data'

# Redis 缓存数据库
cbr 'base-redis' '/data/mos/redis'

# PostgreSQL 主数据库
cbr 'base-postgres' '/data/mos/postgresql'

# InfluxDB 时序数据库
cbr 'base-influxdb' '/data/mos/influxdb'

#===============================================================================
# MOS 基座后管系统数据卷迁移
#===============================================================================

echo ""
echo "==============================================================================="
echo "开始迁移 MOS 基座后管系统数据卷..."
echo "==============================================================================="

# 数据监控服务图片存储
cbr 'data-monitor-image' '/data/mos/data-monitor/images'

# 用户认证数据库（MySQL）
cbr 'user-auth-db' '/data/mos/mysql'

# 各服务日志数据卷迁移
cbr 'data-monitor-log' '/data/mos/data-monitor/logs/logs'       # 数据监控日志
cbr 'data-storage-log' '/data/mos/data-storage/logs'            # 数据存储日志
cbr 'data-warn-log' '/data/mos/data-warn/logs'                  # 数据预警日志
cbr 'converter-monitor-log' '/data/mos/converter-monitor/logs'  # 数据转换监控日志
cbr 'gis-biz-log' '/data/mos/gis-biz/logs'                      # GIS 业务日志
cbr 'mes-biz-log' '/data/mos/mes-biz/logs'                      # MES 业务日志

#===============================================================================
# 视频监控系统数据卷迁移
#===============================================================================

echo ""
echo "==============================================================================="
echo "开始迁移视频监控系统数据卷..."
echo "==============================================================================="

# 视频服务 Redis 缓存
cbr 'video-redis' '/data/video-server/video-redis'

#===============================================================================
# 监控工具数据卷迁移
#===============================================================================

echo ""
echo "==============================================================================="
echo "开始迁移监控工具数据卷..."
echo "==============================================================================="

# Portainer 容器管理工具数据
cbr 'monitoring-portainer-data' '/data/monitoring/portainer/data'

# Grafana 监控面板数据
cbr 'monitoring-grafana-data' '/data/monitoring/grafana/data'

# Prometheus 指标收集数据
cbr 'monitoring-prometheus-data' '/data/monitoring/prometheus/data'

#===============================================================================
# 升级完成
#===============================================================================

echo ""
echo "==============================================================================="
echo "数据卷升级完成！"
echo "==============================================================================="
echo "升级摘要:"
echo "- 所有数据已从宿主机目录迁移到 Docker 命名卷"
echo "- 备份文件保存在: $MIGRATION_TEMP_PATH"
echo "- 请验证服务正常运行后，可手动清理备份文件"
echo ""
echo "后续步骤:"
echo "1. 重启所有服务: ./compose_deploy.sh restart"
echo "2. 验证服务状态: docker ps"
echo "3. 检查数据完整性"
echo "4. 清理备份文件: rm -rf $MIGRATION_TEMP_PATH"
echo ""
echo "升级脚本执行完毕！"
echo "==============================================================================="
