#!/bin/bash

# 综合信息平台 - 环境配置文件
# 注意: 此文件包含敏感信息，生产环境部署前请仔细检查所有配置项

### 开发环境配置，如果合并到 BladeX 环境，应改为生产环境配置 ###

# 基础机构
export MINECODE=199988                  # 部署机构 ID（根据 CMS 机构名表进行映射的 ID，每个机构都不同）

# 授权配置
export AUTH_SERVICE_NAME=基座平台
export AUTH_SERVICE_ID=skeleton
export AUTH_PROJECT_NAME=XX煤矿
export AUTH_PROJECT_ID=199988

# 服务部署开关配置（决定哪些服务需要启动）
DEPLOY_MOS_SERVICE=true                 # 部署MOS基础服务（用户权限）- 一般必须启用
DEPLOY_IOT_SERVICE=true                 # 部署IOT平台服务 (数据接入系统、预警报警消息推送、TOPO)
DEPLOY_VIDEO_SERVICE=false              # 部署视频服务
DEPLOY_MES_SERVICE=false                # 部署MES业务服务 (前后端)
DEPLOY_GIS_SERVICE=false                # 部署GIS业务服务 (前后端)
DEPLOY_FLOW_SERVICE=false               # 部署业务审批流 (前后端)
DEPLOY_APP_SERVICE=false                # 部署APP

# 外部服务地址配置（CMS 和授权服务）
# 生产环境地址
export PUBLIC_SERVICE_PATH=39.105.136.49
export PUBLIC_SERVICE_PORT=22260
# 测试环境地址
# export PUBLIC_SERVICE_PATH=192.168.201.5
# export PUBLIC_SERVICE_PORT=22262
# CMS 上游服务地址
export UPSTREAM_CMS=http://${PUBLIC_SERVICE_PATH}:9005

# 核心基础设施端口配置
export PORT_POSTGRES=5432                    # PostgreSQL 数据库端口
export PORT_MYSQL=3306                       # MySQL 数据库端口
export PORT_RABBITMQ=5672                    # RabbitMQ 服务端口
export PORT_RABBITMQ_DASHBOARD=15672         # RabbitMQ 控制台端口
export PORT_BLADEX_RABBITMQ=5672             # 业务专用 RabbitMQ 服务端口
export PORT_BLADEX_RABBITMQ_DASHBOARD=15672  # 业务专用 RabbitMQ 控制台端口

# 业务服务端口配置
export PORT_BASE_BACKEND=9401           # 基础后端服务端口
export PORT_DATA_MONITOR=9003           # 数据接口服务端口
export PORT_MES_BIZ=9102                # MES业务服务端口
export PORT_GIS_BIZ=9101                # GIS业务服务端口
export PORT_PUSH_MSG=9104               # 消息推送服务端口
export PORT_AUTH_BACKEND=22260          # 授权后端服务端口

# 前端服务端口配置
export PORT_FRONTEND=80                 # 主前端服务端口
export PORT_FRONTEND_SABER=14148        # Saber 前端框架端口（8002 的替代端口）

# 工具服务端口配置
export POST_WORKFLOW_BACKEND=8004       # 工作流后端服务端口
export PORT_FILE_PREVIEW=8012           # 文件预览服务端口（kkfileview）

# InfluxDB 服务配置
export INFLUXDB_ADMIN_ENABLED=true      # InfluxDB 管理功能启用
export INFLUXDB_HTTP_AUTH_ENABLED=true  # InfluxDB HTTP 认证启用

# GraphQL 服务配置
export HASURA_MEMORY_LIMIT=10G          # Hasura GraphQL Engine 内存限制

# 依赖服务

# GIS 服务配置
export BLADE_MINIO_HOST=***********     # 为二三维提供minio的访问地址 [综合信息平台部署服务IP]
export LY_GIS_HOST=***********          # GIS 服务主机地址
export THREE_GIS_URL=http://*************:20030
export SAFE_URL_HOST=http://${LY_GIS_HOST}
export SAFE_PORT=8888
export UPSTREAM_GIS_API=http://${LY_GIS_HOST}:8888
export UPSTREAM_GIS_STATIC=http://${LY_GIS_HOST}:1889
export UPSTREAM_GIS_MINIO=http://${LY_GIS_HOST}:9000
export UPSTREAM_GIS_THREE=http://${LY_GIS_HOST}:10048

# 帆软服务配置
export UPSTREAM_FINEREPORT=http://${BLADE_MINIO_HOST}:8080

# 短信服务配置
export SMS_URL=http://localhost:9001
export SMS_USERNAME=root
export SMS_PASSWORD=123456

# 应急广播服务配置
export MONITOR_FACTORY_URL=http://localhost:9001

# SSH 调试服务配置
export SSH_PASSWORD=Btdd#369#
export SSH_PORT=2222

# 备份配置
BACKUP_BASE_PATH=/home/<USER>

# Docker 基础配置
export DOCKER_NETWORK=middle
