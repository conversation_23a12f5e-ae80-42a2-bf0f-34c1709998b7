- table:
    schema: public
    name: alarm_detail
- table:
    schema: public
    name: alarm_directive
- table:
    schema: public
    name: alarm_handle
- table:
    schema: public
    name: alarm_history
- table:
    schema: public
    name: alarm_realtime
  object_relationships:
  - name: handle
    using:
      manual_configuration:
        remote_table:
          schema: public
          name: alarm_handle
        column_mapping:
          begin_time: begin_time
          point_id: point_id
  - name: system
    using:
      manual_configuration:
        remote_table:
          schema: public
          name: model_system
        column_mapping:
          system_id: id
- table:
    schema: public
    name: alembic_version
- table:
    schema: public
    name: area
- table:
    schema: public
    name: area_top_data_info
- table:
    schema: public
    name: belt_weigher_definition
- table:
    schema: public
    name: belt_weigher_realtime
- table:
    schema: public
    name: broadcast
  object_relationships:
  - name: geolocation_area
    using:
      foreign_key_constraint_on: geolocation_area_id
  - name: mining_area
    using:
      foreign_key_constraint_on: mining_area_id
  array_relationships:
  - name: broadcast_maps
    using:
      foreign_key_constraint_on:
        column: point_id
        table:
          schema: public
          name: broadcast_map
- table:
    schema: public
    name: broadcast_directive
- table:
    schema: public
    name: broadcast_group
  array_relationships:
  - name: broadcast_maps
    using:
      foreign_key_constraint_on:
        column: boardcast_group_id
        table:
          schema: public
          name: broadcast_map
- table:
    schema: public
    name: broadcast_map
  object_relationships:
  - name: broadcast
    using:
      foreign_key_constraint_on: point_id
  - name: broadcast_group
    using:
      foreign_key_constraint_on: boardcast_group_id
- table:
    schema: public
    name: card_directive
- table:
    schema: public
    name: class_set
  array_relationships:
  - name: positions
    using:
      foreign_key_constraint_on:
        column: class_id
        table:
          schema: public
          name: position
  - name: schedules
    using:
      foreign_key_constraint_on:
        column: class_id
        table:
          schema: public
          name: schedule
- table:
    schema: public
    name: cms_attribute
- table:
    schema: public
    name: cms_china_province
- table:
    schema: public
    name: cms_device_model
- table:
    schema: public
    name: cms_equipment
- table:
    schema: public
    name: cms_mapping_model_attr
- table:
    schema: public
    name: cms_mine_attr_type
- table:
    schema: public
    name: cms_mine_group
- table:
    schema: public
    name: cms_mine_name
- table:
    schema: public
    name: cms_mine_system
- table:
    schema: public
    name: cms_mine_unit
- table:
    schema: public
    name: config_automation_group
  array_relationships:
  - name: points
    using:
      foreign_key_constraint_on:
        column: group_id
        table:
          schema: public
          name: config_automation_group_device_point_map
- table:
    schema: public
    name: config_automation_group_device_point_map
  object_relationships:
  - name: config_automation_group
    using:
      foreign_key_constraint_on: group_id
  - name: point
    using:
      foreign_key_constraint_on: point_id
- table:
    schema: public
    name: config_map
  array_relationships:
  - name: config_map_categories
    using:
      foreign_key_constraint_on:
        column: map_guid
        table:
          schema: public
          name: config_map_category
- table:
    schema: public
    name: config_map_category
  object_relationships:
  - name: config_map
    using:
      foreign_key_constraint_on: map_guid
- table:
    schema: public
    name: daily_login_log
  computed_fields:
  - name: last_operator_time
    definition:
      function:
        schema: public
        name: last_operator_time
- table:
    schema: public
    name: data_attribute
- table:
    schema: public
    name: data_automation_definition
  array_relationships:
  - name: data_automation_realtimes
    using:
      foreign_key_constraint_on:
        column: point_id
        table:
          schema: public
          name: data_automation_realtime
- table:
    schema: public
    name: data_automation_realtime
  object_relationships:
  - name: data_automation_definition
    using:
      foreign_key_constraint_on: point_id
- table:
    schema: public
    name: data_business
- table:
    schema: public
    name: data_child_system
- table:
    schema: public
    name: data_definition_conf
- table:
    schema: public
    name: data_derive_definition
  array_relationships:
  - name: data_derive_realtimes
    using:
      foreign_key_constraint_on:
        column: point_id
        table:
          schema: public
          name: data_derive_realtime
- table:
    schema: public
    name: data_derive_realtime
  object_relationships:
  - name: data_derive_definition
    using:
      foreign_key_constraint_on: point_id
- table:
    schema: public
    name: data_device
  object_relationships:
  - name: gis
    using:
      manual_configuration:
        remote_table:
          schema: public
          name: data_device_gis
        column_mapping:
          id: device_id
- table:
    schema: public
    name: data_device_gis
- table:
    schema: public
    name: data_device_model_conf
- table:
    schema: public
    name: data_point
- table:
    schema: public
    name: data_point_temp
- table:
    schema: public
    name: data_quick_query
- table:
    schema: public
    name: data_quick_query_temp
- table:
    schema: public
    name: data_status
- table:
    schema: public
    name: data_status_temp
- table:
    schema: public
    name: data_topo
- table:
    schema: public
    name: data_type
- table:
    schema: public
    name: data_warming
- table:
    schema: public
    name: definition_relative_conf
- table:
    schema: public
    name: definition_view
- table:
    schema: public
    name: device
  object_relationships:
  - name: geolocation_area
    using:
      foreign_key_constraint_on: geolocation_area_id
- table:
    schema: public
    name: device_control
- table:
    schema: public
    name: device_control_white_list
- table:
    schema: public
    name: device_point
  array_relationships:
  - name: config_automation_group_device_point_maps
    using:
      foreign_key_constraint_on:
        column: point_id
        table:
          schema: public
          name: config_automation_group_device_point_map
- table:
    schema: public
    name: ding_approval_system
- table:
    schema: public
    name: dispatch
  object_relationships:
  - name: geolocation_area
    using:
      foreign_key_constraint_on: geolocation_area_id
  - name: mining_area
    using:
      foreign_key_constraint_on: mining_area_id
  array_relationships:
  - name: dispatch_maps
    using:
      foreign_key_constraint_on:
        column: point_id
        table:
          schema: public
          name: dispatch_map
- table:
    schema: public
    name: dispatch_directive
- table:
    schema: public
    name: dispatch_group
  array_relationships:
  - name: dispatch_maps
    using:
      foreign_key_constraint_on:
        column: dispatch_group_id
        table:
          schema: public
          name: dispatch_map
- table:
    schema: public
    name: dispatch_map
  object_relationships:
  - name: dispatch
    using:
      foreign_key_constraint_on: point_id
  - name: dispatch_group
    using:
      foreign_key_constraint_on: dispatch_group_id
- table:
    schema: public
    name: employee
  array_relationships:
  - name: positions
    using:
      foreign_key_constraint_on:
        column: card_id
        table:
          schema: public
          name: position
  - name: schedule_x_employees
    using:
      foreign_key_constraint_on:
        column: employee_id
        table:
          schema: public
          name: schedule_x_employee
- table:
    schema: public
    name: employee_help
- table:
    schema: public
    name: employee_help_statistics
- table:
    schema: public
    name: employee_timeout
- table:
    schema: public
    name: employee_timeout_statistics
- table:
    schema: public
    name: enterprise
- table:
    schema: public
    name: escape_route
  array_relationships:
  - name: geolocation_area_escape_route_maps
    using:
      foreign_key_constraint_on:
        column: escape_route_id
        table:
          schema: public
          name: geolocation_area_escape_route_map
- table:
    schema: public
    name: fire_alarm_config
  object_relationships:
  - name: fire_alarm_threshold
    using:
      foreign_key_constraint_on: threshold_id
  - name: safety_supervision
    using:
      foreign_key_constraint_on: c_point_id
  - name: safetySupervisionByTPointId
    using:
      foreign_key_constraint_on: t_point_id
  - name: work_face
    using:
      foreign_key_constraint_on: work_face_serial
  array_relationships:
  - name: fire_alarm_realtimes
    using:
      foreign_key_constraint_on:
        column: config_id
        table:
          schema: public
          name: fire_alarm_realtime
- table:
    schema: public
    name: fire_alarm_realtime
  object_relationships:
  - name: fire_alarm_config
    using:
      foreign_key_constraint_on: config_id
- table:
    schema: public
    name: fire_alarm_statistics
- table:
    schema: public
    name: fire_alarm_threshold
  array_relationships:
  - name: fire_alarm_configs
    using:
      foreign_key_constraint_on:
        column: threshold_id
        table:
          schema: public
          name: fire_alarm_config
- table:
    schema: public
    name: flight_route
- table:
    schema: public
    name: gas_drainage
  object_relationships:
  - name: geolocation_area
    using:
      foreign_key_constraint_on: geolocation_area_id
  - name: mining_area
    using:
      foreign_key_constraint_on: mining_area_id
  array_relationships:
  - name: gas_drainage_realtimes
    using:
      foreign_key_constraint_on:
        column: point_id
        table:
          schema: public
          name: gas_drainage_realtime
- table:
    schema: public
    name: gas_drainage_realtime
  object_relationships:
  - name: gas_drainage
    using:
      foreign_key_constraint_on: point_id
- table:
    schema: public
    name: gas_emission_config
  object_relationships:
  - name: safety_supervision
    using:
      foreign_key_constraint_on: point_id
  - name: work_face
    using:
      foreign_key_constraint_on: work_face_serial
  array_relationships:
  - name: gas_emission_controlled_maps
    using:
      foreign_key_constraint_on:
        column: config_id
        table:
          schema: public
          name: gas_emission_controlled_map
  - name: gas_emission_methane_maps
    using:
      foreign_key_constraint_on:
        column: config_id
        table:
          schema: public
          name: gas_emission_methane_map
  - name: gas_emission_realtimes
    using:
      foreign_key_constraint_on:
        column: config_id
        table:
          schema: public
          name: gas_emission_realtime
- table:
    schema: public
    name: gas_emission_controlled_map
  object_relationships:
  - name: gas_emission_config
    using:
      foreign_key_constraint_on: config_id
  - name: safety_supervision
    using:
      foreign_key_constraint_on: point_id
- table:
    schema: public
    name: gas_emission_methane_map
  object_relationships:
  - name: gas_emission_config
    using:
      foreign_key_constraint_on: config_id
  - name: safety_supervision
    using:
      foreign_key_constraint_on: point_id
- table:
    schema: public
    name: gas_emission_realtime
  object_relationships:
  - name: gas_emission_config
    using:
      foreign_key_constraint_on: config_id
- table:
    schema: public
    name: gas_emission_statistics
- table:
    schema: public
    name: gas_patrol
  object_relationships:
  - name: safety_supervision
    using:
      foreign_key_constraint_on: point_id
- table:
    schema: public
    name: geography_columns
- table:
    schema: public
    name: geolocation_area
  object_relationships:
  - name: mining_area
    using:
      foreign_key_constraint_on: mining_area_id
  array_relationships:
  - name: broadcasts
    using:
      foreign_key_constraint_on:
        column: geolocation_area_id
        table:
          schema: public
          name: broadcast
  - name: devices
    using:
      foreign_key_constraint_on:
        column: geolocation_area_id
        table:
          schema: public
          name: device
  - name: dispatches
    using:
      foreign_key_constraint_on:
        column: geolocation_area_id
        table:
          schema: public
          name: dispatch
  - name: gas_drainages
    using:
      foreign_key_constraint_on:
        column: geolocation_area_id
        table:
          schema: public
          name: gas_drainage
  - name: geolocation_area_device_maps
    using:
      foreign_key_constraint_on:
        column: geolocation_area_id
        table:
          schema: public
          name: geolocation_area_device_map
  - name: geolocation_area_escape_route_maps
    using:
      foreign_key_constraint_on:
        column: geolocation_area_id
        table:
          schema: public
          name: geolocation_area_escape_route_map
  - name: geolocation_area_safety_supervision_maps
    using:
      foreign_key_constraint_on:
        column: geolocation_area_id
        table:
          schema: public
          name: geolocation_area_safety_supervision_map
  - name: geolocation_area_safety_supervision_type_maps
    using:
      foreign_key_constraint_on:
        column: geolocation_area_id
        table:
          schema: public
          name: geolocation_area_safety_supervision_type_map
  - name: geolocation_area_ventilation_route_maps
    using:
      foreign_key_constraint_on:
        column: geolocation_area_id
        table:
          schema: public
          name: geolocation_area_ventilation_route_map
  - name: location_identifiers
    using:
      foreign_key_constraint_on:
        column: geolocation_area_id
        table:
          schema: public
          name: location_identifiers
  - name: safety_supervisions
    using:
      foreign_key_constraint_on:
        column: geolocation_area_id
        table:
          schema: public
          name: safety_supervision
  - name: stress_monitor_definitions
    using:
      foreign_key_constraint_on:
        column: geolocation_area_id
        table:
          schema: public
          name: stress_monitor_definition
  - name: video_channels
    using:
      foreign_key_constraint_on:
        column: geolocation_area_id
        table:
          schema: public
          name: video_channel
  - name: water_observation_definitions
    using:
      foreign_key_constraint_on:
        column: geolocation_area_id
        table:
          schema: public
          name: water_observation_definition
- table:
    schema: public
    name: geolocation_area_device_map
- table:
    schema: public
    name: geolocation_area_escape_route_map
  object_relationships:
  - name: escape_route
    using:
      foreign_key_constraint_on: escape_route_id
  - name: geolocation_area
    using:
      foreign_key_constraint_on: geolocation_area_id
- table:
    schema: public
    name: geolocation_area_safety_supervision_map
  object_relationships:
  - name: geolocation_area
    using:
      foreign_key_constraint_on: geolocation_area_id
  - name: safety_supervision
    using:
      foreign_key_constraint_on: point_id
- table:
    schema: public
    name: geolocation_area_safety_supervision_type_map
  object_relationships:
  - name: geolocation_area
    using:
      foreign_key_constraint_on: geolocation_area_id
- table:
    schema: public
    name: geolocation_area_ventilation_route_map
  object_relationships:
  - name: geolocation_area
    using:
      foreign_key_constraint_on: geolocation_area_id
  - name: ventilation_route
    using:
      foreign_key_constraint_on: ventilation_route_id
- table:
    schema: public
    name: geometry_columns
- table:
    schema: public
    name: location_identifiers
  object_relationships:
  - name: geolocation_area
    using:
      foreign_key_constraint_on: geolocation_area_id
  - name: mining_area
    using:
      foreign_key_constraint_on: mining_area_id
  array_relationships:
  - name: location_identifiers_realtimes
    using:
      foreign_key_constraint_on:
        column: point_id
        table:
          schema: public
          name: location_identifiers_realtime
  - name: positions
    using:
      foreign_key_constraint_on:
        column: point_id
        table:
          schema: public
          name: position
  - name: positionsByPrevPointId
    using:
      foreign_key_constraint_on:
        column: prev_point_id
        table:
          schema: public
          name: position
  - name: substation_routes
    using:
      foreign_key_constraint_on:
        column: begin_point_id
        table:
          schema: public
          name: substation_route
  - name: substationRoutesByEndPointId
    using:
      foreign_key_constraint_on:
        column: end_point_id
        table:
          schema: public
          name: substation_route
- table:
    schema: public
    name: location_identifiers_realtime
  object_relationships:
  - name: location_identifier
    using:
      foreign_key_constraint_on: point_id
- table:
    schema: public
    name: location_overman
- table:
    schema: public
    name: location_overman_statistics
- table:
    schema: public
    name: login_statistics
- table:
    schema: public
    name: mapping_equip_attr_point
- table:
    schema: public
    name: mining_area
  array_relationships:
  - name: broadcasts
    using:
      foreign_key_constraint_on:
        column: mining_area_id
        table:
          schema: public
          name: broadcast
  - name: dispatches
    using:
      foreign_key_constraint_on:
        column: mining_area_id
        table:
          schema: public
          name: dispatch
  - name: gas_drainages
    using:
      foreign_key_constraint_on:
        column: mining_area_id
        table:
          schema: public
          name: gas_drainage
  - name: geolocation_areas
    using:
      foreign_key_constraint_on:
        column: mining_area_id
        table:
          schema: public
          name: geolocation_area
  - name: location_identifiers
    using:
      foreign_key_constraint_on:
        column: mining_area_id
        table:
          schema: public
          name: location_identifiers
  - name: safety_supervisions
    using:
      foreign_key_constraint_on:
        column: mining_area_id
        table:
          schema: public
          name: safety_supervision
  - name: stress_monitor_definitions
    using:
      foreign_key_constraint_on:
        column: mining_area_id
        table:
          schema: public
          name: stress_monitor_definition
  - name: video_channels
    using:
      foreign_key_constraint_on:
        column: mining_area_id
        table:
          schema: public
          name: video_channel
  - name: water_observation_definitions
    using:
      foreign_key_constraint_on:
        column: mining_area_id
        table:
          schema: public
          name: water_observation_definition
- table:
    schema: public
    name: model_attribute_type
- table:
    schema: public
    name: model_attribute_type_temp
- table:
    schema: public
    name: model_business_type
- table:
    schema: public
    name: model_device_type
- table:
    schema: public
    name: model_image
- table:
    schema: public
    name: model_system
- table:
    schema: public
    name: model_system_bak
- table:
    schema: public
    name: model_system_temp
- table:
    schema: public
    name: monitor_statistics
- table:
    schema: public
    name: mq_statistics
- table:
    schema: public
    name: operation_log
- table:
    schema: public
    name: point_definition_view
  object_relationships:
  - name: geolocation_area
    using:
      manual_configuration:
        remote_table:
          schema: public
          name: geolocation_area
        column_mapping:
          geolocation_area_id: id
- table:
    schema: public
    name: point_view
  object_relationships:
  - name: definition
    using:
      manual_configuration:
        remote_table:
          schema: public
          name: point_definition_view
        column_mapping:
          point_id: point_id
- table:
    schema: public
    name: portal_monitor
- table:
    schema: public
    name: portal_system
- table:
    schema: public
    name: position
  object_relationships:
  - name: class_set
    using:
      foreign_key_constraint_on: class_id
  - name: employee
    using:
      foreign_key_constraint_on: card_id
  - name: location_identifier
    using:
      foreign_key_constraint_on: point_id
  - name: locationIdentifierByPrevPointId
    using:
      foreign_key_constraint_on: prev_point_id
- table:
    schema: public
    name: production_information
- table:
    schema: public
    name: pumping_wells_definition
- table:
    schema: public
    name: pumping_wells_realtime
- table:
    schema: public
    name: quake_pos_result
- table:
    schema: public
    name: raster_columns
- table:
    schema: public
    name: raster_overviews
- table:
    schema: public
    name: safety_supervision
  object_relationships:
  - name: gas_patrol
    using:
      manual_configuration:
        remote_table:
          schema: public
          name: gas_patrol
        column_mapping:
          point_id: point_id
  - name: geolocation_area
    using:
      foreign_key_constraint_on: geolocation_area_id
  - name: mining_area
    using:
      foreign_key_constraint_on: mining_area_id
  array_relationships:
  - name: fire_alarm_configs
    using:
      foreign_key_constraint_on:
        column: c_point_id
        table:
          schema: public
          name: fire_alarm_config
  - name: fireAlarmConfigsByTPointId
    using:
      foreign_key_constraint_on:
        column: t_point_id
        table:
          schema: public
          name: fire_alarm_config
  - name: gas_emission_configs
    using:
      foreign_key_constraint_on:
        column: point_id
        table:
          schema: public
          name: gas_emission_config
  - name: gas_emission_controlled_maps
    using:
      foreign_key_constraint_on:
        column: point_id
        table:
          schema: public
          name: gas_emission_controlled_map
  - name: gas_emission_methane_maps
    using:
      foreign_key_constraint_on:
        column: point_id
        table:
          schema: public
          name: gas_emission_methane_map
  - name: gas_patrols
    using:
      foreign_key_constraint_on:
        column: point_id
        table:
          schema: public
          name: gas_patrol
  - name: geolocation_area_safety_supervision_maps
    using:
      foreign_key_constraint_on:
        column: point_id
        table:
          schema: public
          name: geolocation_area_safety_supervision_map
  - name: safety_supervision_realtimes
    using:
      foreign_key_constraint_on:
        column: point_id
        table:
          schema: public
          name: safety_supervision_realtime
- table:
    schema: public
    name: safety_supervision_alarm_statistics
- table:
    schema: public
    name: safety_supervision_realtime
  object_relationships:
  - name: safety_supervision
    using:
      foreign_key_constraint_on: point_id
- table:
    schema: public
    name: safety_supervision_statement
- table:
    schema: public
    name: scene
- table:
    schema: public
    name: schedule
  object_relationships:
  - name: class_set
    using:
      foreign_key_constraint_on: class_id
  array_relationships:
  - name: schedule_x_employees
    using:
      foreign_key_constraint_on:
        column: schedule_id
        table:
          schema: public
          name: schedule_x_employee
- table:
    schema: public
    name: schedule_x_employee
  object_relationships:
  - name: employee
    using:
      foreign_key_constraint_on: employee_id
  - name: schedule
    using:
      foreign_key_constraint_on: schedule_id
- table:
    schema: public
    name: sms
  object_relationships:
  - name: sms_template
    using:
      foreign_key_constraint_on: sms_template_id
- table:
    schema: public
    name: sms_template
  array_relationships:
  - name: sms
    using:
      foreign_key_constraint_on:
        column: sms_template_id
        table:
          schema: public
          name: sms
- table:
    schema: public
    name: spatial_ref_sys
- table:
    schema: public
    name: stress_monitor_definition
  object_relationships:
  - name: geolocation_area
    using:
      foreign_key_constraint_on: geolocation_area_id
  - name: mining_area
    using:
      foreign_key_constraint_on: mining_area_id
  array_relationships:
  - name: stress_monitor_realtimes
    using:
      foreign_key_constraint_on:
        column: point_id
        table:
          schema: public
          name: stress_monitor_realtime
- table:
    schema: public
    name: stress_monitor_realtime
  object_relationships:
  - name: stress_monitor_definition
    using:
      foreign_key_constraint_on: point_id
- table:
    schema: public
    name: substation_route
  object_relationships:
  - name: location_identifier
    using:
      foreign_key_constraint_on: begin_point_id
  - name: locationIdentifierByEndPointId
    using:
      foreign_key_constraint_on: end_point_id
- table:
    schema: public
    name: system_monitor
- table:
    schema: public
    name: system_statistics
- table:
    schema: public
    name: user_log
- table:
    schema: public
    name: vehicle_definition
  array_relationships:
  - name: vehicle_positions
    using:
      foreign_key_constraint_on:
        column: card_id
        table:
          schema: public
          name: vehicle_position
- table:
    schema: public
    name: vehicle_position
  object_relationships:
  - name: vehicle_definition
    using:
      foreign_key_constraint_on: card_id
  array_relationships:
  - name: vehicle_times
    using:
      foreign_key_constraint_on:
        column: card_id
        table:
          schema: public
          name: vehicle_time
- table:
    schema: public
    name: vehicle_time
  object_relationships:
  - name: vehicle_position
    using:
      foreign_key_constraint_on: card_id
- table:
    schema: public
    name: ventilation_route
  array_relationships:
  - name: geolocation_area_ventilation_route_maps
    using:
      foreign_key_constraint_on:
        column: ventilation_route_id
        table:
          schema: public
          name: geolocation_area_ventilation_route_map
- table:
    schema: public
    name: vibration_definition
  array_relationships:
  - name: vibration_realtimes
    using:
      foreign_key_constraint_on:
        column: point_id
        table:
          schema: public
          name: vibration_realtime
- table:
    schema: public
    name: vibration_realtime
  object_relationships:
  - name: vibration_definition
    using:
      foreign_key_constraint_on: point_id
- table:
    schema: public
    name: video_channel
  object_relationships:
  - name: geolocation_area
    using:
      foreign_key_constraint_on: geolocation_area_id
  - name: livewall_alert
    using:
      manual_configuration:
        remote_table:
          schema: public
          name: video_livewall_alert
        column_mapping:
          id: video_channel_id
  - name: mining_area
    using:
      foreign_key_constraint_on: mining_area_id
  - name: video_device
    using:
      foreign_key_constraint_on: device_id
  array_relationships:
  - name: video_channel_maps
    using:
      foreign_key_constraint_on:
        column: video_channel_id
        table:
          schema: public
          name: video_channel_map
- table:
    schema: public
    name: video_channel_group
  array_relationships:
  - name: video_channel_maps
    using:
      foreign_key_constraint_on:
        column: video_channel_group_id
        table:
          schema: public
          name: video_channel_map
- table:
    schema: public
    name: video_channel_map
  object_relationships:
  - name: video_channel
    using:
      foreign_key_constraint_on: video_channel_id
  - name: video_channel_group
    using:
      foreign_key_constraint_on: video_channel_group_id
- table:
    schema: public
    name: video_device
  array_relationships:
  - name: video_channels
    using:
      foreign_key_constraint_on:
        column: device_id
        table:
          schema: public
          name: video_channel
- table:
    schema: public
    name: video_livewall_alert
  object_relationships:
  - name: video_channel
    using:
      foreign_key_constraint_on: video_channel_id
- table:
    schema: public
    name: viewport
- table:
    schema: public
    name: water_observation_definition
  object_relationships:
  - name: geolocation_area
    using:
      foreign_key_constraint_on: geolocation_area_id
  - name: mining_area
    using:
      foreign_key_constraint_on: mining_area_id
  array_relationships:
  - name: water_observation_realtimes
    using:
      foreign_key_constraint_on:
        column: point_id
        table:
          schema: public
          name: water_observation_realtime
- table:
    schema: public
    name: water_observation_realtime
  object_relationships:
  - name: water_observation_definition
    using:
      foreign_key_constraint_on: point_id
- table:
    schema: public
    name: work_face
  array_relationships:
  - name: fire_alarm_configs
    using:
      foreign_key_constraint_on:
        column: work_face_serial
        table:
          schema: public
          name: fire_alarm_config
  - name: gas_emission_configs
    using:
      foreign_key_constraint_on:
        column: work_face_serial
        table:
          schema: public
          name: gas_emission_config
