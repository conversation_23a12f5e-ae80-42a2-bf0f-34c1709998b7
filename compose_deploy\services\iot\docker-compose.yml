#===============================================================================
# YAML 锚点定义 - 可复用的配置模板
#===============================================================================

# IoT 服务通用配置
x-iot-defaults: &iot_defaults
  restart: unless-stopped
  logging:
    driver: "json-file"
    options:
      max-size: "1g"
      max-file: "2"
  networks:
    - middle

# Nginx 前端服务配置模板
x-nginx-defaults: &nginx_defaults
  <<: *iot_defaults
  env_file:
    - ../services/base/.env
    - ../services/iot/.env
  healthcheck:
    test: ["CMD", "nginx", "-t"]
    interval: 30s
    timeout: 10s
    retries: 3
    start_period: 40s

# IoT 前端服务配置模板
x-iot-frontend-defaults: &iot_frontend_defaults
  <<: *nginx_defaults
  command: nginx -g 'daemon off;'
  volumes:
    - "../services/mos/config/nginx-default.conf:/etc/nginx/conf.d/default.conf:ro"

# IoT 后端服务配置模板
x-iot-backend-defaults: &iot_backend_defaults
  <<: *iot_defaults
  env_file:
    - ../services/base/.env
    - ../services/iot/.env
  healthcheck:
    test: ["CMD", "curl", "-f", "http://localhost:9001/health"]
    interval: 30s
    timeout: 10s
    retries: 3
    start_period: 60s

#===============================================================================
# 网络配置
#===============================================================================

networks:
  middle:
    external: true
    name: ${DOCKER_NETWORK}

#===============================================================================
# 数据卷配置
#===============================================================================

volumes:
  data-monitor-image:
    external: true
  data-monitor-log:
    external: true
  data-storage-log:
    external: true
  data-warn-log:
    external: true
  converter-monitor-log:
    external: true

#===============================================================================
# 服务定义 - 按 IoT 数据流程和依赖关系排序
#===============================================================================

services:
  #-----------------------------------------------------------------------------
  # 核心后端服务 - 数据处理流水线（按数据流向排序）
  #-----------------------------------------------------------------------------

  # 数据接入服务 - IoT 数据流的入口点
  backend-data-storage:
    <<: *iot_backend_defaults
    container_name: backend-data-storage
    image: harbor2.qdbdtd.com:8088/bjproduct/platform/services/dataroute:${BACKEND_ROUTE_VERSION}
    environment:
      # 核心配置
      - RABBITMQ_VIRTUALHOST=${RMB_DR_VH}
      - DATAROUTE_APPROVAL_URL=${UPSTREAM_CMS}
      - DATA_ROUTE_ACTIVE_PROFILE=pro
      # 告警配置
      - POSITION_STATION_ALARM_TYPE_ID=
      - SAFETY_SUBSTATION_ALARM_TYPE_ID=
      # 可选配置 (当前未启用)
      #- RABBITMQ_PREFETCH_COUNT=50
      #- RABBITMQ_CONSUMERS_PER_QUEUE=20
      #- TDENGINE_CONNECT_TYPE=restful
      #- TDENGINE_HOST=**************
      #- TDENGINE_PORT=6041
      #- TDENGINE_USERNAME=root
      #- TDENGINE_PASSWORD=Dbqdtd@168
      #- SCHEDULED_DATA_MONITOR_UPDATE_RMQ_RESET_INIT_DELAY=86400000
      #- SCHEDULED_DATA_MONITOR_UPDATE_RMQ_RESET_FIXED_DELAY=86400000
      #- SCHEDULED_DATA_MONITOR_UPDATE_DATA_MONITOR_CRON=
      #- SCHEDULED_POSITION_CHECK_ENABLED=false
      #- POSITION_TRACK_MAX_LENGTH=35565
      #- POSITION_TRACK_EXTRACT_TIME_RANGE=30
    volumes:
      - "data-storage-log:/home/<USER>/logs"
    deploy:
      resources:
        limits:
          cpus: '10'
          memory: 10G
        reservations:
          memory: 4G
    #ports:
    #  - "${PORT_DATA_STORAGE}:9001"

  # 统一接口服务 - 时序监控和数据查询
  backend-data-monitor:
    <<: *iot_backend_defaults
    container_name: backend-data-monitor
    image: harbor2.qdbdtd.com:8088/bjproduct/platform/services/timescale-monitor:${BACKEND_TSMONITOR_VERSION}
    environment:
      # 核心配置
      - TIMESCALE_MONITOR_ACTIVE_PROFILE=pro
      - RABBITMQ_VIRTUALHOST=${RMB_DM_VH}
      - DATAROUTE_APPROVAL_URL=${UPSTREAM_CMS}
      - MINECODE=${MINECODE}
      # 安全配置
      - SAFE_URL_HOST=${SAFE_URL_HOST}
      - SAFE_PORT=${SAFE_PORT}
      - BLADE_BACKEND=backend-process-user-permission
      - ENABLE_DATA_SCOPE=true
      # 存储配置
      - MINIO_ENDPOINT=http://bladex-minio:9000
      # 监控配置
      - SCHEDULED_COLLECT_FILE_LOAD_FILE_BASE_PATH=/home/<USER>/static/archive
      - SCHEDULED_SERVER_STATE_MONITOR_STATE_FILE=/home/<USER>/static/archive/docker_disk_usage.txt
      - SCHEDULED_SERVER_STATE_MONITOR_CRON=0 0/10 * * * ?
      - SCHEDULED_SERVER_STATE_MONITOR_WARN_LIMIT=95
      - SCHEDULED_SERVER_STATE_MONITOR_NOTIFY_PHONE=15822780432
      # 推送配置
      - SYSTEM_MONITOR_PUSH_MSG_TYPE_ID=88
      - SYNC_SYSTEM_MONITOR_TO_PUSH_MSG=false
      - MOS_VIDEO_URL=
      # 可选配置
      #- TDENGINE_CONNECT_TYPE=restful
      #- TDENGINE_HOST=**************
      #- TDENGINE_PORT=6041
      #- SCHEDULED_MODEL_SYSTEM_ALIVE_CHECK_CRON=0 */5 * * * ?
      #- SCHEDULED_COLLECT_FILE_LOAD_DELAY=300000
      #- SCHEDULED_ACCESS_NODE_STATUS_DETECT_CHECK_MODE=0
      #- SCHEDULED_POSITION_SUPERIOR_UNIT_DATA_SYNC_TIME_RANGE=1440
      #- SCHEDULED_POSITION_SUPERIOR_UNIT_DATA_SYNC_INIT_RATE=600000
      #- SCHEDULED_POSITION_SUPERIOR_UNIT_DATA_SYNC_FIXED_RATE=600000
    volumes:
      - "data-monitor-log:/home/<USER>/logs"
      - "data-monitor-image:/home/<USER>"
      - "/home/<USER>/file_sync/archive:/home/<USER>/static/archive"
    depends_on:
      - backend-data-warn
    #ports:
    #  - "${PORT_DATA_MONITOR}:9001"

  # 数据预警服务 - 异常检测和告警
  backend-data-warn:
    <<: *iot_backend_defaults
    container_name: backend-data-warn
    image: harbor2.qdbdtd.com:8088/bjproduct/platform/services/data_warn:${BACKEND_WARN_VERSION}
    environment:
      - RABBITMQ_VIRTUALHOST=${RMB_DW_VH}
    volumes:
      - "data-warn-log:/home/<USER>/logs"
    depends_on:
      - backend-converter-monitor
    #ports:
    #  - "${PORT_DATA_WARN}:9001"

  # 数据解析服务 - 原始数据转换和标准化
  backend-converter-monitor:
    <<: *iot_backend_defaults
    container_name: backend-converter-monitor
    image: harbor2.qdbdtd.com:8088/bjproduct/platform/services/parse-data:${BACKEND_PARSE_VERSION}
    environment:
      - RABBITMQ_VIRTUALHOST=${RMB_PD_VH}
    volumes:
      - "converter-monitor-log:/home/<USER>/logs"
    depends_on:
      - backend-data-storage
    #ports:
    #  - "${PORT_CONVERTER_MONITOR}:9001"

  # 消息推送服务
  backend-push-msg:
    <<: *backend-defaults
    image: harbor2.qdbdtd.com:8088/bjproduct/platform/services/push-msg:${BACKEND_PUSH_MSG}
    environment:
      # 短信服务配置 (已无效化，SMS_URL 中加了异常字符串 111)
      - SMS_URL=${SMS_URL:-http://*************:8080/smg/webService/smsOper111?wsdl}
      - SMS_USERNAME=${SMS_USERNAME:-zdyjpt}
      - SMS_PASSWORD=${SMS_PASSWORD:-zdyjpt123}
      # 推送服务配置
      - PUSH_COMPANY=${PUSH_COMPANY}
      - HISTORY_SAVE_MODE=${HISTORY_SAVE_MODE:-influxdb}
      # 告警配置
      - ALARM_TIME_INTERVAL=${ALARM_TIME_INTERVAL:-1}
      - ALARM_LINKAGE_PHONES=${ALARM_LINKAGE_PHONES:-10054799573}
      # 数据库联动配置 (已无效化，用户名 sa 后面加了 xxxx)
      - MSSQL_URL=${ALARM_LINKAGE_DB_URL:-***********************************************************************************************}
      - MSSQL_USERNAME=${ALARM_LINKAGE_DB_USER:-saxxxx}
      - MSSQL_PASSWORD=${ALARM_LINKAGE_DB_PASSWORD:-KjfdU#183&}
    #ports:
    #  - "${PORT_PUSH_MSG}:9001"
    depends_on:
      - nacos
      - backend-process-user-permission
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9001/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  #-----------------------------------------------------------------------------
  # 前端服务 - 用户界面层（依赖后端服务）
  #-----------------------------------------------------------------------------

  # 联网系统前端
  frontend-biz-data_ims:
    <<: *iot_frontend_defaults
    container_name: frontend-biz-data_ims
    image: harbor2.qdbdtd.com:8088/bjproduct/platform/front-services/biz-data-ims:${FRONTEND_DATA_IMS_VERSION}
    depends_on:
      - backend-data-monitor
    #ports:
    #  - "${PORT_FRONTEND_DATA_IMS}:80"

  # 数据接入管理前端
  frontend-module-data_access:
    <<: *iot_frontend_defaults
    container_name: frontend-module-data_access
    image: harbor2.qdbdtd.com:8088/bjproduct/platform/front-services/base-data-access:${FRONTEND_DATA_ACCESS_VERSION}
    depends_on:
      - backend-data-storage
    #ports:
    #  - "${PORT_FRONTEND_DATA_ACCESS}:80"

  # 前端告警服务模块
  frontend-module-alarm_serve:
    <<: *nginx-reset
    image: harbor2.qdbdtd.com/bjproduct/platform/front-services/alarm_serve:${FRONTEND_ALARM_VERSION}
    depends_on:
      - backend-push-msg

  # 数据查看前端
  frontend-module-data_view:
    <<: *iot_frontend_defaults
    container_name: frontend-module-data_view
    image: harbor2.qdbdtd.com:8088/bjproduct/platform/front-services/base-data-view:${FRONTEND_DATA_VIEW_VERSION}
    depends_on:
      - backend-data-monitor
    #ports:
    #  - "${PORT_FRONTEND_DATA_VIEW}:80"

  # 页面编辑器前端
  frontend-module-page_builder:
    <<: *iot_frontend_defaults
    container_name: frontend-module-page_builder
    image: harbor2.qdbdtd.com:8088/bjproduct/platform/front-services/base-page-editor:${FRONTEND_PAGE_EDITOR_VERSION}
    #ports:
    #  - "${PORT_FRONTEND_PAGE_BUILDER}:80"
  # 页面渲染器前端
  frontend-module-page_render:
    <<: *iot_frontend_defaults
    container_name: frontend-module-page_render
    image: harbor2.qdbdtd.com:8088/bjproduct/platform/front-services/base-page-editor:render-${FRONTEND_PAGE_EDITOR_VERSION}
    depends_on:
      - frontend-module-page_builder
    #ports:
    #  - "${PORT_FRONTEND_PAGE_RENDER}:80"

  #-----------------------------------------------------------------------------
  # 拓扑服务 - 可选的拓扑编辑和渲染功能
  #-----------------------------------------------------------------------------

  ## 拓扑渲染器
  #frontend-module-topo_render:
  #  <<: *iot_frontend_defaults
  #  container_name: frontend-module-topo_render
  #  image: harbor2.qdbdtd.com:8088/bjproduct/platform/front-services/base-topo-render:${FRONTEND_TOPO_RENDER_VERSION}

  ## 拓扑编辑器
  #frontend-module-topo_editor:
  #  <<: *iot_frontend_defaults
  #  container_name: frontend-module-topo_editor
  #  image: harbor2.qdbdtd.com:8088/bjproduct/platform/front-services/base-topo-editor:${FRONTEND_TOPO_EDITOR_VERSION}

  ## 拓扑渲染器（嵌入式）
  #frontend-module-topo_embed:
  #  <<: *iot_frontend_defaults
  #  container_name: frontend-module-topo_embed
  #  image: harbor2.qdbdtd.com:8088/bjproduct/platform/front-services/base-topo-render:embed-${FRONTEND_TOPO_RENDER_VERSION}

  ## 新版拓扑编辑器
  #frontend-base-prtopo-editor:
  #  <<: *iot_frontend_defaults
  #  container_name: frontend-base-prtopo-editor
  #  image: harbor2.qdbdtd.com:8088/bjproduct/platform/front-services/base-prtopo-editor:${FRONTEND_TOPO_VERSION}

  ## 新版拓扑预览
  #frontend-base-prtopo-embed:
  #  <<: *iot_frontend_defaults
  #  container_name: frontend-base-prtopo-embed
  #  image: harbor2.qdbdtd.com:8088/bjproduct/platform/front-services/base-prtopo-editor:embed-${FRONTEND_TOPO_VERSION}

  #-----------------------------------------------------------------------------
  # GraphQL 数据服务 - 可选的高级数据查询接口
  #-----------------------------------------------------------------------------

  # Hasura GraphQL Engine (当前未启用)
  #graphql-engine:
  #  <<: *iot_backend_defaults
  #  container_name: graphql-engine
  #  image: harbor2.qdbdtd.com:8088/middleware/hasura/graphql-engine:${BAAS_VERSION}
  #  environment:
  #    - HASURA_GRAPHQL_ENABLE_CONSOLE=true
  #    #- HASURA_GRAPHQL_DATABASE_URL=${POSTGRES_URL}
  #  volumes:
  #    - "./hasura/migrations:/hasura-migrations:ro"
  #    - "./hasura/metadata:/hasura-metadata:ro"
  #  deploy:
  #    resources:
  #      limits:
  #        memory: ${HASURA_MEMORY_LIMIT}
  #  depends_on:
  #    - backend-data-monitor

