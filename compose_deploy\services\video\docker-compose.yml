# =============================================================================
# YAML 模板定义 (按功能分组)
# =============================================================================

# 视频服务基础模板
x-video-defaults: &video_defaults
  restart: unless-stopped
  logging:
    driver: "json-file"
    options:
      max-size: "1g"
      max-file: "2"
  env_file:
    - ../base/.env
    - .env
  networks:
    - middle

# Nginx 前端服务模板
x-nginx-defaults: &nginx_defaults
  <<: *video_defaults
  healthcheck:
    test: ["CMD", "nginx", "-t"]
    interval: 30s
    timeout: 10s
    retries: 3
    start_period: 30s

# =============================================================================
# 网络和存储卷定义
# =============================================================================

networks:
  # 视频服务网络 - 与其他服务共享网络环境
  middle:
    external: true
    name: ${DOCKER_NETWORK}

volumes:
  # 视频服务 Redis 数据存储
  video-redis:
    external: true

# =============================================================================
# 服务定义 (按视频处理架构和依赖关系排序)
# =============================================================================

services:
  # ---------------------------------------------------------------------------
  # 基础设施层 - 缓存和存储服务
  # ---------------------------------------------------------------------------

  # Redis 缓存服务 (视频流信息缓存)
  video-redis:
    <<: *video_defaults
    image: harbor2.qdbdtd.com:8088/middleware/redis:4.0.11
    container_name: video-redis
    volumes:
      # Redis 数据持久化存储
      - video-redis:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # ---------------------------------------------------------------------------
  # 流媒体服务层 - 视频流处理和分发
  # ---------------------------------------------------------------------------

  # SRS 流媒体服务器 (视频流接收和分发)
  video-srs:
    <<: *video_defaults
    image: harbor2.qdbdtd.com:8088/middleware/srs:6.0.10
    container_name: video-srs
    volumes:
      # SRS 配置文件挂载
      - $PWD/srs.conf:/usr/local/srs/conf/docker.conf
    #ports:
    #  - "${PORT_PROXY}:8080"  # HTTP 流媒体端口
    #  - "${PORT_SRS}:1935"    # RTMP 推流端口
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:1985/api/v1/summaries"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # VideoJS Flow 协议转换服务 (HTTP -> WebSocket)
  videojs-flow:
    <<: *video_defaults
    image: harbor2.qdbdtd.com:8088/middleware/videojs-flow:1.1.0
    container_name: videojs-flow
    environment:
      # SRS 服务器地址配置
      - HOST=http://video-srs:8080
    depends_on:
      - video-srs

  # ---------------------------------------------------------------------------
  # 业务处理层 - 视频业务逻辑和任务处理
  # ---------------------------------------------------------------------------

  # Celery 异步任务处理服务
  celery:
    <<: *video_defaults
    image: harbor2.qdbdtd.com:8088/bjproduct/platform/videos/video-server:${VERSION_BACKEND}
    container_name: video-celery
    environment:
      # Redis 消息队列配置
      - BROKER_URL=redis://video-redis:6379/0
      # 后端服务接口配置
      - BACKEND_URL=http://video-server:9000/devices
      # RTMP 推流服务器配置
      - RTMP_SERVER_URI=rtmp://video-srs:1935/live
      # SRS API 接口配置
      - SRS_URL=http://video-srs:1985/api/v1/streams/?count=1500
      # 任务执行周期配置 (如果视频流超过300路，建议设置为600秒)
      - CYCLE_SECONDS=150
    logging:
      driver: "json-file"
      options:
        max-size: "200k"
        max-file: "10"
    depends_on:
      - video-redis
      - video-srs
    command: ["celery", "-A", "c.app", "worker", "-B"]

  # 视频预览服务 (视频截图和预览图生成)
  video-preview:
    <<: *video_defaults
    image: harbor2.qdbdtd.com:8088/bjproduct/platform/videos/video_preview:${VERSION_BACKENDS}
    container_name: video-preview
    volumes:
      # 预览图片存储目录
      - ./image:/app/image
    #ports:
    #  - "9019:19850"
    depends_on:
      - video-srs

  # 视频后端服务 (视频管理 API)
  video-server:
    <<: *video_defaults
    image: harbor2.qdbdtd.com:8088/bjproduct/platform/videos/video-server:${VERSION_BACKEND}
    container_name: video-server
    environment:
      # 数据库连接配置 (可选，使用基础设施数据库)
      # - POSTGRES_URL=postgres://${POSTGRES_USERNAME:-postgres}:${POSTGRES_PASSWORD:-postgres}@${POSTGRES_HOST:-postgres}:${POSTGRES_PORT:-5432}/${POSTGRES_DATABASE:-gis}
      # Redis 消息队列配置
      - BROKER_URL=redis://video-redis:6379/0
      # SRS API 接口配置
      - SRS_URL=http://video-srs:1985/api/v1/streams/?count=1500
    depends_on:
      - video-redis
      - video-srs
    # 生产环境 Gunicorn 配置 (默认 worker 数量: 4，可根据需要调整)
    # command: ["gunicorn", "--timeout", "120", "-w", "24", "-b", ":9000", "--log-level", "debug", "--access-logfile", "-", "--error-logfile", "-", "run:app"]
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # ---------------------------------------------------------------------------
  # 前端界面层 - 用户界面和监控展示
  # ---------------------------------------------------------------------------

  # 视频前端界面 (视频监控管理界面)
  video-frontend:
    <<: *nginx_defaults
    image: harbor2.qdbdtd.com:8088/bjproduct/platform/front-services/video-front:${VERSION_FRONTEND}
    container_name: video-frontend
    environment:
      # 后端服务接口地址
      - BACKEND_URL=http://video-server:9000
      # SRS 流媒体服务地址
      - SRS_URL=http://video-srs:1985
    depends_on:
      - video-server
      - video-srs
    #ports:
    #  - "${PORT_VIDEO_FRONTEND}:80"
