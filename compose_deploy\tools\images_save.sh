#!/bin/bash

#===============================================================================
# Docker 镜像保存工具
#===============================================================================
# 功能描述: 批量保存本地 Docker 镜像为 tar 文件
# 适用场景: 离线环境部署、镜像备份、镜像迁移
# 创建时间: 2024年
#
# 使用方法: ./images_save.sh
# 输出目录: /home/<USER>/
#===============================================================================

# 遇到错误立即退出
set -e

#===============================================================================
# 全局变量配置
#===============================================================================

# 镜像保存目录
SAVE_PATH=/home/<USER>

#===============================================================================
# 镜像清理
#===============================================================================

# 删除未使用的镜像（没有被任何容器使用的镜像）
echo "正在清理不再使用的镜像..."
docker image prune -a -f >/dev/null 2>&1 || true
echo "未使用镜像清理完成"

#===============================================================================
# 镜像信息收集
#===============================================================================

# 获取本地所有镜像数量（排除表头）
echo "正在扫描本地 Docker 镜像..."
count=$(docker images | awk 'NR>=2{print}' | wc -l)

# 获取所有镜像名称数组
IMAGE_NAMES=($(docker images | awk 'NR>=2{print $1}'))

# 获取所有镜像标签数组
IMAGE_TAGS=($(docker images | awk 'NR>=2{print $2}'))

echo "发现 $count 个本地镜像需要保存"

#===============================================================================
# 目录准备
#===============================================================================

# 检查并创建保存目录
if [ ! -d "$SAVE_PATH" ]; then
  echo "创建镜像保存目录: $SAVE_PATH"
  mkdir -p "$SAVE_PATH"
else
  echo "使用现有保存目录: $SAVE_PATH"
fi

#===============================================================================
# 镜像保存处理
#===============================================================================

echo "开始批量保存镜像..."

# 遍历所有镜像进行保存
for ((i=0; i<"$count"; i++)); do
  # 构建完整的镜像名称（包含标签）
  SOURCE="${IMAGE_NAMES[$i]}:${IMAGE_TAGS[$i]}"

  # 生成保存文件名（提取镜像名的最后部分，避免路径分隔符问题）
  image_base_name=$(echo "${IMAGE_NAMES[$i]}" | awk -F/ '{print $NF}')
  TARGET="$SAVE_PATH/${image_base_name}_${IMAGE_TAGS[$i]}.tar"

  # 检查文件是否已存在，避免重复保存
  if [ -f "$TARGET" ]; then
    echo "跳过已存在的镜像: $SOURCE -> $(basename "$TARGET")"
  else
    echo "正在保存镜像: $SOURCE -> $(basename "$TARGET")"

    # 执行镜像保存操作
    if docker save "$SOURCE" -o "$TARGET"; then
      # 显示保存后的文件大小
      file_size=$(du -h "$TARGET" | cut -f1)
      echo "  ✓ 保存成功，文件大小: $file_size"
    else
      echo "  ✗ 保存失败: $SOURCE"
      exit 1
    fi
  fi
done

#===============================================================================
# 保存完成统计
#===============================================================================

echo ""
echo "==============================================================================="
echo "镜像保存完成！"
echo "==============================================================================="
echo "保存目录: $SAVE_PATH"
echo "处理镜像: $count 个"

# 统计保存的文件
saved_files=$(find "$SAVE_PATH" -name "*.tar" -type f | wc -l)
total_size=$(du -sh "$SAVE_PATH" | cut -f1)

echo "保存文件: $saved_files 个"
echo "总计大小: $total_size"
echo ""
echo "使用说明:"
echo "  1. 可将 $SAVE_PATH 目录打包传输到目标机器"
echo "  2. 在目标机器上使用 'docker load -i <文件名>' 加载镜像"
echo "  3. 批量加载: find $SAVE_PATH -name '*.tar' -exec docker load -i {} \\;"
echo "==============================================================================="
