# Services 目录

本目录包含了综合信息平台的所有微服务组件，按照功能模块进行组织。

## 目录结构

```text
services/
├── base/           # 基础服务（数据库、消息队列、缓存等）
├── mos/            # MOS基础服务（用户权限管理）
├── iot/            # IOT平台服务（数据接入、预警报警、Topo）
├── gis/            # GIS业务服务
├── mes/            # MES业务服务
├── video/          # 视频服务
├── workflow/       # 业务审批流服务
├── app/            # 移动应用服务
├── ssh/            # SSH服务
└── monitoring/     # 监控工具服务（Grafana、Prometheus等）
```

## 服务说明

### base - 基础服务

包含系统运行所需的基础组件：

- **PostgreSQL** 主数据库 (GIS数据库)
- **PostgreSQL Business** 业务数据库 (MES数据库)
- **Redis** 缓存数据库
- **RabbitMQ** 消息队列
- **TDengine** 时序数据库
- **MinIO** 对象存储
- **Node-RED** 数据网关
- **SFTPGo** 文件传输服务

### mos - MOS基座后管系统

提供用户权限管理和基础业务功能：

- **用户认证授权** 统一身份认证
- **权限管理** 角色权限控制
- **基础数据管理** 系统基础配置
- **Nacos 配置中心** 微服务配置管理
- **前端网关** Nginx 反向代理
- **前端配置服务** 导航栏配置管理

### iot - IOT物联网平台

物联网数据接入和处理：

- **数据接入系统** 传感器数据采集
- **数据存储服务** 时序数据存储
- **数据监控服务** 实时数据监控
- **预警报警服务** 智能报警推送
- **Topo 拓扑图** 可视化拓扑管理
- **页面编辑器** 动态页面配置
- **Hasura GraphQL API** 统一数据接口

### gis - GIS地理信息系统

地理信息系统相关功能：

- **GIS 前端服务** 地图可视化界面
- **GIS 后端业务** 空间数据处理
- **三维场景** 3D 地理信息展示
- **空间分析** 地理数据分析工具

### mes - MES业务前端系统

业务前端系统：

- **MES 前端界面** 生产管理界面
- **MES 后端业务** 生产执行逻辑
- **生产管理** 生产计划和调度
- **工艺控制** 生产工艺管理
- **质量管理** 产品质量控制

### video - 视频监控服务

视频监控和流媒体服务：

- **SRS 流媒体服务器** RTMP/HTTP 流媒体
- **视频管理服务** 摄像头管理
- **实时预览** 视频实时播放
- **录像回放** 历史视频查看
- **视频 Redis** 视频服务缓存

### workflow - 工作流服务

工作流引擎和审批流程：

- **工作流引擎** 流程执行引擎
- **审批流程管理** 业务审批流程
- **流程设计器** 可视化流程设计
- **任务管理** 待办任务处理

### app - 移动应用服务

移动端应用支持服务：

- **移动端 API** 移动应用接口
- **推送服务** 消息推送功能
- **离线同步** 数据离线同步

### ssh - SSH远程服务

远程访问和管理服务：

- **SSH 访问** 远程终端访问
- **端口转发** 本地端口映射
- **调试支持** 开发调试工具

### monitoring - 监控工具服务

系统监控和运维工具：

- **Grafana** 监控面板和可视化
- **Prometheus** 指标收集和存储
- **Portainer** 容器管理界面
- **AlertManager** 告警管理
- **Node Exporter** 主机监控
- **cAdvisor** 容器监控

## 📁 服务目录结构

每个服务目录都包含：

- **docker-compose.yml** - Docker Compose 配置文件
- **.env** - 环境变量配置文件
- **config/** - 服务配置文件目录
- **data/** - 数据管理目录
  - **init/** - 初始化 SQL 脚本
  - **migrations/** - 数据库迁移脚本
- **static/** - 静态资源文件（如果需要）
- **README.md** - 服务说明文档（如果有）

## 🚀 部署管理

### 统一部署

使用 `compose_deploy.sh` 脚本进行统一部署和管理：

```bash
# 全新部署
./compose_deploy.sh deploy -i

# 启动所有服务
./compose_deploy.sh start

# 停止所有服务
./compose_deploy.sh stop

# 重启所有服务
./compose_deploy.sh restart
```

### 服务开关控制

在 `config/.config.env` 中配置服务开关：

```bash
DEPLOY_VIDEO_SERVICE=false    # 视频服务
DEPLOY_MOS_SERVICE=true       # MOS基础服务
DEPLOY_IOT_SERVICE=true       # IOT平台服务
DEPLOY_MES_SERVICE=false      # MES业务服务
DEPLOY_GIS_SERVICE=false      # GIS业务服务
DEPLOY_FLOW_SERVICE=false     # 工作流服务
DEPLOY_APP_SERVICE=false      # 移动应用服务
```

### 服务依赖关系

- **base** - 基础服务，必须首先启动
- **mos** - 依赖 base 服务
- **iot** - 依赖 base 和 mos 服务
- **其他业务服务** - 依赖 base、mos 和 iot 服务

## 🔧 服务配置

### 数据库配置

- **PostgreSQL 主库**: 端口 5432，用于 GIS 和 IOT 数据
- **PostgreSQL 业务库**: 端口 5433，用于 MES 和工作流数据
- **Redis**: 端口 6379，用于缓存和会话
- **TDengine**: 端口 6030，用于时序数据

### 网络配置

所有服务使用统一的 Docker 网络 `middle`，支持服务间通信。

### 数据持久化

使用 Docker 命名卷进行数据持久化，主要数据卷包括：
- `base-postgres` - PostgreSQL 主数据库
- `base-business-postgres` - PostgreSQL 业务数据库
- `base-redis` - Redis 数据
- `base-tdengine-data` - TDengine 数据
- `bladex-minio` - MinIO 对象存储

## 🔗 相关文档

- [主部署脚本说明](../compose_deploy.sh)
- [核心库脚本说明](../libs/README.md)
- [运维工具说明](../tools/README.md)
- [版本变更日志](../CHANGELOG.md)
