#!/bin/bash

# shellcheck source=_common.sh
# shellcheck disable=SC1091

#===============================================================================
# CentOS 7 系统初始化脚本
#===============================================================================
# 功能描述: CentOS 7 系统环境初始化和 Docker 环境配置
# 适用系统: CentOS 7.x
# 执行权限: 需要 root 权限
# 依赖文件: _common.sh (共通函数库)
# 创建时间: 2024-10-24
# 更新时间: 2025-01-30
#===============================================================================

set -e  # 遇到错误立即退出

# 加载共通函数库
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
if [[ -f "$SCRIPT_DIR/_common.sh" ]]; then
  # shellcheck source=./_common.sh
  source "$SCRIPT_DIR/_common.sh"
else
  echo "错误: 找不到共通函数库 _common.sh"
  exit 1
fi

# 全局配置
BACKUP_DIR="/root/centos7-init-backup"
YUM_REPOS_BACKUP="$BACKUP_DIR/yum.repos.d"

# 创建备份目录
create_backup_dir() {
  safe_create_dir "$BACKUP_DIR"
  safe_create_dir "$YUM_REPOS_BACKUP"
  log_info "备份目录创建完成: $BACKUP_DIR"
}

# 备份重要配置文件
backup_configs() {
  log_info "备份重要配置文件..."

  # 备份 SSH 配置
  if file_exists "/etc/ssh/sshd_config"; then
    safe_copy_file "/etc/ssh/sshd_config" "$BACKUP_DIR/sshd_config.bak"
  fi

  # 备份 SELinux 配置
  if file_exists "/etc/selinux/config"; then
    safe_copy_file "/etc/selinux/config" "$BACKUP_DIR/selinux_config.bak"
  fi

  # 备份 YUM 源配置
  if dir_exists "/etc/yum.repos.d"; then
    cp -r /etc/yum.repos.d/* "$YUM_REPOS_BACKUP/" 2>/dev/null || true
    log_info "YUM 源配置已备份"
  fi

  # 备份 sysctl 配置
  if file_exists "/etc/sysctl.conf"; then
    safe_copy_file "/etc/sysctl.conf" "$BACKUP_DIR/sysctl.conf.bak"
  fi

  log_success "配置文件备份完成"
}

# 关闭防火墙
disable_firewall() {
  log_info "关闭防火墙..."

  if service_exists firewalld; then
    safe_stop_service firewalld
    safe_disable_service firewalld
    log_success "防火墙已关闭并禁用自启"
  else
    log_info "防火墙服务不存在"
  fi

  # 清除 iptables 规则
  iptables -F 2>/dev/null || true
  iptables -X 2>/dev/null || true
  iptables -t nat -F 2>/dev/null || true
  iptables -t nat -X 2>/dev/null || true
  log_success "防火墙规则已清除"
}

# SSH 优化
optimize_ssh() {
  log_info "优化 SSH 配置..."

  local ssh_config="/etc/ssh/sshd_config"
  local ssh_optimized=false

  if file_exists "$ssh_config"; then
    # 禁用 DNS 查找
    if grep -q "^#UseDNS yes" "$ssh_config"; then
      sed -i 's/^#UseDNS yes/UseDNS no/' "$ssh_config"
      ssh_optimized=true
    elif grep -q "^UseDNS yes" "$ssh_config"; then
      sed -i 's/^UseDNS yes/UseDNS no/' "$ssh_config"
      ssh_optimized=true
    elif ! grep -q "^UseDNS no" "$ssh_config"; then
      echo "UseDNS no" >> "$ssh_config"
      ssh_optimized=true
    fi

    # 禁用 GSSAPI 认证（加速登录）
    if ! grep -q "^GSSAPIAuthentication no" "$ssh_config"; then
      echo "GSSAPIAuthentication no" >> "$ssh_config"
      ssh_optimized=true
    fi

    if [[ "$ssh_optimized" == true ]]; then
      # 重启 SSH 服务
      if service_exists sshd; then
        systemctl restart sshd
        log_success "SSH 配置优化完成并重启服务"
      else
        log_warning "SSH 服务不存在，无法重启"
      fi
    else
      log_info "SSH 配置已经是最优状态"
    fi
  else
    log_warning "SSH 配置文件不存在: $ssh_config"
  fi
}

# 永久关闭 SELinux
disable_selinux() {
  log_info "关闭 SELinux..."

  local selinux_config="/etc/selinux/config"

  if file_exists "$selinux_config"; then
    # 修改配置文件
    if grep -q "^SELINUX=enforcing" "$selinux_config"; then
      sed -i 's/^SELINUX=enforcing/SELINUX=disabled/' "$selinux_config"
      log_info "SELinux 配置已修改为 disabled"
    elif grep -q "^SELINUX=permissive" "$selinux_config"; then
      sed -i 's/^SELINUX=permissive/SELINUX=disabled/' "$selinux_config"
      log_info "SELinux 配置已修改为 disabled"
    elif ! grep -q "^SELINUX=disabled" "$selinux_config"; then
      sed -i '/^SELINUX=/c\SELINUX=disabled' "$selinux_config"
      log_info "SELinux 配置已设置为 disabled"
    fi

    # 临时关闭 SELinux
    if command_exists setenforce; then
      setenforce 0 2>/dev/null || log_warning "无法临时关闭 SELinux（可能已经是 disabled 状态）"
    fi

    log_success "SELinux 已永久关闭（重启后生效）"
  else
    log_warning "SELinux 配置文件不存在: $selinux_config"
  fi
}

# 配置 DNS
configure_dns() {
  log_info "配置 DNS..."

  local resolv_conf="/etc/resolv.conf"

  # 添加 Google DNS
  if ! grep -q "nameserver 8.8.8.8" "$resolv_conf"; then
    echo "nameserver 8.8.8.8" >> "$resolv_conf"
    log_info "添加 Google DNS: 8.8.8.8"
  fi

  if ! grep -q "nameserver 8.8.4.4" "$resolv_conf"; then
    echo "nameserver 8.8.4.4" >> "$resolv_conf"
    log_info "添加 Google DNS: 8.8.4.4"
  fi

  log_success "DNS 配置完成"
}

# 更新 YUM 源
update_yum_repos() {
  log_info "更新 YUM 源..."

  # 安装 wget（如果不存在）
  if ! command_exists wget; then
    log_info "安装 wget..."
    yum install -y wget
  fi

  # 备份并删除原有源
  if dir_exists "/etc/yum.repos.d"; then
    rm -rf /etc/yum.repos.d/CentOS* 2>/dev/null || true
    log_info "删除原有 CentOS 源"
  fi

  # 下载阿里云源
  log_info "下载阿里云 YUM 源..."
  if curl -o /etc/yum.repos.d/CentOS-Base.repo http://mirrors.aliyun.com/repo/Centos-7.repo; then
    log_success "CentOS 基础源下载成功"
  else
    log_error "CentOS 基础源下载失败"
    return 1
  fi

  if wget -O /etc/yum.repos.d/epel.repo http://mirrors.aliyun.com/repo/epel-7.repo; then
    log_success "EPEL 源下载成功"
  else
    log_warning "EPEL 源下载失败，继续执行"
  fi

  # 更新系统
  log_info "更新系统包..."
  yum clean all
  yum makecache

  if [[ "$FORCE_MODE" == true ]] || confirm_operation "是否执行系统更新？（可能需要较长时间）" "y"; then
    yum -y update
    log_success "系统更新完成"
  else
    log_info "跳过系统更新"
  fi

  log_success "YUM 源配置完成"
}

# 安装基础软件
install_basic_packages() {
  log_info "安装基础软件包..."

  local basic_packages=(
    "ntp"
    "tree"
    "vim"
    "net-tools"
    "iftop"
    "iotop"
    "curl-devel"
    "expat-devel"
    "gettext-devel"
    "openssl-devel"
    "zlib-devel"
    "asciidoc"
    "gcc"
    "perl-ExtUtils-MakeMaker"
    "wget"
    "unzip"
    "tar"
    "which"
    "lsof"
    "htop"
  )

  log_info "安装软件包: ${basic_packages[*]}"

  if yum install -y "${basic_packages[@]}"; then
    log_success "基础软件包安装完成"
  else
    log_error "基础软件包安装失败"
    return 1
  fi

  # 启动并启用 NTP 服务
  if service_exists ntpd; then
    systemctl enable ntpd
    systemctl start ntpd
    log_success "NTP 服务已启动并设置为自启"
  fi
}

# 安装 Docker（在线方式）
install_docker_online() {
  log_info "安装 Docker（在线方式）..."

  # 检查是否已安装
  if is_docker_installed; then
    log_warning "Docker 已安装: $(get_docker_version)"
    if ! confirm_operation "是否重新安装 Docker？"; then
      log_info "跳过 Docker 安装"
      return 0
    fi
  fi

  # 安装依赖
  local docker_deps=(
    "yum-utils"
    "device-mapper-persistent-data"
    "lvm2"
  )

  log_info "安装 Docker 依赖: ${docker_deps[*]}"
  yum install -y "${docker_deps[@]}"

  # 添加 Docker 官方源
  log_info "添加 Docker 官方源..."
  yum-config-manager --add-repo https://download.docker.com/linux/centos/docker-ce.repo

  # 安装 Docker CE
  log_info "安装 Docker CE..."
  if yum install -y docker-ce; then
    log_success "Docker CE 安装成功"
  else
    log_error "Docker CE 安装失败"
    return 1
  fi

  # 启动并启用 Docker 服务
  systemctl enable docker
  systemctl start docker

  if wait_for_service docker 30; then
    log_success "Docker 服务启动成功"
  else
    log_error "Docker 服务启动失败"
    return 1
  fi

  # 验证安装
  if docker --version >/dev/null 2>&1; then
    log_success "Docker 安装验证成功: $(docker --version)"
  else
    log_error "Docker 安装验证失败"
    return 1
  fi
}

# 配置 Docker 加速器
configure_docker_registry() {
  log_info "配置 Docker 镜像加速器..."

  safe_create_dir "/etc/docker"

  cat > /etc/docker/daemon.json << 'EOF'
{
  "log-driver": "json-file",
  "log-opts": {
  "max-size": "50m",
  "max-file": "3"
  },
  "registry-mirrors": [
  "https://5gmkcj2a.mirror.aliyuncs.com",
  "https://docker.m.daocloud.io"
  ],
  "storage-driver": "overlay2",
  "storage-opts": [
  "overlay2.override_kernel_check=true"
  ]
}
EOF

  # 重启 Docker 服务
  reload_systemd
  systemctl restart docker

  if wait_for_service docker 30; then
    log_success "Docker 镜像加速器配置完成"
  else
    log_error "Docker 服务重启失败"
    return 1
  fi
}

# 安装 Docker Compose
install_docker_compose_online() {
  log_info "安装 Docker Compose（在线方式）..."

  # 检查是否已安装
  if is_docker_compose_installed; then
    log_warning "Docker Compose 已安装: $(get_docker_compose_version)"
    if ! confirm_operation "是否重新安装 Docker Compose？"; then
      log_info "跳过 Docker Compose 安装"
      return 0
    fi
  fi

  # 安装依赖
  local compose_deps=(
    "epel-release"
    "openssl-devel"
    "gcc"
    "python3-pip"
  )

  log_info "安装 Docker Compose 依赖: ${compose_deps[*]}"
  yum install -y "${compose_deps[@]}"

  # 升级 pip
  log_info "升级 pip..."
  pip3 install --upgrade pip

  # 安装 Docker Compose
  log_info "安装 Docker Compose..."
  if pip3 install docker-compose==1.29.2; then
    log_success "Docker Compose 安装成功"
  else
    log_error "Docker Compose 安装失败"
    return 1
  fi

  # 验证安装
  if docker-compose --version >/dev/null 2>&1; then
    log_success "Docker Compose 安装验证成功: $(docker-compose --version)"
  else
    log_error "Docker Compose 安装验证失败"
    return 1
  fi
}

# 开启路由转发
enable_ip_forward() {
  log_info "开启 IP 路由转发..."

  local sysctl_conf="/etc/sysctl.conf"

  if ! grep -q "^net.ipv4.ip_forward = 1" "$sysctl_conf"; then
    echo "net.ipv4.ip_forward = 1" >> "$sysctl_conf"
    log_info "添加路由转发配置到 sysctl.conf"
  fi

  # 立即生效
  sysctl -p

  # 验证配置
  local forward_status=$(cat /proc/sys/net/ipv4/ip_forward)
  if [[ "$forward_status" == "1" ]]; then
    log_success "IP 路由转发已开启"
  else
    log_warning "IP 路由转发配置可能未生效"
  fi
}

# 系统优化
optimize_system() {
  log_info "进行系统优化..."

  local sysctl_conf="/etc/sysctl.conf"

  # 网络优化配置
  local network_optimizations=(
    "net.core.rmem_default = 262144"
    "net.core.rmem_max = 16777216"
    "net.core.wmem_default = 262144"
    "net.core.wmem_max = 16777216"
    "net.ipv4.tcp_rmem = 4096 65536 16777216"
    "net.ipv4.tcp_wmem = 4096 65536 16777216"
    "net.core.netdev_max_backlog = 5000"
    "net.ipv4.tcp_window_scaling = 1"
  )

  for optimization in "${network_optimizations[@]}"; do
    local key=$(echo "$optimization" | cut -d'=' -f1 | tr -d ' ')
    if ! grep -q "^$key" "$sysctl_conf"; then
      echo "$optimization" >> "$sysctl_conf"
    fi
  done

  # 应用配置
  sysctl -p >/dev/null 2>&1

  log_success "系统优化配置完成"
}

# 清理系统
cleanup_system() {
  log_info "清理系统..."

  # 清理 YUM 缓存
  yum clean all

  # 清理临时文件
  rm -rf /tmp/* 2>/dev/null || true
  rm -rf /var/tmp/* 2>/dev/null || true

  # 清理日志文件（保留最近的）
  find /var/log -name "*.log" -type f -mtime +30 -delete 2>/dev/null || true

  log_success "系统清理完成"
}

# 显示安装后信息
show_post_init_info() {
  show_title "CentOS 7 初始化完成"

  echo "系统信息:"
  echo "  操作系统: $(cat /etc/redhat-release 2>/dev/null || echo '未知')"
  echo "  内核版本: $(uname -r)"
  echo "  系统架构: $(uname -m)"
  echo ""

  echo "服务状态:"
  echo "  防火墙: $(check_docker_service_status firewalld)"
  echo "  SELinux: $(getenforce 2>/dev/null || echo '未知')"
  echo "  Docker: $(check_docker_service_status docker)"
  echo "  NTP: $(check_docker_service_status ntpd)"
  echo ""

  echo "软件版本:"
  if is_docker_installed; then
    echo "  Docker: $(get_docker_version)"
  fi
  if is_docker_compose_installed; then
    echo "  Docker Compose: $(get_docker_compose_version)"
  fi
  echo ""

  echo "配置文件:"
  echo "  备份目录: $BACKUP_DIR"
  echo "  Docker 配置: /etc/docker/daemon.json"
  echo "  系统配置: /etc/sysctl.conf"
  echo ""

  echo "重要提示:"
  echo "  - 建议重启系统以确保所有配置生效"
  echo "  - SELinux 关闭将在重启后生效"
  echo "  - 配置文件备份在: $BACKUP_DIR"
  echo "  - 如需恢复配置，请使用备份文件"

  show_separator "=" 60
}

# 主函数
main() {
  # 解析命令行参数
  parse_arguments "$@"

  show_title "CentOS 7 系统初始化开始"
  log_info "Hello Boy! 开始系统初始化..."

  # 执行初始化步骤
  check_root
  check_architecture
  check_system_requirements

  # 如果不是强制模式，询问用户确认
  if [[ "$FORCE_MODE" != true ]]; then
    if ! confirm_operation "确定要开始系统初始化吗？此操作将修改系统配置" "y"; then
      log_info "用户取消初始化"
      exit 0
    fi
  fi

  create_backup_dir
  backup_configs
  disable_firewall
  optimize_ssh
  disable_selinux
  configure_dns
  update_yum_repos
  install_basic_packages
  install_docker_online
  configure_docker_registry
  install_docker_compose_online
  enable_ip_forward
  optimize_system
  cleanup_system

  log_success "CentOS 7 系统初始化完成！"
  show_post_init_info

  echo ""
  log_warning "建议现在重启系统以确保所有配置生效："
  log_info "执行命令: reboot"
}

# 执行主函数
main "$@"
