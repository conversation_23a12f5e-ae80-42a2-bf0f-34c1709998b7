server {
  listen        80;
  server_name   localhost;
  root          /usr/share/nginx/html;
  resolver      ${BACKEND_RESOLVER};

  #===============================================================================
  # 静态资源服务 - 前端应用入口
  #===============================================================================

  # 入口页面
  location / {
    index  index.html index.htm;
    try_files ${DOLLAR}uri ${DOLLAR}uri /index.html;
  }

  # 字体资源
  location /fonts/simsun.ttf {
    proxy_pass ${DOLLAR}upstream_backend_gis_static;
    proxy_set_header   Host             ${DOLLAR}proxy_host;
    proxy_set_header   X-Real-IP        ${DOLLAR}remote_addr;
    proxy_set_header   X-Forwarded-For  ${DOLLAR}proxy_add_x_forwarded_for;
  }

  #===============================================================================
  # 核心业务 API - 基础平台服务
  #===============================================================================

  # BladeX 基础平台 API
  set ${DOLLAR}upstream_backend "${BACKEND_ENDPOINT}";
  location /api {
    ${BACKEND_URL_REWRITE}
    proxy_pass ${DOLLAR}upstream_backend;
    proxy_set_header   Host             ${DOLLAR}proxy_host;
    proxy_set_header   X-Real-IP        ${DOLLAR}remote_addr;
    proxy_set_header   X-Forwarded-For  ${DOLLAR}proxy_add_x_forwarded_for;
  }

  # 数据接口服务 - Data Monitor
  set ${DOLLAR}upstream_backend_data_monitor "${BACKEND_DATA_MONITOR}";
  location /interface {
    client_max_body_size 10m;
    proxy_pass ${DOLLAR}upstream_backend_data_monitor;
    proxy_set_header   Host             ${DOLLAR}proxy_host;
    proxy_set_header   X-Real-IP        ${DOLLAR}remote_addr;
    proxy_set_header   X-Forwarded-For  ${DOLLAR}proxy_add_x_forwarded_for;
  }

  #===============================================================================
  # 业务模块 API - 各业务系统后端服务
  #===============================================================================

  # MES 业务前端系统
  set ${DOLLAR}upstream_backend_mes_biz "http://backend-mes-biz:9001";
  location /mes-biz {
    client_max_body_size 100m;
    proxy_pass ${DOLLAR}upstream_backend_mes_biz;
    proxy_set_header   Host             ${DOLLAR}proxy_host;
    proxy_set_header   X-Real-IP        ${DOLLAR}remote_addr;
    proxy_set_header   X-Forwarded-For  ${DOLLAR}proxy_add_x_forwarded_for;
  }

  # 移动应用后端服务
  set ${DOLLAR}upstream_backend_bd_app "http://backend-bd-app:9001";
  location /app-backend {
    client_max_body_size 10m;
    proxy_pass ${DOLLAR}upstream_backend_bd_app;
    proxy_set_header   Host             ${DOLLAR}proxy_host;
    proxy_set_header   X-Real-IP        ${DOLLAR}remote_addr;
    proxy_set_header   X-Forwarded-For  ${DOLLAR}proxy_add_x_forwarded_for;
  }

  # GIS 地理信息系统
  set ${DOLLAR}upstream_backend_gis_biz "http://backend-gis-biz:9001";
  location /gis-biz {
    client_max_body_size 10m;
    proxy_pass ${DOLLAR}upstream_backend_gis_biz;
    proxy_set_header   Host             ${DOLLAR}proxy_host;
    proxy_set_header   X-Real-IP        ${DOLLAR}remote_addr;
    proxy_set_header   X-Forwarded-For  ${DOLLAR}proxy_add_x_forwarded_for;
  }

  #===============================================================================
  # GraphQL 数据服务 - Hasura GraphQL Engine
  #===============================================================================

  set ${DOLLAR}upstream_hasura "http://graphql-engine:8080";

  # Hasura GraphQL API
  location /hasura {
    rewrite /hasura/(.*) /${DOLLAR}1 break;
    proxy_pass ${DOLLAR}upstream_hasura;
    proxy_http_version 1.1;
    proxy_set_header   Upgrade          ${DOLLAR}http_upgrade;
    proxy_set_header   Connection       "upgrade";
    proxy_set_header   Host             ${DOLLAR}proxy_host;
    proxy_set_header   X-Real-IP        ${DOLLAR}remote_addr;
    proxy_set_header   X-Forwarded-For  ${DOLLAR}proxy_add_x_forwarded_for;
  }

  # 拓扑预览中的 Hasura API
  location /preview-topo/hasura/ {
    rewrite /preview-topo/hasura/(.+) /${DOLLAR}1 break;
    proxy_pass ${DOLLAR}upstream_hasura;
    proxy_http_version 1.1;
    proxy_set_header   Upgrade          ${DOLLAR}http_upgrade;
    proxy_set_header   Connection       "upgrade";
    proxy_set_header   Host             ${DOLLAR}proxy_host;
    proxy_set_header   X-Real-IP        ${DOLLAR}remote_addr;
    proxy_set_header   X-Forwarded-For  ${DOLLAR}proxy_add_x_forwarded_for;
  }

  #===============================================================================
  # 前端模块服务 - 嵌入式前端组件
  #===============================================================================

  # 拓扑渲染器（嵌入式前端模块）
  set ${DOLLAR}upstream_frontend_topo_app "http://frontend-module-topo_embed";
  location /preview-topo {
     rewrite /preview-topo/(.*) /${DOLLAR}1 break;
     proxy_pass ${DOLLAR}upstream_frontend_topo_app;
     proxy_set_header   Host             ${DOLLAR}proxy_host;
     proxy_set_header   X-Real-IP        ${DOLLAR}remote_addr;
     proxy_set_header   X-Forwarded-For  ${DOLLAR}proxy_add_x_forwarded_for;
  }

  #===============================================================================
  # 视频流媒体服务 - 实时视频流
  #===============================================================================

  set ${DOLLAR}upstream_video_streaming_server "http://videojs-flow:8088";

  # 实时视频流 - WebSocket
  location /live {
    rewrite /live/(.*) /live/${DOLLAR}1 break;
    proxy_pass ${DOLLAR}upstream_video_streaming_server;
    proxy_http_version 1.1;
    proxy_set_header   Upgrade          ${DOLLAR}http_upgrade;
    proxy_set_header   Connection       "upgrade";
    proxy_set_header   Host             ${DOLLAR}proxy_host;
    proxy_set_header   X-Real-IP        ${DOLLAR}remote_addr;
    proxy_set_header   X-Forwarded-For  ${DOLLAR}proxy_add_x_forwarded_for;
  }

  # FLV 视频流
  location /flv {
    rewrite /flv/(.+) /${DOLLAR}1 break;
    proxy_pass ${DOLLAR}upstream_video_streaming_server;
    proxy_http_version 1.1;
    proxy_set_header   Upgrade          ${DOLLAR}http_upgrade;
    proxy_set_header   Connection       "upgrade";
    proxy_set_header   Host             ${DOLLAR}proxy_host;
    proxy_set_header   X-Real-IP        ${DOLLAR}remote_addr;
    proxy_set_header   X-Forwarded-For  ${DOLLAR}proxy_add_x_forwarded_for;
  }

  #===============================================================================
  # 外部 GIS 服务 - 第三方 GIS 系统集成
  #===============================================================================

  # GIS API 服务（洛阳）
  set ${DOLLAR}upstream_backend_gis_api "${UPSTREAM_GIS_API}";
  location /api/gis {
    rewrite /api/gis/(.+) /${DOLLAR}1 break;
    proxy_pass ${DOLLAR}upstream_backend_gis_api;
    proxy_set_header   Host             ${DOLLAR}proxy_host;
    proxy_set_header   X-Real-IP        ${DOLLAR}remote_addr;
    proxy_set_header   X-Forwarded-For  ${DOLLAR}proxy_add_x_forwarded_for;
  }

  # GIS 静态资源服务（洛阳）
  set ${DOLLAR}upstream_backend_gis_static "${UPSTREAM_GIS_STATIC}";

  # GIS 源文件资源
  location /gis/source {
    rewrite /gis/(.+) /${DOLLAR}1 break;
    proxy_pass ${DOLLAR}upstream_backend_gis_static;
    proxy_set_header   Host             ${DOLLAR}proxy_host;
    proxy_set_header   X-Real-IP        ${DOLLAR}remote_addr;
    proxy_set_header   X-Forwarded-For  ${DOLLAR}proxy_add_x_forwarded_for;
  }

  # MinIO 对象存储
  location /minio {
    proxy_pass ${DOLLAR}upstream_backend_gis_static;
    proxy_set_header   Host             ${DOLLAR}proxy_host;
    proxy_set_header   X-Real-IP        ${DOLLAR}remote_addr;
    proxy_set_header   X-Forwarded-For  ${DOLLAR}proxy_add_x_forwarded_for;
  }
}
