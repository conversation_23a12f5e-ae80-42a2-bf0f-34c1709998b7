#!/bin/bash

#===============================================================================
# Docker 安装脚本
#===============================================================================
# 功能描述: 自动安装 Docker CE 和 Docker Compose
# 适用系统: CentOS 7.x
# 执行权限: 需要 root 权限
#===============================================================================

# Docker 安装
echo "============================ docker安装开始 ============================"

# 安装 Docker
which docker
if [ $? -ne 0 ]; then
  # 移除既有版本
  yum remove -y docker docker-common docker-selinux docker-engine

  # 安装依赖
  yum install -y yum-utils device-mapper-persistent-data lvm2
  yum-config-manager --add-repo https://mirrors.aliyun.com/docker-ce/linux/centos/docker-ce.repo
  yum makecache fast

  # 安装 Docker
  yum install -y docker-ce
  
  # 配置镜像加速
  cat > /etc/docker/daemon.json << 'EOF'
{
  "log-driver": "json-file",
  "data-root": "/data/docker",
  "log-opts": { "max-size": "50m", "max-file": "3" },
  "registry-mirrors": [
    "https://5gmkcj2a.mirror.aliyuncs.com",
    "https://docker.m.daocloud.io"
  ]
}
EOF

  # 重启 Docker
  systemctl daemon-reload
  systemctl enable docker --now
  # systemctl start docker
fi

# 安装 Docker Compose
which docker-compose
if [ $? -ne 0 ]; then
  yum install -y python3-pip
  pip3 install docker-compose
fi

echo "============================ docker安装完成 ============================"
exit 0
