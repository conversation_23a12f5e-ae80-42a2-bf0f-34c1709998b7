# 系统初始化脚本目录

本目录包含微服务部署运行前提相关的脚本，用于准备系统环境和安装必要的软件。所有脚本都经过重构优化，使用统一的共通函数库，提供更好的用户体验和错误处理。

## 📁 目录内容

| 脚本名称 | 代码行数 | 主要功能 | 状态 |
|---------|---------|----------|------|
| docker-common.sh | 384 | 共通函数库 | ✅ 完成 |
| centos7-init.sh | 434 | 系统初始化 | ✅ 优化完成 |
| docker-install-offline.sh | 244 | 离线安装 | ✅ 完成 |
| docker-uninstall-offline.sh | 310 | 离线卸载 | ✅ 完成 |
| test-docker-scripts.sh | 254 | 测试验证 | ✅ 完成 |

### 🔧 docker-common.sh

**共通函数库**

**功能**：

- 统一的日志输出函数
- 系统检查和验证函数
- 文件和目录操作函数
- 服务管理函数
- Docker 状态检查函数
- 命令行参数解析
- 错误处理和状态管理

**说明**：

- 被其他脚本引用，不需要单独执行
- 提供384个函数，统一脚本行为
- 支持静默模式和强制模式

### 🚀 centos7-init.sh

**CentOS 7 系统初始化脚本**

**功能**：

- 🔒 关闭防火墙和 SELinux
- 🌐 SSH 连接优化（禁用 DNS 查找、GSSAPI）
- 📦 更新 YUM 源为阿里云镜像
- 🛠️ 安装扩展的基础软件包
- 🐳 在线安装 Docker CE 和 Docker Compose
- 🔄 开启 IP 路由转发
- ⚡ 配置 Docker 镜像加速和性能优化
- 💾 自动备份重要配置文件
- 🧹 系统清理和优化
- 📊 详细的安装后状态报告

**使用方法**：

```bash
# 基本使用
sudo ./centos7-init.sh

# 强制模式（跳过确认）
sudo ./centos7-init.sh --force

# 静默模式
sudo ./centos7-init.sh --quiet

# 查看帮助
./centos7-init.sh --help
```

**新增特性**：

- 配置文件自动备份到 `/root/centos7-init-backup`
- 智能检测已安装软件，避免重复安装
- 网络参数优化和性能调优
- 详细的进度显示和状态反馈

### 🐳 docker-install.sh

**Docker 环境安装脚本（原版在线安装）**

**功能**：

- 卸载旧版本 Docker
- 安装 Docker CE
- 配置阿里云镜像加速
- 启动 Docker 服务

**使用方法**：

```bash
sudo ./docker-install.sh
```

### 📦 docker-install-offline.sh

**Docker 离线安装脚本（新增）**

**功能**：

- 🔍 检查必需文件完整性
- 📂 解压并安装 Docker 二进制文件
- ⚙️ 配置 systemd 服务文件
- 🚀 启动 containerd 和 Docker 服务
- 🐙 安装 Docker Compose
- ✅ 完整的安装验证
- 📊 详细的安装后信息

**使用方法**：

```bash
# 基本使用
sudo ./docker-install-offline.sh

# 强制模式
sudo ./docker-install-offline.sh --force

# 查看帮助
./docker-install-offline.sh --help
```

**依赖文件**：
```
docker/
├── setups/x86_64/
│   ├── docker-*.tar          # Docker 二进制包
│   └── docker-compose-*      # Docker Compose 二进制文件
└── systemd/
    ├── docker.service        # Docker 服务文件
    ├── containerd.service    # containerd 服务文件
    └── docker.socket         # Docker socket 文件
```

### 🗑️ docker-uninstall-offline.sh

**Docker 离线卸载脚本（新增）**

**功能**：

- 🛑 停止所有容器和服务
- 🧹 删除所有镜像、容器和卷
- 📁 清理二进制文件和配置
- 🔧 删除 systemd 服务文件
- 🌐 清理网络配置和 iptables 规则
- 👥 删除 docker 用户组
- ✅ 完整的卸载验证

**使用方法**：

```bash
# 交互式卸载
sudo ./docker-uninstall-offline.sh

# 强制卸载（跳过确认）
sudo ./docker-uninstall-offline.sh --force

# 环境变量确认
DOCKER_UNINSTALL_CONFIRM=yes sudo ./docker-uninstall-offline.sh
```

**安全特性**：

- 多重确认机制防止误操作
- 详细的警告信息
- 完整的清理验证

### 🧪 test-docker-scripts.sh

**脚本测试工具（新增）**

**功能**：

- 🔍 测试共通函数库功能
- 📋 验证脚本文件完整性
- ✅ 检查语法正确性
- 📁 验证必需文件存在
- 🐳 Docker 状态检查
- 📊 生成测试报告

**使用方法**：

```bash
# 运行所有测试
./test-docker-scripts.sh --all

# 仅测试共通函数
./test-docker-scripts.sh --common

# 仅测试文件完整性
./test-docker-scripts.sh --files

# 仅测试语法
./test-docker-scripts.sh --syntax

# 查看帮助
./test-docker-scripts.sh --help
```

### 📄 其他文件

- **docker_install.md**: Docker 安装详细文档
- **docker-install-offline-README.md**: 离线安装详细说明
- **dos2unix-6.0.3-7.el7.x86_64.rpm**: 文本格式转换工具
- **docker/**: Docker 相关文件目录

## 🔄 推荐执行顺序

### 方案一：在线安装（推荐新手）

1. **系统初始化**：

   ```bash
   sudo ./centos7-init.sh
   ```

2. **重启系统**：

   ```bash
   reboot
   ```

3. **验证环境**：

   ```bash
   docker --version
   docker compose --version
   ```

### 方案二：离线安装（推荐生产环境）

1. **系统基础初始化**（跳过 Docker 安装）：

   ```bash
   # 需要手动修改脚本或使用参数控制
   sudo ./centos7-init.sh
   ```

2. **离线安装 Docker**：

   ```bash
   sudo ./docker-install-offline.sh
   ```

3. **验证安装**：

   ```bash
   docker --version
   docker compose --version
   ./test-docker-scripts.sh --docker
   ```

## 🛠️ 高级功能

### 命令行参数支持

所有主要脚本都支持以下参数：

- `--help`: 显示帮助信息
- `--version`: 显示版本信息
- `--quiet`: 静默模式
- `--force`: 强制模式（跳过确认）

### 配置备份

- 自动备份重要配置文件
- 备份位置：`/root/centos7-init-backup`
- 包含：SSH、SELinux、YUM 源、sysctl 等配置

### 日志系统

- 彩色日志输出：
  - 🔵 INFO: 一般信息
  - 🟢 SUCCESS: 成功操作
  - 🟡 WARNING: 警告信息
  - 🔴 ERROR: 错误信息

### 测试和验证

- 完整的测试套件
- 语法检查和文件验证
- 功能测试和状态检查
- 详细的测试报告

## 🔗 相关文档

- [Docker 官方文档](https://docs.docker.com/)
- [Docker Compose 文档](https://docs.docker.com/compose/)
- [CentOS 7 系统管理指南](https://docs.centos.org/)
- [离线安装详细说明](docker-install-offline-README.md)
- [Docker 安装文档](docker_install.md)
