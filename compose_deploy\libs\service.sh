#!/bin/bash

#===============================================================================
# 服务部署管理工具
#===============================================================================
# 功能描述: 提供各微服务模块的启动、停止、重启和删除功能
# 支持功能: 基础服务、业务服务、IOT服务等的生命周期管理
# 创建时间: 2024年
#
# 主要功能:
# - 各微服务模块的启动管理
# - 各微服务模块的停止管理
# - 各微服务模块的重启管理
# - 各微服务模块的删除管理
# - Hasura GraphQL 引擎状态检查
#===============================================================================

# 遇到错误立即退出
set -e

#===============================================================================
# Hasura 管理函数
#===============================================================================

#-------------------------------------------------------------------------------
# 函数名: check_hasura_up
# 功能: 检查 Hasura GraphQL 引擎是否启动完成
# 参数: 无
# 返回: 无（超时时直接退出程序）
# 说明: 等待 Hasura GraphQL 引擎启动，超时时间为120秒
#       通过健康检查端点验证服务可用性
#-------------------------------------------------------------------------------
check_hasura_up() {
    cd "$BASE_SERVICE_PATH" || exit
    echo "INFO: 正在等待 graphql-engine 启动..."

    local timeout=0
    while (( timeout < 120 )); do
        docker run --network="${DOCKER_NETWORK}" --rm -a stdout "${DOCKER_BUSY_BOX_IMAGE}" \
            sh -c "wget --quiet --tries=1 --spider http://graphql-engine:8080/healthz || exit 1"
        if [ $? -eq 0 ]; then
            echo ""
            echo "INFO: Hasura GraphQL 引擎启动成功"
            break
        fi
        printf "."
        timeout=$(( timeout + 5 ))
        sleep 5
    done

    if (( timeout >= 120 )); then
        echo_error "Hasura GraphQL 引擎启动超时"
        exit 1
    fi

    cd "$DEPLOY_PATH" || exit
}

#===============================================================================
# 服务上线函数
# docker compose up -d
#===============================================================================

up_ssh_service () {
  echo_yellow "上线 SSH 服务"
  docker_compose_up "$SSH_SERVICE_PATH"
  cd "$DEPLOY_PATH" || exit
}

up_base_service () {
  echo_yellow "上线基础服务"
  docker_compose_up "$BASE_SERVICE_PATH"
  cd "$DEPLOY_PATH" || exit
}

up_video_service () {
  echo_yellow "上线视频服务"
  docker_compose_up "$VIDEO_SERVICE_PATH"
  cd "$DEPLOY_PATH" || exit
}

up_mos_service () {
  echo_yellow "上线 MOS 服务"
  docker_compose_up "$MOS_SERVICE_PATH"
  cd "$DEPLOY_PATH" || exit
}

up_iot_service () {
  echo_yellow "上线 IOT 服务"
  docker_compose_up "$IOT_SERVICE_PATH"
  # if [ $DEPLOY_IOT_SERVICE = true ]; then
  #     check_hasura_up;
  # fi
  cd "$DEPLOY_PATH" || exit
}

up_mes_service () {
  echo_yellow "上线 MES 服务"
  docker_compose_up "$MES_SERVICE_PATH"
  cd "$DEPLOY_PATH" || exit
}

up_gis_service () {
  echo_yellow "上线 GIS 服务"
  docker_compose_up "$GIS_SERVICE_PATH"
  cd "$DEPLOY_PATH" || exit
}

up_app_service () {
  echo_yellow "上线 app 服务"
  docker_compose_up "$APP_SERVICE_PATH"
  cd "$DEPLOY_PATH" || exit
}

up_workflow_service () {
  echo_yellow "上线 业务审批流 服务"
  docker_compose_up "$WORK_FLOW_PATH"
  cd "$DEPLOY_PATH" || exit
}

# =============================================================================
# down
# docker compose down
# =============================================================================

down_base_service () {
  echo_yellow "停止基础服务"
  docker_compose_down "$BASE_SERVICE_PATH"
  cd "$DEPLOY_PATH" || exit
}

down_video_service () {
  echo_yellow "停止视频服务"
  docker_compose_down "$VIDEO_SERVICE_PATH"
  cd "$DEPLOY_PATH" || exit
}

down_mos_service () {
  echo_yellow "停止 MOS 服务"
  docker_compose_down "$MOS_SERVICE_PATH"
  cd "$DEPLOY_PATH" || exit
}

down_iot_service () {
  echo_yellow "停止 IOT 服务"
  docker_compose_down "$IOT_SERVICE_PATH"
  cd "$DEPLOY_PATH" || exit
}

down_mes_service () {
  echo_yellow "停止 MES 服务"
  docker_compose_down "$MES_SERVICE_PATH"
  cd "$DEPLOY_PATH" || exit
}

down_gis_service () {
  echo_yellow "停止 GIS 服务"
  docker_compose_down "$GIS_SERVICE_PATH"
  cd "$DEPLOY_PATH" || exit
}

down_app_service () {
  echo_yellow "停止 app 服务"
  docker_compose_down "$APP_SERVICE_PATH"
  cd "$DEPLOY_PATH" || exit
}

down_workflow_service () {
  echo_yellow "停止 业务审批流 服务"
  docker_compose_down "$WORK_FLOW_PATH"
  cd "$DEPLOY_PATH" || exit
}

#===============================================================================
# 服务启动函数
# docker compose start
#===============================================================================

start_ssh_service () {
  echo_yellow "启动 SSH 服务"
  docker_compose_start "$SSH_SERVICE_PATH"
  cd "$DEPLOY_PATH" || exit
}

start_base_service () {
  echo_yellow "启动基础服务"
  docker_compose_start "$BASE_SERVICE_PATH"
  cd "$DEPLOY_PATH" || exit
}

start_video_service () {
  echo_yellow "启动视频服务"
  docker_compose_start "$VIDEO_SERVICE_PATH"
  cd "$DEPLOY_PATH" || exit
}

start_mos_service () {
  echo_yellow "启动 MOS 服务"
  docker_compose_start "$MOS_SERVICE_PATH"
  cd "$DEPLOY_PATH" || exit
}

start_iot_service () {
  echo_yellow "启动 IOT 服务"
  docker_compose_start "$IOT_SERVICE_PATH"
  # if [ $DEPLOY_IOT_SERVICE = true ]; then
  #     check_hasura_up;
  # fi
  cd "$DEPLOY_PATH" || exit
}

start_mes_service () {
  echo_yellow "启动 MES 服务"
  docker_compose_start "$MES_SERVICE_PATH"
  cd "$DEPLOY_PATH" || exit
}

start_gis_service () {
  echo_yellow "启动 GIS 服务"
  docker_compose_start "$GIS_SERVICE_PATH"
  cd "$DEPLOY_PATH" || exit
}

start_app_service () {
  echo_yellow "启动 app 服务"
  docker_compose_start "$APP_SERVICE_PATH"
  cd "$DEPLOY_PATH" || exit
}

start_workflow_service () {
  echo_yellow "启动 业务审批流 服务"
  docker_compose_start "$WORK_FLOW_PATH"
  cd "$DEPLOY_PATH" || exit
}

# =============================================================================
# stop
# docker compose stop
# =============================================================================

stop_base_service () {
  echo_yellow "停止基础服务"
  docker_compose_stop "$BASE_SERVICE_PATH"
  cd "$DEPLOY_PATH" || exit
}

stop_video_service () {
  echo_yellow "停止视频服务"
  docker_compose_stop "$VIDEO_SERVICE_PATH"
  cd "$DEPLOY_PATH" || exit
}

stop_mos_service () {
  echo_yellow "停止 MOS 服务"
  docker_compose_stop "$MOS_SERVICE_PATH"
  cd "$DEPLOY_PATH" || exit
}

stop_iot_service () {
  echo_yellow "停止 IOT 服务"
  docker_compose_stop "$IOT_SERVICE_PATH"
  cd "$DEPLOY_PATH" || exit
}

stop_mes_service () {
  echo_yellow "停止 MES 服务"
  docker_compose_stop "$MES_SERVICE_PATH"
  cd "$DEPLOY_PATH" || exit
}

stop_gis_service () {
  echo_yellow "停止 GIS 服务"
  docker_compose_stop "$GIS_SERVICE_PATH"
  cd "$DEPLOY_PATH" || exit
}

stop_app_service () {
  echo_yellow "停止 app 服务"
  docker_compose_stop "$APP_SERVICE_PATH"
  cd "$DEPLOY_PATH" || exit
}

stop_workflow_service () {
  echo_yellow "停止 业务审批流 服务"
  docker_compose_stop "$WORK_FLOW_PATH"
  cd "$DEPLOY_PATH" || exit
}

stop_ssh_service () {
  echo_yellow "停止 SSH 服务"
  docker_compose_down "$SSH_SERVICE_PATH"
  cd "$DEPLOY_PATH" || exit
}

# =============================================================================
# restart
# docker compose restart
# =============================================================================

restart_base_service () {
  echo_yellow "启动基础服务"
  docker_compose_restart "$BASE_SERVICE_PATH"
  cd "$DEPLOY_PATH" || exit
}


restart_video_service () {
  echo_yellow "启动视频服务"
  docker_compose_restart "$VIDEO_SERVICE_PATH"
  cd "$DEPLOY_PATH" || exit
}

restart_mos_service () {
  echo_yellow "启动 MOS 服务"
  docker_compose_restart "$MOS_SERVICE_PATH"
  cd "$DEPLOY_PATH" || exit
}

restart_iot_service () {
  echo_yellow "启动 IOT 服务"
  docker_compose_restart "$IOT_SERVICE_PATH"
  # if [ $DEPLOY_IOT_SERVICE = true ]; then
  #     check_hasura_up;
  # fi
  cd "$DEPLOY_PATH" || exit
}

restart_mes_service () {
  echo_yellow "启动 MES 服务"
  docker_compose_restart "$MES_SERVICE_PATH"
  cd "$DEPLOY_PATH" || exit
}

restart_gis_service () {
  echo_yellow "启动 GIS 服务"
  docker_compose_restart "$GIS_SERVICE_PATH"
  cd "$DEPLOY_PATH" || exit
}

restart_app_service () {
  echo_yellow "启动 app 服务"
  docker_compose_restart "$APP_SERVICE_PATH"
  cd "$DEPLOY_PATH" || exit
}

restart_workflow_service () {
  echo_yellow "启动 业务审批流 服务"
  docker_compose_restart "$WORK_FLOW_PATH"
  cd "$DEPLOY_PATH" || exit
}
