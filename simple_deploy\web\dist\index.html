<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Deploy - 简单部署管理器</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <style>
        body {
            margin: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        .header {
            background: #409EFF;
            color: white;
            padding: 0 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .main-container {
            padding: 20px;
        }
        .stats-card {
            margin-bottom: 20px;
        }
        .service-card {
            margin-bottom: 16px;
        }
        .status-running {
            color: #67C23A;
        }
        .status-stopped {
            color: #909399;
        }
        .status-error {
            color: #F56C6C;
        }
        .group-section {
            margin-bottom: 30px;
        }
    </style>
</head>
<body>
    <div id="app">
        <el-container>
            <!-- 头部 -->
            <el-header class="header">
                <el-row align="middle" justify="space-between">
                    <el-col :span="12">
                        <h2 style="margin: 0;">Simple Deploy</h2>
                    </el-col>
                    <el-col :span="12" style="text-align: right;">
                        <el-button @click="refreshData" :loading="loading">
                            <el-icon><refresh /></el-icon>
                            刷新
                        </el-button>
                    </el-col>
                </el-row>
            </el-header>

            <!-- 主内容 -->
            <el-main class="main-container">
                <!-- 统计卡片 -->
                <el-row :gutter="20" class="stats-card">
                    <el-col :span="6">
                        <el-card>
                            <div style="text-align: center;">
                                <div style="font-size: 24px; font-weight: bold; color: #409EFF;">
                                    {{ stats.total }}
                                </div>
                                <div>总服务数</div>
                            </div>
                        </el-card>
                    </el-col>
                    <el-col :span="6">
                        <el-card>
                            <div style="text-align: center;">
                                <div style="font-size: 24px; font-weight: bold; color: #67C23A;">
                                    {{ stats.running }}
                                </div>
                                <div>运行中</div>
                            </div>
                        </el-card>
                    </el-col>
                    <el-col :span="6">
                        <el-card>
                            <div style="text-align: center;">
                                <div style="font-size: 24px; font-weight: bold; color: #909399;">
                                    {{ stats.stopped }}
                                </div>
                                <div>已停止</div>
                            </div>
                        </el-card>
                    </el-col>
                    <el-col :span="6">
                        <el-card>
                            <div style="text-align: center;">
                                <div style="font-size: 24px; font-weight: bold; color: #F56C6C;">
                                    {{ stats.error }}
                                </div>
                                <div>异常</div>
                            </div>
                        </el-card>
                    </el-col>
                </el-row>

                <!-- 服务分组 -->
                <div v-for="group in serviceGroups" :key="group.name" class="group-section">
                    <el-card>
                        <template #header>
                            <el-row align="middle" justify="space-between">
                                <el-col :span="12">
                                    <h3 style="margin: 0;">{{ group.displayName }}</h3>
                                    <p style="margin: 5px 0 0 0; color: #909399;">{{ group.description }}</p>
                                </el-col>
                                <el-col :span="12" style="text-align: right;">
                                    <el-button-group>
                                        <el-button
                                            type="success"
                                            size="small"
                                            @click="startGroup(group.name)"
                                            :loading="groupLoading[group.name]">
                                            启动组
                                        </el-button>
                                        <el-button
                                            type="warning"
                                            size="small"
                                            @click="stopGroup(group.name)"
                                            :loading="groupLoading[group.name]">
                                            停止组
                                        </el-button>
                                    </el-button-group>
                                </el-col>
                            </el-row>
                        </template>

                        <el-row :gutter="16">
                            <el-col :span="8" v-for="service in group.services" :key="service.id">
                                <el-card class="service-card" shadow="hover">
                                    <el-row align="middle" justify="space-between">
                                        <el-col :span="16">
                                            <div style="font-weight: bold;">{{ service.display_name || service.name }}</div>
                                            <div style="font-size: 12px; color: #909399;">{{ service.image }}:{{ service.version }}</div>
                                            <div style="margin-top: 5px;">
                                                <el-tag
                                                    :type="getStatusType(service.status)"
                                                    size="small">
                                                    {{ getStatusText(service.status) }}
                                                </el-tag>
                                            </div>
                                        </el-col>
                                        <el-col :span="8" style="text-align: right;">
                                            <el-button-group size="small">
                                                <el-button
                                                    type="success"
                                                    :disabled="service.status === 'running'"
                                                    @click="startService(service.id)"
                                                    :loading="serviceLoading[service.id]">
                                                    启动
                                                </el-button>
                                                <el-button
                                                    type="warning"
                                                    :disabled="service.status === 'stopped'"
                                                    @click="stopService(service.id)"
                                                    :loading="serviceLoading[service.id]">
                                                    停止
                                                </el-button>
                                            </el-button-group>
                                        </el-col>
                                    </el-row>
                                </el-card>
                            </el-col>
                        </el-row>
                    </el-card>
                </div>
            </el-main>
        </el-container>
    </div>

    <script>
        const { createApp } = Vue;
        const { ElMessage } = ElementPlus;

        createApp({
            data() {
                return {
                    loading: false,
                    serviceLoading: {},
                    groupLoading: {},
                    stats: {
                        total: 0,
                        running: 0,
                        stopped: 0,
                        error: 0
                    },
                    services: [],
                    serviceGroups: [
                        { name: 'base', displayName: '基础服务', description: '数据库、缓存、消息队列等基础服务', services: [] },
                        { name: 'gis', displayName: 'GIS服务', description: '地理信息系统服务', services: [] },
                        { name: 'video', displayName: '视频服务', description: '视频监控和流媒体服务', services: [] },
                        { name: 'mos', displayName: 'MOS中台', description: '用户权限和前端服务', services: [] },
                        { name: 'iot', displayName: 'IoT平台', description: '数据接入和监控服务', services: [] },
                        { name: 'mes', displayName: 'MES系统', description: '业务前端系统', services: [] },
                        { name: 'workflow', displayName: '工作流', description: '业务流程管理', services: [] }
                    ]
                }
            },
            mounted() {
                this.loadDashboard();
            },
            methods: {
                async loadDashboard() {
                    this.loading = true;
                    try {
                        const response = await axios.get('/api/v1/dashboard');
                        this.stats = response.data.stats;
                        this.services = response.data.services;
                        this.groupServices();
                    } catch (error) {
                        ElMessage.error('加载数据失败: ' + error.message);
                    } finally {
                        this.loading = false;
                    }
                },
                groupServices() {
                    // 按分组整理服务
                    this.serviceGroups.forEach(group => {
                        group.services = this.services.filter(service =>
                            service.category === group.name
                        );
                    });
                },
                async startService(serviceId) {
                    this.$set(this.serviceLoading, serviceId, true);
                    try {
                        await axios.post(`/api/v1/services/${serviceId}/start`);
                        ElMessage.success('服务启动成功');
                        this.loadDashboard();
                    } catch (error) {
                        ElMessage.error('启动服务失败: ' + error.response?.data?.error || error.message);
                    } finally {
                        this.$set(this.serviceLoading, serviceId, false);
                    }
                },
                async stopService(serviceId) {
                    this.$set(this.serviceLoading, serviceId, true);
                    try {
                        await axios.post(`/api/v1/services/${serviceId}/stop`);
                        ElMessage.success('服务停止成功');
                        this.loadDashboard();
                    } catch (error) {
                        ElMessage.error('停止服务失败: ' + error.response?.data?.error || error.message);
                    } finally {
                        this.$set(this.serviceLoading, serviceId, false);
                    }
                },
                async startGroup(groupName) {
                    this.$set(this.groupLoading, groupName, true);
                    try {
                        await axios.post(`/api/v1/groups/${groupName}/start`);
                        ElMessage.success('服务组启动成功');
                        this.loadDashboard();
                    } catch (error) {
                        ElMessage.error('启动服务组失败: ' + error.response?.data?.error || error.message);
                    } finally {
                        this.$set(this.groupLoading, groupName, false);
                    }
                },
                async stopGroup(groupName) {
                    this.$set(this.groupLoading, groupName, true);
                    try {
                        await axios.post(`/api/v1/groups/${groupName}/stop`);
                        ElMessage.success('服务组停止成功');
                        this.loadDashboard();
                    } catch (error) {
                        ElMessage.error('停止服务组失败: ' + error.response?.data?.error || error.message);
                    } finally {
                        this.$set(this.groupLoading, groupName, false);
                    }
                },
                refreshData() {
                    this.loadDashboard();
                },
                getStatusType(status) {
                    switch (status) {
                        case 'running': return 'success';
                        case 'stopped': return 'info';
                        default: return 'danger';
                    }
                },
                getStatusText(status) {
                    switch (status) {
                        case 'running': return '运行中';
                        case 'stopped': return '已停止';
                        case 'starting': return '启动中';
                        case 'stopping': return '停止中';
                        default: return '异常';
                    }
                }
            }
        }).use(ElementPlus).mount('#app');
    </script>
</body>
</html>
