#appendonly yes #AOF持久化，是否记录更新操作日志，默认redis是异步（快照）把数据写入本地磁盘
#appendfilename appendonly.aof #指定更新日志文件名
#appendfsync always   ##AOF持久化三种同步策略：每次有数据发生变化时都会写入appendonly.aof
# appendfsync everysec  #默认方式，每秒同步一次到appendonly.aof
#appendfsync yes       #不同步，数据不会持久化
appendonly yes
appendfsync always
# 持久化混合模式
aof-use-rdb-preamble yes
requirepass ${REDIS_PASSWORD}
#save 900 1
#save 300 1
#每30 监测至少一条数据改变 就同步到磁盘
save 30 1
#maxclients 10

