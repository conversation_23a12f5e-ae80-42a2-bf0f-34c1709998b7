@echo off
setlocal enabledelayedexpansion

REM Simple Deploy 快速启动脚本 (Windows版本)
REM 用于快速构建、导入配置并启动服务

echo 🚀 Simple Deploy 快速启动脚本
echo ================================

REM 检查Go环境
where go >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ 错误: 未找到Go环境，请先安装Go
    pause
    exit /b 1
)

REM 检查Docker环境
where docker >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ 错误: 未找到Docker环境，请先安装Docker
    pause
    exit /b 1
)

REM 检查Docker是否运行
docker info >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ 错误: Docker守护进程未运行，请启动Docker
    pause
    exit /b 1
)

echo ✅ 环境检查通过

REM 构建程序
echo.
echo 📦 构建Simple Deploy...
go mod tidy
go build -o build\simple-deploy.exe cmd\main.go

if %errorlevel% neq 0 (
    echo ❌ 构建失败
    pause
    exit /b 1
)

echo ✅ 构建完成

REM 检查是否存在compose-deploy目录
if exist "..\compose-deploy" (
    echo.
    echo 📥 导入现有配置...
    build\simple-deploy.exe import ..\compose-deploy
    
    if %errorlevel% equ 0 (
        echo ✅ 配置导入完成
    ) else (
        echo ⚠️  配置导入失败，但可以继续启动服务
    )
) else (
    echo ⚠️  未找到compose-deploy目录，跳过配置导入
)

REM 启动服务
echo.
echo 🌐 启动Web服务...
echo 访问地址: http://localhost:8080
echo 按 Ctrl+C 停止服务
echo.

build\simple-deploy.exe serve

pause
