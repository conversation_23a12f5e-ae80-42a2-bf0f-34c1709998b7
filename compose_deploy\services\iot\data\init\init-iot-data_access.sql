-- 数据接入和派生点服务初始化数据 --

INSERT INTO "public"."data_type"("id", "name", "type", "discription", "object", "created_at", "updated_at", "order_number") VALUES ('AP9GbyKY', '下限报警', 'warn', NULL, NULL, '2019-12-11 05:31:02.143112+00', '2019-12-11 05:31:02.143112+00', NULL)
  ON conflict(id) do  update set name=excluded.name,type=excluded.type,discription=excluded.discription,object=excluded.object;

INSERT INTO "public"."data_type"("id", "name", "type", "discription", "object", "created_at", "updated_at", "order_number") VALUES ('rpOH1UrP', '下限预警', 'warn', NULL, NULL, '2019-12-11 05:31:07.101225+00', '2019-12-11 05:31:07.101225+00', NULL)
  ON conflict(id) do  update set name=excluded.name,type=excluded.type,discription=excluded.discription,object=excluded.object;

INSERT INTO "public"."data_type"("id", "name", "type", "discription", "object", "created_at", "updated_at", "order_number") VALUES ('P2i8ZcyQ', '上限预警', 'warn', NULL, NULL, '2019-12-11 05:31:10.942495+00', '2019-12-11 05:31:10.942495+00', NULL)
  ON conflict(id) do  update set name=excluded.name,type=excluded.type,discription=excluded.discription,object=excluded.object;

INSERT INTO "public"."data_type"("id", "name", "type", "discription", "object", "created_at", "updated_at", "order_number") VALUES ('AI6nizhk', '温度报警', 'warn', NULL, NULL, '2020-03-06 06:46:27.47664+00', '2020-03-06 06:46:27.47664+00', NULL)
  ON conflict(id) do  update set name=excluded.name,type=excluded.type,discription=excluded.discription,object=excluded.object;


INSERT INTO "public"."data_type"("id", "name", "type", "discription", "object", "created_at", "updated_at", "order_number") VALUES ('s5kWlbb8', '上限报警', 'warn', NULL, NULL, '2019-12-11 05:30:54.433179+00', '2019-12-20 05:36:02.21492+00', NULL) 
  ON conflict(id) do  update set name=excluded.name,type=excluded.type,discription=excluded.discription,object=excluded.object;

INSERT INTO "public"."data_type"("id", "name", "type", "discription", "object", "created_at", "updated_at", "order_number") VALUES ('K3GgqFyx', '皮带保护报警', 'warn', NULL, NULL, '2020-08-20 05:06:42.05697+00', '2020-08-20 05:06:42.05697+00', NULL)
  ON conflict(id) do  update set name=excluded.name,type=excluded.type,discription=excluded.discription,object=excluded.object;

INSERT INTO "public"."data_type"("id", "name", "type", "discription", "object", "created_at", "updated_at", "order_number") VALUES ('7zcnB2F2', '电压报警', 'warn', NULL, NULL, '2020-03-29 07:31:33.253529+00', '2020-03-29 07:31:33.253529+00', NULL)
  ON conflict(id) do  update set name=excluded.name,type=excluded.type,discription=excluded.discription,object=excluded.object;

INSERT INTO "public"."data_type"("id", "name", "type", "discription", "object", "created_at", "updated_at", "order_number") VALUES ('Ut7nTV8q', '电流报警', 'warn', NULL, NULL, '2020-03-29 07:31:42.878101+00', '2020-03-29 07:31:42.878101+00', NULL)
  ON conflict(id) do  update set name=excluded.name,type=excluded.type,discription=excluded.discription,object=excluded.object;

INSERT INTO "public"."data_type"("id", "name", "type", "discription", "object", "created_at", "updated_at", "order_number") VALUES ('SQPRE6fb', '震动报警', 'warn', NULL, NULL, '2020-03-29 07:39:13.360076+00', '2020-03-29 07:39:13.360076+00', NULL)
  ON conflict(id) do  update set name=excluded.name,type=excluded.type,discription=excluded.discription,object=excluded.object;

INSERT INTO "public"."data_type"("id", "name", "type", "discription", "object", "created_at", "updated_at", "order_number") VALUES ('D4be28Kt', '故障报警', 'warn', NULL, NULL, '2020-03-29 07:51:50.267905+00', '2020-03-29 07:51:50.267905+00', NULL)
  ON conflict(id) do  update set name=excluded.name,type=excluded.type,discription=excluded.discription,object=excluded.object;

INSERT INTO "public"."data_type"("id", "name", "type", "discription", "object", "created_at", "updated_at", "order_number") VALUES ('rESsuiY9', '压力报警', 'warn', NULL, NULL, '2020-03-29 08:16:11.3291+00', '2020-03-29 08:16:11.3291+00', NULL)
  ON conflict(id) do  update set name=excluded.name,type=excluded.type,discription=excluded.discription,object=excluded.object;

INSERT INTO "public"."data_type"("id", "name", "type", "discription", "object", "created_at", "updated_at", "order_number") VALUES ('9NZq29Js', '风量报警', 'warn', NULL, NULL, '2020-04-03 12:48:55.236438+00', '2020-04-03 12:48:55.236438+00', NULL)
  ON conflict(id) do  update set name=excluded.name,type=excluded.type,discription=excluded.discription,object=excluded.object;

INSERT INTO "public"."data_type"("id", "name", "type", "discription", "object", "created_at", "updated_at", "order_number") VALUES ('AB8AQWRh', '速度报警', 'warn', NULL, NULL, '2020-04-04 00:21:08.258538+00', '2020-04-04 00:21:08.258538+00', NULL)
  ON conflict(id) do  update set name=excluded.name,type=excluded.type,discription=excluded.discription,object=excluded.object;

INSERT INTO "public"."data_type"("id", "name", "type", "discription", "object", "created_at", "updated_at", "order_number") VALUES ('6HQjonW4', '开关报警', 'warn', NULL, NULL, '2020-04-04 01:10:18.114521+00', '2020-04-04 01:10:18.114521+00', NULL)
  ON conflict(id) do  update set name=excluded.name,type=excluded.type,discription=excluded.discription,object=excluded.object;

INSERT INTO "public"."data_type"("id", "name", "type", "discription", "object", "created_at", "updated_at", "order_number") VALUES ('thBAQpMh', '位置报警', 'warn', NULL, NULL, '2020-04-04 01:14:39.837018+00', '2020-04-04 01:14:39.837018+00', NULL)
  ON conflict(id) do  update set name=excluded.name,type=excluded.type,discription=excluded.discription,object=excluded.object;

INSERT INTO "public"."data_type"("id", "name", "type", "discription", "object", "created_at", "updated_at", "order_number") VALUES ('ZnvfPEQB', '功率因数报警', 'warn', NULL, NULL, '2020-04-11 10:36:11.374574+00', '2020-04-11 10:36:11.374574+00', NULL)
  ON conflict(id) do  update set name=excluded.name,type=excluded.type,discription=excluded.discription,object=excluded.object;

INSERT INTO "public"."data_type"("id", "name", "type", "discription", "object", "created_at", "updated_at", "order_number") VALUES ('p9ejYahw', '流量报警', 'warn', NULL, NULL, '2020-04-13 08:31:56.209791+00', '2020-04-13 08:31:56.209791+00', NULL)
  ON conflict(id) do  update set name=excluded.name,type=excluded.type,discription=excluded.discription,object=excluded.object;

INSERT INTO "public"."data_type"("id", "name", "type", "discription", "object", "created_at", "updated_at", "order_number") VALUES ('omMNjF4D', '自动运行预警', 'warn', NULL, NULL, '2020-08-23 08:09:31.577831+00', '2020-08-23 08:09:31.577831+00', NULL)
  ON conflict(id) do  update set name=excluded.name,type=excluded.type,discription=excluded.discription,object=excluded.object;


INSERT INTO "public"."model_system"("id", "name", "created_at", "updated_at", "conf_table", "update_table", "history_table", "conf_table_struct", "category", "flag", "definition_routing_key", "update_routing_key", "mine_id", "sid", "route_service", "warn_service", "converter_service", "exchange") VALUES ('999999', '派生点', '2019-09-01 02:19:25.439465+00', '2020-09-16 05:41:17.648936+00', 'data_derive_definition', 'data_derive_realtime', 'data_derive_realtime', '     {"name": {"label": "名称","type": "String","isUpdate":"true"},
        "point_id": {"label": "点Id","type": "String","isUpdate":"false"},
        "system_id": {"label": "系统Id","type": "String","isUpdate":"false"},
        "data_type": {"label": "值类型","type": "String","isUpdate":"true"},
        "point_type": {"label": "点类型","type": "Int","isUpdate":"true"},
        "company_code": {"label": "矿名称","type": "String","isUpdate":"true"},
        "group_code": {"label": "集团名称","type": "String","isUpdate":"true"},
        "unit": {"label": "单位","type": "String","isUpdate":"true"},
        "type": {"label": "类型","type": "String","isUpdate":"true"},
        "parent_system_id": {"label": "父系统","type": "String","isUpdate":"true"}
}
', NULL, '1', '', '', NULL, 999, 0, NULL, NULL, 'ex_derive')
 ON conflict(id) do  update set name=excluded.name,
created_at=excluded.created_at,
updated_at=excluded.updated_at,
conf_table=excluded.conf_table,
update_table=excluded.update_table,
history_table=excluded.history_table,
conf_table_struct=excluded.conf_table_struct,
category=excluded.category,
flag=excluded.flag,
definition_routing_key=excluded.definition_routing_key,
update_routing_key=excluded.update_routing_key,
sid=excluded.sid,
route_service=excluded.route_service,
warn_service=excluded.warn_service,
converter_service=excluded.converter_service,
exchange=excluded.exchange
