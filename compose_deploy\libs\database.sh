#!/bin/bash

#===============================================================================
# 数据库备份工具
#===============================================================================
# 功能描述: 提供PostgreSQL数据库备份和恢复功能
# 支持数据库: PostgreSQL (GIS数据库、MES业务数据库)
# 创建时间: 2024年
#
# 主要功能:
# - PostgreSQL 数据库备份
# - 备份文件压缩和存储
# - 备份目录管理
# - 错误处理和日志记录
#===============================================================================

# 遇到错误立即退出
set -e

#===============================================================================
# 数据库备份函数
#===============================================================================

#-------------------------------------------------------------------------------
# 函数名: backup_postgres
# 功能: 备份PostgreSQL数据库（包括GIS和MES数据库）
# 参数: 无
# 返回: 无（备份失败时直接退出程序）
# 说明: 备份GIS主数据库和MES业务数据库到指定目录
#       使用pg_dump进行逻辑备份，并压缩存储
#       备份文件按时间戳命名，便于管理和恢复
#-------------------------------------------------------------------------------
backup_postgres() {
  echo_yellow "开始备份PostgreSQL数据库..."

  # 检查必要的环境变量
  if [ -z "$BASE_SERVICE_PATH" ]; then
    echo "ERROR: BASE_SERVICE_PATH环境变量未设置" >&2
    exit 1
  fi

  if [ -z "$BACKUP_BASE_PATH" ]; then
    echo "ERROR: BACKUP_BASE_PATH环境变量未设置" >&2
    exit 1
  fi

  # 加载基础服务环境变量
  local env_file="$BASE_SERVICE_PATH/.env"
  if [ ! -f "$env_file" ]; then
    echo "ERROR: 环境变量文件不存在: $env_file" >&2
    exit 1
  fi

  echo "INFO: 加载环境变量文件: $env_file"
  source "$env_file"

  # 创建备份目录
  local backup_date
  backup_date=$(date "+%Y-%m-%d")
  local backup_time
  backup_time=$(date "+%H.%M.%S")

  BACKUP_DB_PATH="${BACKUP_BASE_PATH}/${backup_date}/db"

  echo "INFO: 创建备份目录: $BACKUP_DB_PATH"
  mkdir -p "$BACKUP_DB_PATH"

  # 检查必要的数据库环境变量
  _check_postgres_env_vars

  # 备份GIS主数据库
  _backup_gis_database "$backup_time"

  # 备份MES业务数据库
  _backup_mes_database "$backup_time"

  echo_yellow "PostgreSQL数据库备份完成！"
  echo "INFO: 备份文件保存在: $BACKUP_DB_PATH"
}

#===============================================================================
# 内部辅助函数
#===============================================================================

#-------------------------------------------------------------------------------
# 函数名: _check_postgres_env_vars
# 功能: 检查PostgreSQL备份所需的环境变量
# 参数: 无
# 返回: 无（环境变量缺失时直接退出程序）
# 说明: 验证数据库连接所需的用户名和数据库名环境变量
#-------------------------------------------------------------------------------
_check_postgres_env_vars() {
  echo "INFO: 检查数据库环境变量..."

  # 检查GIS数据库环境变量
  if [ -z "$POSTGRES_USERNAME" ]; then
    echo "ERROR: POSTGRES_USERNAME环境变量未设置" >&2
    exit 1
  fi

  if [ -z "$POSTGRES_DATABASE" ]; then
    echo "ERROR: POSTGRES_DATABASE环境变量未设置" >&2
    exit 1
  fi

  # 检查MES数据库环境变量
  if [ -z "$BLADEX_POSTGRES_USERNAME" ]; then
    echo "ERROR: BLADEX_POSTGRES_USERNAME环境变量未设置" >&2
    exit 1
  fi

  if [ -z "$BLADEX_POSTGRES_DATABASE" ]; then
    echo "ERROR: BLADEX_POSTGRES_DATABASE环境变量未设置" >&2
    exit 1
  fi

  echo "INFO: 数据库环境变量检查通过"
  echo "INFO: GIS数据库 - 用户: $POSTGRES_USERNAME, 数据库: $POSTGRES_DATABASE"
  echo "INFO: MES数据库 - 用户: $BLADEX_POSTGRES_USERNAME, 数据库: $BLADEX_POSTGRES_DATABASE"
}

#-------------------------------------------------------------------------------
# 函数名: _backup_gis_database
# 功能: 备份GIS主数据库
# 参数:
#   $1: 备份时间戳
# 返回: 无（备份失败时直接退出程序）
# 说明: 使用pg_dump备份GIS数据库，压缩后保存到备份目录
#-------------------------------------------------------------------------------
_backup_gis_database() {
  local backup_time="$1"
  local container_name="base_postgres"
  local backup_filename="pg.${backup_time}.tgz"

  echo_yellow "备份GIS主数据库..."
  echo "INFO: 容器: $container_name"
  echo "INFO: 数据库: $POSTGRES_DATABASE"
  echo "INFO: 用户: $POSTGRES_USERNAME"

  # 检查容器是否运行
  if ! docker ps --format "{{.Names}}" | grep -q "^${container_name}$"; then
    echo "ERROR: PostgreSQL容器 '$container_name' 未运行" >&2
    exit 1
  fi

  # 执行数据库备份
  echo "INFO: 正在执行pg_dump备份..."
  if ! docker exec "$container_name" /bin/bash -c "pg_dump -U ${POSTGRES_USERNAME} ${POSTGRES_DATABASE} > /tmp/gis.sql"; then
    echo "ERROR: GIS数据库备份失败" >&2
    exit 1
  fi

  # 压缩备份文件
  echo "INFO: 正在压缩备份文件..."
  if ! docker exec "$container_name" /bin/bash -c "cd /tmp && tar zcvf gis.tgz gis.sql"; then
    echo "ERROR: GIS备份文件压缩失败" >&2
    exit 1
  fi

  # 复制备份文件到宿主机
  echo "INFO: 正在复制备份文件到宿主机..."
  if ! docker cp "${container_name}:/tmp/gis.tgz" "${BACKUP_DB_PATH}/${backup_filename}"; then
    echo "ERROR: GIS备份文件复制失败" >&2
    exit 1
  fi

  # 清理容器内临时文件
  echo "INFO: 清理容器内临时文件..."
  docker exec "$container_name" /bin/bash -c 'rm -f /tmp/gis.sql /tmp/gis.tgz' || true

  # 验证备份文件
  if [ -f "${BACKUP_DB_PATH}/${backup_filename}" ]; then
    local file_size
    file_size=$(du -h "${BACKUP_DB_PATH}/${backup_filename}" | cut -f1)
    echo "INFO: GIS数据库备份成功 - 文件: $backup_filename, 大小: $file_size"
  else
    echo "ERROR: GIS备份文件验证失败" >&2
    exit 1
  fi
}

#-------------------------------------------------------------------------------
# 函数名: _backup_mes_database
# 功能: 备份MES业务数据库
# 参数:
#   $1: 备份时间戳
# 返回: 无（备份失败时直接退出程序）
# 说明: 使用pg_dump备份MES数据库，压缩后保存到备份目录
#-------------------------------------------------------------------------------
_backup_mes_database() {
  local backup_time="$1"
  local container_name="base_business_postgres"
  local backup_filename="mes.${backup_time}.tgz"

  echo_yellow "备份MES业务数据库..."
  echo "INFO: 容器: $container_name"
  echo "INFO: 数据库: $BLADEX_POSTGRES_DATABASE"
  echo "INFO: 用户: $BLADEX_POSTGRES_USERNAME"

  # 检查容器是否运行
  if ! docker ps --format "{{.Names}}" | grep -q "^${container_name}$"; then
    echo "ERROR: MES PostgreSQL容器 '$container_name' 未运行" >&2
    exit 1
  fi

  # 执行数据库备份
  echo "INFO: 正在执行pg_dump备份..."
  if ! docker exec "$container_name" /bin/bash -c "pg_dump -U ${BLADEX_POSTGRES_USERNAME} ${BLADEX_POSTGRES_DATABASE} > /tmp/mes.sql"; then
    echo "ERROR: MES数据库备份失败" >&2
    exit 1
  fi

  # 压缩备份文件
  echo "INFO: 正在压缩备份文件..."
  if ! docker exec "$container_name" /bin/bash -c "cd /tmp && tar zcvf mes.tgz mes.sql"; then
    echo "ERROR: MES备份文件压缩失败" >&2
    exit 1
  fi

  # 复制备份文件到宿主机
  echo "INFO: 正在复制备份文件到宿主机..."
  if ! docker cp "${container_name}:/tmp/mes.tgz" "${BACKUP_DB_PATH}/${backup_filename}"; then
    echo "ERROR: MES备份文件复制失败" >&2
    exit 1
  fi

  # 清理容器内临时文件
  echo "INFO: 清理容器内临时文件..."
  docker exec "$container_name" /bin/bash -c 'rm -f /tmp/mes.tgz /tmp/mes.sql' || true

  # 验证备份文件
  if [ -f "${BACKUP_DB_PATH}/${backup_filename}" ]; then
    local file_size
    file_size=$(du -h "${BACKUP_DB_PATH}/${backup_filename}" | cut -f1)
    echo "INFO: MES数据库备份成功 - 文件: $backup_filename, 大小: $file_size"
  else
    echo "ERROR: MES备份文件验证失败" >&2
    exit 1
  fi
}

#===============================================================================
# 数据库恢复函数
#===============================================================================

#-------------------------------------------------------------------------------
# 函数名: restore_postgres
# 功能: 恢复PostgreSQL数据库
# 参数:
#   $1: 备份文件路径
#   $2: 数据库类型 (gis|mes)
# 返回: 无（恢复失败时直接退出程序）
# 说明: 从备份文件恢复指定的数据库
#       支持GIS和MES数据库的恢复操作
#-------------------------------------------------------------------------------
restore_postgres() {
  local backup_file="$1"
  local db_type="$2"

  # 检查参数
  if [ -z "$backup_file" ]; then
    echo "ERROR: 备份文件路径不能为空" >&2
    echo "使用方法: restore_postgres <备份文件路径> <数据库类型:gis|mes>" >&2
    exit 1
  fi

  if [ -z "$db_type" ]; then
    echo "ERROR: 数据库类型不能为空" >&2
    echo "使用方法: restore_postgres <备份文件路径> <数据库类型:gis|mes>" >&2
    exit 1
  fi

  # 检查备份文件是否存在
  if [ ! -f "$backup_file" ]; then
    echo "ERROR: 备份文件不存在: $backup_file" >&2
    exit 1
  fi

  # 加载环境变量
  local env_file="$BASE_SERVICE_PATH/.env"
  if [ ! -f "$env_file" ]; then
    echo "ERROR: 环境变量文件不存在: $env_file" >&2
    exit 1
  fi

  source "$env_file"
  _check_postgres_env_vars

  # 根据数据库类型执行恢复
  case "$db_type" in
    "gis")
      _restore_gis_database "$backup_file"
      ;;
    "mes")
      _restore_mes_database "$backup_file"
      ;;
    *)
      echo "ERROR: 不支持的数据库类型: $db_type" >&2
      echo "支持的类型: gis, mes" >&2
      exit 1
      ;;
  esac

  echo_yellow "数据库恢复完成！"
}

#-------------------------------------------------------------------------------
# 函数名: _restore_gis_database
# 功能: 恢复GIS主数据库
# 参数:
#   $1: 备份文件路径
# 返回: 无（恢复失败时直接退出程序）
# 说明: 从备份文件恢复GIS数据库
#-------------------------------------------------------------------------------
_restore_gis_database() {
  local backup_file="$1"
  local container_name="base_postgres"

  echo_yellow "恢复GIS主数据库..."
  echo "INFO: 备份文件: $backup_file"
  echo "INFO: 容器: $container_name"

  # 检查容器是否运行
  if ! docker ps --format "{{.Names}}" | grep -q "^${container_name}$"; then
    echo "ERROR: PostgreSQL容器 '$container_name' 未运行" >&2
    exit 1
  fi

  # 复制备份文件到容器
  echo "INFO: 复制备份文件到容器..."
  if ! docker cp "$backup_file" "${container_name}:/tmp/restore.tgz"; then
    echo "ERROR: 备份文件复制到容器失败" >&2
    exit 1
  fi

  # 解压备份文件
  echo "INFO: 解压备份文件..."
  if ! docker exec "$container_name" /bin/bash -c "cd /tmp && tar zxvf restore.tgz"; then
    echo "ERROR: 备份文件解压失败" >&2
    exit 1
  fi

  # 恢复数据库
  echo "INFO: 正在恢复数据库..."
  if ! docker exec "$container_name" /bin/bash -c "psql -U ${POSTGRES_USERNAME} -d ${POSTGRES_DATABASE} < /tmp/gis.sql"; then
    echo "ERROR: GIS数据库恢复失败" >&2
    exit 1
  fi

  # 清理临时文件
  docker exec "$container_name" /bin/bash -c 'rm -f /tmp/restore.tgz /tmp/gis.sql' || true

  echo "INFO: GIS数据库恢复成功"
}

#-------------------------------------------------------------------------------
# 函数名: _restore_mes_database
# 功能: 恢复MES业务数据库
# 参数:
#   $1: 备份文件路径
# 返回: 无（恢复失败时直接退出程序）
# 说明: 从备份文件恢复MES数据库
#-------------------------------------------------------------------------------
_restore_mes_database() {
  local backup_file="$1"
  local container_name="base_business_postgres"

  echo_yellow "恢复MES业务数据库..."
  echo "INFO: 备份文件: $backup_file"
  echo "INFO: 容器: $container_name"

  # 检查容器是否运行
  if ! docker ps --format "{{.Names}}" | grep -q "^${container_name}$"; then
    echo "ERROR: MES PostgreSQL容器 '$container_name' 未运行" >&2
    exit 1
  fi

  # 复制备份文件到容器
  echo "INFO: 复制备份文件到容器..."
  if ! docker cp "$backup_file" "${container_name}:/tmp/restore.tgz"; then
    echo "ERROR: 备份文件复制到容器失败" >&2
    exit 1
  fi

  # 解压备份文件
  echo "INFO: 解压备份文件..."
  if ! docker exec "$container_name" /bin/bash -c "cd /tmp && tar zxvf restore.tgz"; then
    echo "ERROR: 备份文件解压失败" >&2
    exit 1
  fi

  # 恢复数据库
  echo "INFO: 正在恢复数据库..."
  if ! docker exec "$container_name" /bin/bash -c "psql -U ${BLADEX_POSTGRES_USERNAME} -d ${BLADEX_POSTGRES_DATABASE} < /tmp/mes.sql"; then
    echo "ERROR: MES数据库恢复失败" >&2
    exit 1
  fi

  # 清理临时文件
  docker exec "$container_name" /bin/bash -c 'rm -f /tmp/restore.tgz /tmp/mes.sql' || true

  echo "INFO: MES数据库恢复成功"
}

#===============================================================================
# 备份管理函数
#===============================================================================

#-------------------------------------------------------------------------------
# 函数名: list_backups
# 功能: 列出可用的数据库备份文件
# 参数: 无
# 返回: 无
# 说明: 显示备份目录中的所有数据库备份文件
#       按日期和时间排序显示
#-------------------------------------------------------------------------------
list_backups() {
  echo_yellow "列出可用的数据库备份文件..."

  if [ -z "$BACKUP_BASE_PATH" ]; then
    echo "ERROR: BACKUP_BASE_PATH环境变量未设置" >&2
    exit 1
  fi

  if [ ! -d "$BACKUP_BASE_PATH" ]; then
    echo "INFO: 备份目录不存在: $BACKUP_BASE_PATH"
    return 0
  fi

  echo "INFO: 备份根目录: $BACKUP_BASE_PATH"
  echo ""

  # 查找所有备份文件
  local backup_files
  backup_files=$(find "$BACKUP_BASE_PATH" -name "*.tgz" -type f 2>/dev/null | sort)

  if [ -z "$backup_files" ]; then
    echo "INFO: 未找到任何备份文件"
    return 0
  fi

  echo "可用的备份文件:"
  echo "=================="

  while IFS= read -r file; do
    if [ -f "$file" ]; then
      local file_size
      file_size=$(du -h "$file" | cut -f1)
      local file_date
      file_date=$(stat -c %y "$file" 2>/dev/null | cut -d' ' -f1,2 | cut -d'.' -f1)
      echo "文件: $(basename "$file")"
      echo "路径: $file"
      echo "大小: $file_size"
      echo "时间: $file_date"
      echo "------------------"
    fi
  done <<< "$backup_files"
}

#-------------------------------------------------------------------------------
# 函数名: cleanup_old_backups
# 功能: 清理过期的备份文件
# 参数:
#   $1: 保留天数（默认7天）
# 返回: 无
# 说明: 删除指定天数之前的备份文件，释放存储空间
#-------------------------------------------------------------------------------
cleanup_old_backups() {
  local keep_days="${1:-7}"

  echo_yellow "清理过期的备份文件..."
  echo "INFO: 保留最近 $keep_days 天的备份文件"

  if [ -z "$BACKUP_BASE_PATH" ]; then
    echo "ERROR: BACKUP_BASE_PATH环境变量未设置" >&2
    exit 1
  fi

  if [ ! -d "$BACKUP_BASE_PATH" ]; then
    echo "INFO: 备份目录不存在: $BACKUP_BASE_PATH"
    return 0
  fi

  # 查找并删除过期的备份目录
  local deleted_count=0
  while IFS= read -r dir; do
    if [ -d "$dir" ]; then
      echo "INFO: 删除过期备份目录: $dir"
      rm -rf "$dir"
      ((deleted_count++))
    fi
  done < <(find "$BACKUP_BASE_PATH" -maxdepth 1 -type d -mtime +$keep_days 2>/dev/null)

  echo "INFO: 清理完成，删除了 $deleted_count 个过期备份目录"
}
