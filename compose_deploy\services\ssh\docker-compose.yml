# =============================================================================
# SSH 调试服务 Docker Compose 配置文件
# =============================================================================

# =============================================================================
# 网络配置
# =============================================================================

networks:
  # 中台统一网络 - 与其他服务共享网络环境
  middle:
    external: true
    name: ${DOCKER_NETWORK}

# =============================================================================
# 服务定义
# =============================================================================

services:
  # ---------------------------------------------------------------------------
  # SSH 远程访问服务
  # ---------------------------------------------------------------------------

  ssh:
    # 使用 Alpine Linux 基础的轻量级 SSH 服务镜像
    image: harbor2.qdbdtd.com:8088/middleware/alpine-ssh:3.12
    container_name: debug-ssh-service
    # 服务配置 - 默认不自动重启，需要手动启动
    restart: "no"
    # 环境变量配置
    environment:
      # SSH 登录密码 (从环境变量读取)
      - PASSWORD=${SSH_PASSWORD}
    # 端口映射配置
    ports:
      # 将容器的 22 端口映射到宿主机指定端口
      - "${SSH_PORT}:22"
    # 网络配置 - 接入中台统一网络
    networks:
      - middle
    # 日志配置
    logging:
      driver: "json-file"
      options:
        max-size: "50m"
        max-file: "3"
