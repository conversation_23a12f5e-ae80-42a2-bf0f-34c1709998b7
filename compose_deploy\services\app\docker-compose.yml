#===============================================================================
# YAML 锚点定义 - 可复用的配置模板
#===============================================================================

# 应用服务通用配置
x-app-defaults: &app_defaults
  restart: unless-stopped
  logging:
    driver: "json-file"
    options:
      max-size: "1g"
      max-file: "2"
  networks:
    - middle

# Nginx 前端服务配置模板
x-nginx-defaults: &nginx_defaults
  <<: *app_defaults
  healthcheck:
    test: ["CMD", "nginx", "-t"]
    interval: 30s
    timeout: 10s
    retries: 3
    start_period: 40s

# 后端服务配置模板
x-backend-defaults: &backend_defaults
  <<: *app_defaults
  env_file:
    - ../services/base/.env
    - ../services/app/.env
    - ../services/mos/.env

#===============================================================================
# 网络配置
#===============================================================================

networks:
  middle:
    external: true
    name: ${DOCKER_NETWORK}

#===============================================================================
# 服务定义 - 按业务依赖关系排序
#===============================================================================

services:
  #-----------------------------------------------------------------------------
  # 后端服务 - 业务逻辑层（优先启动）
  #-----------------------------------------------------------------------------

  # 移动应用后端服务
  backend-bd-app:
    <<: *backend_defaults
    container_name: backend-bd-app
    image: harbor2.qdbdtd.com:8088/bjproduct/platform/services/app-backend:${BACKEND_APP_VERSION}
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9001/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    #ports:
    #  - "${APP_BACKEND_PORT}:9001"

  #-----------------------------------------------------------------------------
  # 前端服务 - 用户界面层（依赖后端服务）
  #-----------------------------------------------------------------------------

  # 移动应用前端服务
  frontend-bd-app:
    <<: *nginx_defaults
    container_name: frontend-bd-app
    image: harbor2.qdbdtd.com:8088/bjproduct/platform/front-services/bd-app:${FRONTEND_APP_VERSION}
    environment:
      - UPSTREAM_GIS_API=${UPSTREAM_GIS_API}
      - UPSTREAM_GIS_STATIC=${UPSTREAM_GIS_STATIC}
      - BACKEND_ENDPOINT=http://backend-bd-app:9001
      - BACKEND_DATA_MONITOR=${BACKEND_DATA_MONITOR}
      - BACKEND_RESOLVER=127.0.0.11
      - BACKEND_URL_REWRITE=rewrite ^/api/(.*)$ /$1 break;
      - DOLLAR=$$
    volumes:
      - "./config/nginx.conf:/etc/nginx/conf.d/default.conf.template:ro"
    depends_on:
      - backend-bd-app
    #ports:
    #  - "${APP_PORT}:80"
