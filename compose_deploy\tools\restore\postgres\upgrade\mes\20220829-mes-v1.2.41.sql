--定制化页面删除
UPDATE blade_menu SET is_deleted = 1 WHERE "name" = '烟雾温度联合监测';
UPDATE blade_menu SET is_deleted = 1 WHERE "name" = '风量瓦斯联合监测';
UPDATE blade_menu SET is_deleted = 1 WHERE "id" = 1539912978298572801;
UPDATE blade_menu SET is_deleted = 1 WHERE "id" = 1539912978298572802;
UPDATE blade_menu SET is_deleted = 1 WHERE "id" = 1539912978298572803;
UPDATE blade_menu SET is_deleted = 1 WHERE "id" = 1539912978298572804;
UPDATE blade_menu SET is_deleted = 1 WHERE "id" = 1539912978386653187;
UPDATE blade_menu SET is_deleted = 1 WHERE "id" = 1539912978386653186;
UPDATE blade_menu SET is_deleted = 1 WHERE "id" = 1539912978386653188;
UPDATE blade_menu SET is_deleted = 1 WHERE "id" = 1539912978386653189;
--2.0无用菜单删除
UPDATE blade_menu SET is_deleted = 1 WHERE "id" = 1539910557425995778;
UPDATE blade_menu SET is_deleted = 1 WHERE "id" = 1539910557493104641;
UPDATE blade_menu SET is_deleted = 1 WHERE "id" = 1539910557493104642;
UPDATE blade_menu SET is_deleted = 1 WHERE "id" = 1539910557556019202;
UPDATE blade_menu SET is_deleted = 1 WHERE "id" = 1540260876496404482;
UPDATE blade_menu SET is_deleted = 1 WHERE "id" = 1540260876630622212;
UPDATE blade_menu SET is_deleted = 1 WHERE "id" = 1539910557690236930;
UPDATE blade_menu SET is_deleted = 1 WHERE "id" = 1540260876563513345;
UPDATE blade_menu SET is_deleted = 1 WHERE "id" = 1540260876563513346;
UPDATE blade_menu SET is_deleted = 1 WHERE "id" = 1540260876630622210;
UPDATE blade_menu SET is_deleted = 1 WHERE "id" = 1540260876630622213;
UPDATE blade_menu SET is_deleted = 1 WHERE "id" = 1540260876630622209;
UPDATE blade_menu SET is_deleted = 1 WHERE "id" = 1540260876630622211;
--2.0菜单数据调整
UPDATE blade_menu SET is_deleted = 1 WHERE code = 'folder_delete' OR code = 'file_edit';
UPDATE blade_menu SET "name" = '删除' WHERE code = 'file_delete';
UPDATE blade_menu SET "name" = '编辑' WHERE code = 'folder_edit';
UPDATE blade_menu SET is_deleted = 0 WHERE "id" IN (
	1555426995878281217,
	1123598815738675303,
	1123598815738675304,
	1123598815738675299,
	1123598815738675298,
	1123598815738675305,
	1123598815738675301,
	1123598815738675302,
	1542376576753197058);
--自由排班路由
INSERT INTO "public"."blade_project_route"("id", "parent_id", "name", "path", "product_id", "type", "tenant_id", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted", "code", "alias") VALUES ('.app-business_serve.report.daiban_genban_report', 'base-business_serve', '月度带班跟班表 ', '/app-business_serve/#/report/daiban_genban_report', NULL, 2, '000000', 1123598821738675201, 1123598813738675201, '2022-08-29 09:47:00', 1123598821738675201, '2022-08-29 09:47:00', NULL, 0, NULL, NULL);
INSERT INTO "public"."blade_project_route"("id", "parent_id", "name", "path", "product_id", "type", "tenant_id", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted", "code", "alias") VALUES ('.app-business_serve.surfaceShiftTable', 'base-business_serve', '自动排班--跟班表', '/app-business_serve/#/surfaceShiftTable', NULL, 2, '000000', 1123598821738675201, 1123598813738675201, '2022-08-29 09:46:39', 1123598821738675201, '2022-08-29 09:46:39', NULL, 0, NULL, NULL);
INSERT INTO "public"."blade_project_route"("id", "parent_id", "name", "path", "product_id", "type", "tenant_id", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted", "code", "alias") VALUES ('.app-business_serve.surfaceRestTable', 'base-business_serve', '月度领导跟班调休表', '/app-business_serve/#/surfaceRestTable', NULL, 2, '000000', 1123598821738675201, 1123598813738675201, '2022-08-29 09:46:13', 1123598821738675201, '2022-08-29 09:46:13', NULL, 0, NULL, NULL);
INSERT INTO "public"."blade_project_route"("id", "parent_id", "name", "path", "product_id", "type", "tenant_id", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted", "code", "alias") VALUES ('.app-business_serve.report.lead_genban_work', 'base-business_serve', '月度领导跟班次数表', '/app-business_serve/#/report/lead_genban_work', NULL, 2, '000000', 1123598821738675201, 1123598813738675201, '2022-08-29 09:45:57', 1123598821738675201, '2022-08-29 09:45:57', NULL, 0, NULL, NULL);
INSERT INTO "public"."blade_project_route"("id", "parent_id", "name", "path", "product_id", "type", "tenant_id", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted", "code", "alias") VALUES ('.app-business_serve.surfaceShiftTable2', 'base-business_serve', '跟班领导信息表', '/app-business_serve/#/surfaceShiftTable2', NULL, 2, '000000', 1123598821738675201, 1123598813738675201, '2022-08-29 09:45:33', 1123598821738675201, '2022-08-29 09:45:33', NULL, 0, NULL, NULL);
INSERT INTO "public"."blade_project_route"("id", "parent_id", "name", "path", "product_id", "type", "tenant_id", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted", "code", "alias") VALUES ('.app-business_serve.shiftTable', 'base-business_serve', '自动排班--带班表', '/app-business_serve/#/shiftTable', NULL, 2, '000000', 1123598821738675201, 1123598813738675201, '2022-08-29 09:45:00', 1123598821738675201, '2022-08-29 09:45:00', NULL, 0, NULL, NULL);
INSERT INTO "public"."blade_project_route"("id", "parent_id", "name", "path", "product_id", "type", "tenant_id", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted", "code", "alias") VALUES ('.app-business_serve.restTable', 'base-business_serve', '月度领导带班调休表', '/app-business_serve/#/restTable', NULL, 2, '000000', 1123598821738675201, 1123598813738675201, '2022-08-29 09:44:39', 1123598821738675201, '2022-08-29 09:44:39', NULL, 0, NULL, NULL);
INSERT INTO "public"."blade_project_route"("id", "parent_id", "name", "path", "product_id", "type", "tenant_id", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted", "code", "alias") VALUES ('.app-business_serve.report.lead_work', 'base-business_serve', '月度领导带班次数表', '/app-business_serve/#/report/lead_work', NULL, 2, '000000', 1123598821738675201, 1123598813738675201, '2022-08-29 09:44:20', 1123598821738675201, '2022-08-29 09:44:20', NULL, 0, NULL, NULL);
INSERT INTO "public"."blade_project_route"("id", "parent_id", "name", "path", "product_id", "type", "tenant_id", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted", "code", "alias") VALUES ('.app-business_serve.shiftTable2', 'base-business_serve', '带班领导信息表', '/app-business_serve/#/shiftTable2', NULL, 2, '000000', 1123598821738675201, 1123598813738675201, '2022-08-29 09:44:00', 1123598821738675201, '2022-08-29 09:44:00', NULL, 0, NULL, NULL);
--自由排版菜单
INSERT INTO "public"."blade_menu"("id", "parent_id", "code", "name", "alias", "path", "source", "sort", "category", "action", "is_open", "component", "remark", "is_deleted", "api_path", "api_method", "api_name", "route_id", "navigation_id", "type", "json", "product_id") VALUES (1564068359639666689, 1564068071201574914, NULL, '自动排班--跟班表', NULL, NULL, NULL, 4, 1, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL, 'default', '{"url":"/app-business_serve/#/surfaceShiftTable","topo":"","page":"","tabPageMenuLayout":"","tabPage":[]}', 1422766050562281473);
INSERT INTO "public"."blade_menu"("id", "parent_id", "code", "name", "alias", "path", "source", "sort", "category", "action", "is_open", "component", "remark", "is_deleted", "api_path", "api_method", "api_name", "route_id", "navigation_id", "type", "json", "product_id") VALUES (1564068295923994625, 1564068071201574914, NULL, '月度领导跟班调休表', NULL, NULL, NULL, 3, 1, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL, 'default', '{"url":"/app-business_serve/#/surfaceRestTable","topo":"","page":"","tabPageMenuLayout":"","tabPage":[]}', 1422766050562281473);
INSERT INTO "public"."blade_menu"("id", "parent_id", "code", "name", "alias", "path", "source", "sort", "category", "action", "is_open", "component", "remark", "is_deleted", "api_path", "api_method", "api_name", "route_id", "navigation_id", "type", "json", "product_id") VALUES (1564068235811229698, 1564068071201574914, NULL, '月度领导跟班次数表', NULL, NULL, NULL, 2, 1, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL, 'default', '{"url":"/app-business_serve/#/report/lead_genban_work","topo":"","page":"","tabPageMenuLayout":"","tabPage":[]}', 1422766050562281473);
INSERT INTO "public"."blade_menu"("id", "parent_id", "code", "name", "alias", "path", "source", "sort", "category", "action", "is_open", "component", "remark", "is_deleted", "api_path", "api_method", "api_name", "route_id", "navigation_id", "type", "json", "product_id") VALUES (1564068150104821762, 1564068071201574914, NULL, '跟班领导信息表', NULL, NULL, NULL, 1, 1, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL, 'default', '{"url":"/app-business_serve/#/surfaceShiftTable2","topo":"","page":"","tabPageMenuLayout":"","tabPage":[]}', 1422766050562281473);
INSERT INTO "public"."blade_menu"("id", "parent_id", "code", "name", "alias", "path", "source", "sort", "category", "action", "is_open", "component", "remark", "is_deleted", "api_path", "api_method", "api_name", "route_id", "navigation_id", "type", "json", "product_id") VALUES (1564068071201574914, 1539910558269050881, NULL, '跟班信息', NULL, NULL, NULL, 5, 1, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL, 'default', '{"url":"","topo":"","page":"","tabPageMenuLayout":"","tabPage":[]}', 1422766050562281473);
INSERT INTO "public"."blade_menu"("id", "parent_id", "code", "name", "alias", "path", "source", "sort", "category", "action", "is_open", "component", "remark", "is_deleted", "api_path", "api_method", "api_name", "route_id", "navigation_id", "type", "json", "product_id") VALUES (1564067722252259330, 1564067422082699265, NULL, '自动排班--带班表', NULL, NULL, NULL, 4, 1, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL, 'default', '{"url":"/app-business_serve/#/shiftTable","topo":"","page":"","tabPageMenuLayout":"","tabPage":[]}', 1422766050562281473);
INSERT INTO "public"."blade_menu"("id", "parent_id", "code", "name", "alias", "path", "source", "sort", "category", "action", "is_open", "component", "remark", "is_deleted", "api_path", "api_method", "api_name", "route_id", "navigation_id", "type", "json", "product_id") VALUES (1564067661338382338, 1564067422082699265, NULL, '月度领导带班调休表', NULL, NULL, NULL, 3, 1, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL, 'default', '{"url":"/app-business_serve/#/restTable","topo":"","page":"","tabPageMenuLayout":"","tabPage":[]}', 1422766050562281473);
INSERT INTO "public"."blade_menu"("id", "parent_id", "code", "name", "alias", "path", "source", "sort", "category", "action", "is_open", "component", "remark", "is_deleted", "api_path", "api_method", "api_name", "route_id", "navigation_id", "type", "json", "product_id") VALUES (1564067597790482434, 1564067422082699265, NULL, '月度领导带班次数表', NULL, NULL, NULL, 2, 1, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL, 'default', '{"url":"/app-business_serve/#/report/lead_work","topo":"","page":"","tabPageMenuLayout":"","tabPage":[]}', 1422766050562281473);
INSERT INTO "public"."blade_menu"("id", "parent_id", "code", "name", "alias", "path", "source", "sort", "category", "action", "is_open", "component", "remark", "is_deleted", "api_path", "api_method", "api_name", "route_id", "navigation_id", "type", "json", "product_id") VALUES (1564067512235069441, 1564067422082699265, NULL, '带班领导信息表', NULL, NULL, NULL, 1, 1, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL, 'default', '{"url":"/app-business_serve/#/shiftTable2","topo":"","page":"","tabPageMenuLayout":"","tabPage":[]}', 1422766050562281473);
INSERT INTO "public"."blade_menu"("id", "parent_id", "code", "name", "alias", "path", "source", "sort", "category", "action", "is_open", "component", "remark", "is_deleted", "api_path", "api_method", "api_name", "route_id", "navigation_id", "type", "json", "product_id") VALUES (1564067422082699265, 1539910558269050881, NULL, '带班信息', NULL, NULL, NULL, 5, 1, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL, 'default', '{"url":"","topo":"","page":"","tabPageMenuLayout":"","tabPage":[]}', 1422766050562281473);
INSERT INTO "public"."blade_menu"("id", "parent_id", "code", "name", "alias", "path", "source", "sort", "category", "action", "is_open", "component", "remark", "is_deleted", "api_path", "api_method", "api_name", "route_id", "navigation_id", "type", "json", "product_id") VALUES (1564067351039578114, 1564067252301467650, NULL, '月度带班跟班表', NULL, NULL, NULL, 1, 1, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL, 'default', '{"url":"/app-business_serve/#/report/daiban_genban_report","topo":"","page":"","tabPageMenuLayout":"","tabPage":[]}', 1422766050562281473);
INSERT INTO "public"."blade_menu"("id", "parent_id", "code", "name", "alias", "path", "source", "sort", "category", "action", "is_open", "component", "remark", "is_deleted", "api_path", "api_method", "api_name", "route_id", "navigation_id", "type", "json", "product_id") VALUES (1564067252301467650, 1539910558269050881, NULL, '带班跟班信息', NULL, NULL, NULL, 4, 1, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL, 'default', '{"url":"","topo":"","page":"","tabPageMenuLayout":"","tabPage":[]}', 1422766050562281473);

