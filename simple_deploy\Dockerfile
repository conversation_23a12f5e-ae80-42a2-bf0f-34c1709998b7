# Simple Deploy Dockerfile
# 多阶段构建，减小最终镜像大小

# 构建阶段
FROM golang:1.21-alpine AS builder

# 设置工作目录
WORKDIR /app

# 安装必要的包
RUN apk add --no-cache git gcc musl-dev sqlite-dev

# 复制go mod文件
COPY go.mod go.sum ./

# 下载依赖
RUN go mod download

# 复制源代码
COPY . .

# 构建应用
RUN CGO_ENABLED=1 GOOS=linux go build -ldflags="-w -s" -o simple-deploy cmd/main.go

# 运行阶段
FROM alpine:latest

# 安装必要的运行时依赖
RUN apk add --no-cache ca-certificates sqlite

# 创建非root用户
RUN addgroup -g 1001 -S simple-deploy && \
    adduser -u 1001 -S simple-deploy -G simple-deploy

# 设置工作目录
WORKDIR /app

# 从构建阶段复制二进制文件
COPY --from=builder /app/simple-deploy .

# 创建数据目录
RUN mkdir -p /app/data && \
    chown -R simple-deploy:simple-deploy /app

# 切换到非root用户
USER simple-deploy

# 暴露端口
EXPOSE 8080

# 设置环境变量
ENV GIN_MODE=release

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD wget --no-verbose --tries=1 --spider http://localhost:8080/api/v1/dashboard || exit 1

# 启动命令
CMD ["./simple-deploy", "serve"]
