# 基础服务 (Base Services)

本目录包含系统运行所需的基础设施服务，包括数据库、缓存、消息队列、时序数据库、对象存储等组件。

## 📁 目录结构

```
base/
├── docker-compose.yml    # 基础服务编排配置
├── .env                  # 环境变量配置
├── config/               # 服务配置文件
│   ├── redis.template    # Redis配置模板
│   └── rabbitmq.conf     # RabbitMQ配置
├── tdengine/             # TDengine相关配置
└── vsftp/                # FTP服务相关配置
```

## 🏗️ 包含的服务

根据 `docker-compose.yml` 配置，本模块包含以下服务：

| 服务名称 | 容器名称 | 说明 | 端口 |
|---------|----------|------|------|
| postgres | base_postgres | PostgreSQL主数据库 (PostGIS扩展) | 5432 |
| business-postgres | base_business_postgres | PostgreSQL业务数据库 (PostGIS扩展) | 5432 |
| redis | redis | Redis缓存服务 | 6379 |
| rabbitmq | rabbitmq | RabbitMQ消息队列 | 5672, 15672 |
| tdengine | tdengine | TDengine时序数据库 | 6030-6049 |
| bladex-minio | bladex-minio | MinIO对象存储服务 | 9000 |
| ftpserver | ftpserver | SFTPGo文件传输服务 | 2022, 2121, 12100-12200 |
| rednode | rednode | Node-RED网关服务 | 1880 |

## 🚀 使用方法

### 启动服务

```bash
# 通过主部署脚本启动
./compose_deploy.sh start base

# 或直接使用docker-compose
cd services/base
docker compose up -d
```

### 查看服务状态

```bash
cd services/base
docker compose ps
```

### 查看服务日志

```bash
cd services/base
docker compose logs [service_name]
```

## 🔧 配置说明

主要配置通过 `.env` 文件和 `config/` 目录下的配置文件进行管理。

### 环境变量

| 变量名 | 说明 |
|--------|------|
| POSTGRES_USERNAME | PostgreSQL用户名 |
| POSTGRES_PASSWORD | PostgreSQL密码 |
| POSTGRES_DATABASE | PostgreSQL数据库名 |
| REDIS_PASSWORD | Redis密码 |
| RABBITMQ_USERNAME | RabbitMQ用户名 |
| RABBITMQ_PASSWORD | RabbitMQ密码 |
| BLADEX_MINIO_ACCESS_KEY | MinIO访问密钥 |
| BLADEX_MINIO_SECRET_KEY | MinIO秘密密钥 |

### 数据持久化

| 数据卷名称 | 说明 |
|-----------|------|
| base-postgres | PostgreSQL主库数据 |
| base-business-postgres | PostgreSQL业务库数据 |
| base-redis | Redis数据 |
| base-rabbitmq | RabbitMQ数据 |
| base-tdengine-data | TDengine数据 |
| bladex-minio | MinIO对象存储数据 |
| base-red-node | Node-RED配置数据 |
| base-sftpgo-data | SFTPGo配置数据 |

## 🔗 相关文档

- [主部署脚本说明](../../compose_deploy.sh)
- [服务总览](../README.md)
