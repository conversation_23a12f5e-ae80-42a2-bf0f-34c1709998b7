#!/bin/bash

# Simple Deploy 快速启动脚本
# 用于快速构建、导入配置并启动服务

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
COMPOSE_DEPLOY_PATH="../compose-deploy"

echo "🚀 Simple Deploy 快速启动脚本"
echo "================================"

# 检查Go环境
if ! command -v go &> /dev/null; then
    echo "❌ 错误: 未找到Go环境，请先安装Go"
    exit 1
fi

# 检查Docker环境
if ! command -v docker &> /dev/null; then
    echo "❌ 错误: 未找到Docker环境，请先安装Docker"
    exit 1
fi

# 检查Docker是否运行
if ! docker info &> /dev/null; then
    echo "❌ 错误: Docker守护进程未运行，请启动Docker"
    exit 1
fi

echo "✅ 环境检查通过"

# 构建程序
echo ""
echo "📦 构建Simple Deploy..."
cd "$SCRIPT_DIR"
make build

if [ $? -ne 0 ]; then
    echo "❌ 构建失败"
    exit 1
fi

echo "✅ 构建完成"

# 检查是否存在compose-deploy目录
if [ -d "$COMPOSE_DEPLOY_PATH" ]; then
    echo ""
    echo "📥 导入现有配置..."
    ./build/simple-deploy import "$COMPOSE_DEPLOY_PATH"
    
    if [ $? -eq 0 ]; then
        echo "✅ 配置导入完成"
    else
        echo "⚠️ 配置导入失败，但可以继续启动服务"
    fi
else
    echo "⚠️ 未找到compose-deploy目录，跳过配置导入"
fi

# 启动服务
echo ""
echo "🌐 启动Web服务..."
echo "访问地址: http://localhost:8080"
echo "按 Ctrl+C 停止服务"
echo ""

./build/simple-deploy serve
