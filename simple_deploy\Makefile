# Simple Deploy Makefile

.PHONY: build clean run import serve test deps help

# 变量定义
BINARY_NAME=simple-deploy
BUILD_DIR=build
MAIN_PATH=cmd/main.go
PROJECT_PATH=../compose-deploy

# 默认目标
help: ## 显示帮助信息
	@echo "Simple Deploy - 简单部署管理器"
	@echo ""
	@echo "可用命令:"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  %-15s %s\n", $$1, $$2}' $(MAKEFILE_LIST)

deps: ## 安装依赖
	@echo "安装Go依赖..."
	go mod tidy
	go mod download

build: deps ## 构建程序
	@echo "构建Simple Deploy..."
	@mkdir -p $(BUILD_DIR)
	CGO_ENABLED=1 go build -ldflags="-w -s" -o $(BUILD_DIR)/$(BINARY_NAME) $(MAIN_PATH)
	@echo "构建完成: $(BUILD_DIR)/$(BINARY_NAME)"

build-linux: deps ## 构建Linux版本
	@echo "构建Linux版本..."
	@mkdir -p $(BUILD_DIR)
	GOOS=linux GOARCH=amd64 CGO_ENABLED=1 go build -ldflags="-w -s" -o $(BUILD_DIR)/$(BINARY_NAME)-linux $(MAIN_PATH)
	@echo "构建完成: $(BUILD_DIR)/$(BINARY_NAME)-linux"

build-windows: deps ## 构建Windows版本
	@echo "构建Windows版本..."
	@mkdir -p $(BUILD_DIR)
	GOOS=windows GOARCH=amd64 CGO_ENABLED=1 go build -ldflags="-w -s" -o $(BUILD_DIR)/$(BINARY_NAME).exe $(MAIN_PATH)
	@echo "构建完成: $(BUILD_DIR)/$(BINARY_NAME).exe"

run: build ## 构建并运行服务器
	@echo "启动Simple Deploy..."
	./$(BUILD_DIR)/$(BINARY_NAME) serve

import: build ## 导入现有项目配置
	@echo "导入项目配置..."
	./$(BUILD_DIR)/$(BINARY_NAME) import $(PROJECT_PATH)

serve: ## 仅启动服务器（需要先构建）
	@echo "启动Web服务器..."
	./$(BUILD_DIR)/$(BINARY_NAME) serve

test: ## 运行测试
	@echo "运行测试..."
	go test -v ./...

clean: ## 清理构建文件
	@echo "清理构建文件..."
	rm -rf $(BUILD_DIR)
	rm -f simple-deploy.db
	rm -f *.log

dev: ## 开发模式（热重载）
	@echo "开发模式启动..."
	go run $(MAIN_PATH) serve

install: build ## 安装到系统路径
	@echo "安装Simple Deploy到 /usr/local/bin/..."
	sudo cp $(BUILD_DIR)/$(BINARY_NAME) /usr/local/bin/
	@echo "安装完成，现在可以在任何地方使用 'simple-deploy' 命令"

docker-build: ## 构建Docker镜像
	@echo "构建Docker镜像..."
	docker build -t simple-deploy:latest .

docker-run: docker-build ## 运行Docker容器
	@echo "运行Docker容器..."
	docker run -p 8080:8080 -v /var/run/docker.sock:/var/run/docker.sock simple-deploy:latest

# 快速部署命令
quick-start: build import serve ## 快速开始（构建、导入、启动）

# 发布相关
release: clean build-linux build-windows ## 构建发布版本
	@echo "创建发布包..."
	@mkdir -p $(BUILD_DIR)/release
	cp $(BUILD_DIR)/$(BINARY_NAME)-linux $(BUILD_DIR)/release/
	cp $(BUILD_DIR)/$(BINARY_NAME).exe $(BUILD_DIR)/release/
	cp README.md $(BUILD_DIR)/release/
	cd $(BUILD_DIR) && tar -czf simple-deploy-release.tar.gz release/
	@echo "发布包已创建: $(BUILD_DIR)/simple-deploy-release.tar.gz"

# 数据库相关
db-reset: ## 重置数据库
	@echo "重置数据库..."
	rm -f simple-deploy.db
	@echo "数据库已重置"

db-backup: ## 备份数据库
	@echo "备份数据库..."
	cp simple-deploy.db simple-deploy.db.backup.$(shell date +%Y%m%d_%H%M%S)
	@echo "数据库备份完成"

# 日志查看
logs: ## 查看日志
	@echo "查看最近的日志..."
	tail -f simple-deploy.log

# 状态检查
status: ## 检查服务状态
	@echo "检查Simple Deploy状态..."
	@if pgrep -f "simple-deploy serve" > /dev/null; then \
		echo "✅ Simple Deploy正在运行"; \
		echo "🌐 Web界面: http://localhost:8080"; \
	else \
		echo "❌ Simple Deploy未运行"; \
	fi

# 系统要求检查
check-deps: ## 检查系统依赖
	@echo "检查系统依赖..."
	@command -v go >/dev/null 2>&1 || { echo "❌ Go未安装"; exit 1; }
	@command -v docker >/dev/null 2>&1 || { echo "❌ Docker未安装"; exit 1; }
	@echo "✅ 系统依赖检查通过"
	@go version
	@docker version --format "Docker版本: {{.Server.Version}}"
