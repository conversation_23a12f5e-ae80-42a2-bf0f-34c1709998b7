# Changelog

## v1.2.11

> ⚠️ 升级后请在用户权限系统中添加新的菜单资源

```JSON
[{"resourceEntity":{"name":"点表列表","value":"app-data-application"},"operationEntity":[{"name":"查看","value":"view","description":"查看功能"},{"name":"所有","value":"*.*","description":"全部功能"}],"parameterEntity":[{"key":"page","value":"true","description":null},{"key":"url","value":"/app-data-application/","description":null},{"key":"icon","value":"el-icon-notebook-2","description":null},{"key":"pageIndex","value":0,"description":null}],"children":null}]
```

### 整体变更

- [Feature] 添加点表列表功能
- [Feature] 添加已发布 Topo 图删除功能
- [Feature] Topo 编辑器添加将一组元素复制到另一张 Topo 图中功能
- [Feature] Topo 编辑器支持批量修改一组相同类型元素属性 (目前支持文字、矩形)
- [Feature] Topo 图支持根据绑点类型（开关量、模拟量）设置禁用对应类型点数据弹窗
- [Feature] Topo 编辑器添加元素水平翻转和垂直翻转功能
- [Feature] Topo 编辑器对齐和等间距功能支持旋转的元素
- [Feature] Topo 草稿预览时可直接打开编辑页面
- [Feature] Topo 图模型库图片替换功能
- [Feature] 添加 Topo 编辑模式隐藏导航栏功能
- [Feature] 添加派生点复制功能
- [Feature] 派生点支持在编辑表达式时直接修改参数点 ID
- [Update] 更新 GIS 服务版本至 1.48
- [Fix] 修复 Topo 编辑器中单个元素无法复制和删除的问题
- [Fix] 修复 Topo 编辑器中对齐元素后选择框可能丢失的问题
- [Fix] 修复 Topo 图已发布列表错误
- [Fix] 修复 Topo 编辑器对齐元素时顺序可能排列错误
- [Fix] 修复 Topo 编辑器多选后元素位置有可能错误的问题
- [Fix] 修复数据接入系统选择列表

## v1.2.10

### 整体变更

- [Feature] 数据接入服务添加派生点 WebSocket 实时值订阅
- [Feature] Topo 图编辑器添加导入、导出功能
- [Fix] 修复 Topo 图编辑器使用 shift 多选后对齐元素时位置计算错误的问题
- [Fix] 修复 Topo 图编辑器多选后删除元素只删除一个的问题及选择框不消失的问题
- [Fix] 修复 Topo 图编辑器多选后复制元素新元素位置错误，且无法自动选中新元素的问题

## v1.2.9

### 整体变更

- [Feature] Topo 图编辑器支持元素锁定
- [Feature] Topo 图编辑器支持使用方向键调整单个或者多个元素坐标
- [Feature] Topo 图编辑器添加对一组元素横纵对齐和等间距的功能
- [Feature] 记录 Topo 图列表页面状态，从编辑器返回时自动返回之前的页面
- [Fix] 修复数据接入配置页面编辑点弹窗填充值错误的问题

## v1.2.8

### 整体变更

- [Feature] 数据接入服务添加 WebSocket 实时值订阅接口
- [Feature] Topo 图 Echarts 组件和视频组件支持随 canvas 缩放和移动
- [Fix] 修复 Topo 图移动后点击事件位置错误
- [Fix] 修复 NGINX DNS 缓存可能导致的 502 Bad Gateway 问题
- [Fix] 修复派生点规则校验时值类型错误

## v1.2.7

### 重要说明

此次更新添加了数据接入自动化配置和钉钉审批功能

请根据实际情况配置 `.config.env` 中 MINECODE 和 CMS 服务地址等。

升级后请通过部署脚本启动 `./compose_deploy.sh deploy`

### 整体变更

- [Feature] 数据接入服务添加自动化配置和钉钉审批功能
- [Feature] 重构视频播放组件，支持 hls/flv/mp4
- [Feature] 部署脚本添加配置检查、Docker 初始化和 Docker 镜像拉取等功能
- [Update] 更新 Hasura Graphql Engine 版本
- [Update] 更新 SRS 版本
- [Update] 数据接入派生点规则校验时添加返回计算值提示
- [Fix] 修复 videojs-flow 对 SRS Empty Reply 不释放连接的问题
- [Fix] 修复数据接入服务选择点弹窗 selection 不清空的问题
- [Change] 生产环境关闭 Hasura Console

## v1.2.6

> `note`: 此版本存在 SRS 空连接未被释放的 Bug，会影响视频服务正常播放，请及时更新

### 整体变更

- [Update] 更新 GIS 服务版本
- [Fix] 修复 GIS 服务 Linux 字体显示问题
- [Fix] 修复 GIS 服务 图层编码出错问题

## v1.2.5

`note`: 此版本存在 SRS 空连接未被释放的 Bug，会影响视频服务正常播放，请及时更新

### 重要说明

此次更新添加了数据接入和派生点服务初始化数据，全新部署时会自动导入。

旧版本升级可以手动导入。

```sh
./compose_deploy.sh data-access init
```

### 整体变更

- [Fix] 修复 Topo 矩形动态渲染问题
- [Update] 添加数据接入和派生点服务初始化数据

## v1.2.4

`note`: 此版本存在 SRS 空连接未被释放的 Bug，会影响视频服务正常播放，请及时更新

### 重要说明

> ⚠️ 升级此版本前需要为前端配置服务创建 Docker 命名卷

```sh
docker volume create --name=frontend-config-data
```

> ⚠️ 升级后请在用户权限系统中添加新的菜单资源

```JSON
[{"resourceEntity":{"name":"导航栏配置","value":"publicnavbarconfig"},"operationEntity":[{"name":"查看","value":"view","description":"查看功能"},{"name":"所有","value":"*.*","description":"全部功能"}],"parameterEntity":[{"key":"page","value":"true","description":null},{"key":"url","value":"/public-navbar-config/","description":null},{"key":"icon","value":"el-icon-notebook-2","description":null},{"key":"pageIndex","value":0,"description":null}],"children":null}]
```

> ⚠️ v1.2.4 之前的版本存在脏数据

受派生点服务影响，`v1.2.4` 之前版本的中台服务在初始化时向数据库写入错误的初始数据；  
请在数据接入时注意 `public.data_automation_*` 表的内容。

### 整体变更

- [Refactor] 使用 videojs-flow 替换 node-media-server
- [Feature] 添加导航栏配置前端和前端配置服务
- [Feature] 添加 SSH 服务
- [Update] Topo 编辑器、视频配置、数据接入前端更新导航栏
- [Update] 添加 Nginx Server Location 扩展配置文件目录和示例配置
- [Update] 更新 Topo 渲染器版本
- [Update] 优化 Topo 渲染器视频播放缓存；提升播放稳定
- [Fix] 修复用户权限系统接口权限判断
- [Fix] 修复数据接入派生点配置
- [Fix] 修复 Topo 深色主题背景
- [Fix] 更新派生点服务，修复数据库错误的初始数据
- [Fix] 修复 Node-RED 反向代理 client size 过低造成部署错误
- [Fix] 修复视频配置页面加载时报错

---

## v1.2.3

### 重要说明

> ⚠️ 此版本包含 Migration  
> ⚠️ 此版本包含 Topo 模型库更新，更新完成后请通过以下命令初始化 Topo 图数据

```sh
./compose_deploy.sh init-topo
```

### 整体变更

- [Update] Topo 模型库添加摄像头和动画
- [Update] 部署脚本添加服务重启和 Topo 初始化
- [Update] Migration: public.model_image 表添加 unique key
- [Fix] 修复用户权限系统日志页面权限错误
- [Fix] 修复用户权限系统授权角色样式问题
- [Fix] 修复部署阶段 CPU 资源不足时 MySQL 初始化失败的问题
- [Fix] 修复部署脚本中不同时部署 GIS 和 MOS 服务时的错误

---

## v1.2.2

### 重要说明

> ⚠️ 升级此版本前需要为 RabbitMQ 创建 Docker 命名卷

```sh
docker volume create --name=base-rabbitmq
```

之后再执行升级操作

```sh
./compose_deploy.sh update -t v1.2.2
```

### 整体变更

- [Change] 调整端口转发和反向代理
- [Update] 优化部署脚本和文档
- [Update] 添加 RabbitMQ 备份
- [Fix] 修改用户权限管理页面错别字

---

## v1.2.1

### 整体变更

- [Fix] 修复 Topo 模型库上传失败的问题
- [Fix] 修改 Nginx 缓存策略，仅缓存 200, 301, 302

---

## v1.2.0

### 重要说明

> ⚠️ 此版本不支持直接升级

此版本将 skeleton 平台服务拆分为四组，对容器编排和部署脚本进行了重构。

此版本开始不再直接向容器挂载本地宿主目录，而是改为使用 Docker 命名数据卷。

此版本将 PostgreSQL 数据库迁移文件进行了重新生成，`hdb_catalog.schema_migrations` 中 version `1598333908952` 版本对齐之前版本中的 `1597650486603`

基于以上的原因，此版本不支持直接升级。手动升级请将原目录中的内容迁移至命名卷，并同步数据库 migration 版本。

### 整体变更

- [Feature] Topo PC 端支持 scale in / out
- [Feature] Topo app 端支持 scale in / out
- [Feature] Topo app 端支持组态事件点击 / 触发
- [Feature] Topo 支持基于子系统的模型（图例）筛选
