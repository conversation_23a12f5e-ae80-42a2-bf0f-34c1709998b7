# =============================================================================
# YAML 模板定义 (按功能分组)
# =============================================================================

# 基础服务模板
x-defaults: &defaults
  restart: unless-stopped
  logging:
    driver: "json-file"
    options:
      max-size: "1g"
      max-file: "2"
  networks:
    - middle

# Nginx 前端服务模板
x-nginx-defaults: &nginx-defaults
  <<: *defaults
  env_file:
    - ../services/base/.env
    - ../services/mes/.env
  healthcheck:
    test: ["CMD", "nginx", "-t"]
    interval: 30s
    timeout: 10s
    retries: 3
    start_period: 30s

# Nginx 业务模块服务模板
x-nginx-reset: &nginx-reset
  <<: *nginx-defaults
  command: nginx -g 'daemon off;'
  volumes:
    - "../services/mos/config/nginx-default.conf:/etc/nginx/conf.d/default.conf:ro"

# 后端服务模板
x-backend-defaults: &backend-defaults
  <<: *defaults
  env_file:
    - ../services/base/.env
    - ../services/mes/.env

# =============================================================================
# 网络和存储卷定义
# =============================================================================

networks:
  middle:
    external: true
    name: ${DOCKER_NETWORK}

volumes:
  # MES 业务日志存储
  mes-biz-log:
    external: true

# =============================================================================
# 服务定义 (按 MES 系统架构和依赖关系排序)
# =============================================================================

services:
  # ---------------------------------------------------------------------------
  # 1. 后端服务层 - MES 核心业务服务
  # ---------------------------------------------------------------------------

  # MES 业务系统后端服务
  backend-mes-biz:
    <<: *backend-defaults
    image: harbor2.qdbdtd.com:8088/bjproduct/platform/services/mes_biz:${BACKEND_MES_VERSION}
    container_name: mes-business-service
    environment:
      # 短信通知服务配置
      - SMS_URL=${SMS_URL}
      - SMS_USERNAME=${SMS_USERNAME}
      - SMS_PASSWORD=${SMS_PASSWORD}
    volumes:
      # 业务日志持久化存储
      - "mes-biz-log:/home/<USER>/logs"
    #ports:
    #  - "${PORT_MES_BIZ}:9001"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9001/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 90s

  # ---------------------------------------------------------------------------
  # 2. 前端服务层 - MES 用户界面服务
  # ---------------------------------------------------------------------------

  # MES 前端业务系统
  frontend-base-business_serve:
    <<: *nginx-reset
    image: harbor2.qdbdtd.com:8088/bjproduct/platform/front-services/base-business-serve:${FRONTEND_MES_VERSION}
    container_name: mes-frontend-service
    depends_on:
      - backend-mes-biz
