create user admin pass 'admin@123#';
create user bdtdtd pass 'dev@123#';

CREATE DATABASE mineims KEEP 3650 DAYS 10 CACHE 32 BLOCKS 9 UPDATE 2 PRECISION 'ns';
use mineims;

drop stable if exists mineims.data_derive_realtime;
create stable if not exists mineims.data_derive_realtime (`time` TIMESTAMP, `value` FLOAT, `value_int` INT, `value_text` NCHAR(64), `state` NCHAR(16)) tags (`point_id` NCHAR(128), `system_id` NCHAR(16), `point_type` INT);

drop stable if exists mineims.data_automation_realtime;
create stable if not exists mineims.data_automation_realtime (`time` TIMESTAMP, `state` NCHAR(32), `value` FLOAT, `value_int` INT, `value_text` NCHAR(64), `updated_at` TIMESTAMP) tags (`point_id` NCHAR(255), `data_type` NCHAR(8));

drop stable if exists mineims.mine_stress_history;
create stable if not exists mineims.mine_stress_history (`time` TIM<PERSON><PERSON><PERSON>, `state` NCHAR(32), `value` FLOAT, `value_int` INT, `value_text` NCHAR(64), `updated_at` TIMES<PERSON>MP) tags (`point_id` NCHAR(128));

drop stable if exists mineims.position_history_detail;
create stable if not exists mineims.position_history_detail (`time` TIMESTAMP, `ry_well_code` NCHAR(1), `down_time` TIMESTAMP, `up_time` TIMESTAMP, `area_code` NCHAR(32), `area_name` NCHAR(256), `in_area_time` TIMESTAMP, `station_code` NCHAR(32), `station_name` NCHAR(256), `in_station_time` TIMESTAMP, `class_set_code` NCHAR(32), `class_set_name` NCHAR(50), `work_status` NCHAR(8), `tunnel_code` NCHAR(32), `tunnel_name` NCHAR(256), `tunnel_distance` FLOAT, `x` FLOAT, `y` FLOAT, `z` FLOAT, `track` BINARY(16374), `updated_at` TIMESTAMP) tags (`card_id` NCHAR(32), `person_name` NCHAR(50), `job` NCHAR(32), `work_kind` NCHAR(50), `leader` NCHAR(1), `special_person` NCHAR(1), `department` NCHAR(50));

drop stable if exists mineims.position_history_single;
create stable if not exists mineims.position_history_single (`time` TIMESTAMP, `ry_well_code` NCHAR(1), `down_time` TIMESTAMP, `up_time` TIMESTAMP, `area_code` NCHAR(32), `area_name` NCHAR(256), `in_area_time` TIMESTAMP, `station_code` NCHAR(32), `station_name` NCHAR(256), `in_station_time` TIMESTAMP, `class_set_code` NCHAR(32), `class_set_name` NCHAR(50), `work_status` NCHAR(8), `tunnel_code` NCHAR(32), `tunnel_name` NCHAR(256), `tunnel_distance` FLOAT, `x` FLOAT, `y` FLOAT, `z` FLOAT, `track` BINARY(16374), `updated_at` TIMESTAMP) tags (`card_id` NCHAR(32), `person_name` NCHAR(50), `job` NCHAR(32), `work_kind` NCHAR(50), `leader` NCHAR(1), `special_person` NCHAR(1), `department` NCHAR(50));

drop stable if exists mineims.position_help_realtime;
create stable if not exists mineims.position_help_realtime (`time` TIMESTAMP, `begin_time` TIMESTAMP, `end_time` TIMESTAMP, `alarm_during` INT, `down_time` TIMESTAMP, `area_code` NCHAR(32), `area_name` NCHAR(256), `enter_area_time` TIMESTAMP, `station_code` NCHAR(32), `station_name` NCHAR(256), `enter_station_time` TIMESTAMP, `notice` NCHAR(1), `collect_status` NCHAR(3)) tags (`card_id` NCHAR(32), `person_name` NCHAR(50), `job` NCHAR(32), `work_kind` NCHAR(50), `leader` NCHAR(1), `special_person` NCHAR(1));

drop stable if exists mineims.position_timeout_realtime;
create stable if not exists mineims.position_timeout_realtime (`time` TIMESTAMP, `begin_time` TIMESTAMP, `end_time` TIMESTAMP, `alarm_during` INT, `down_time` TIMESTAMP, `area_code` NCHAR(32), `area_name` NCHAR(256), `enter_area_time` TIMESTAMP, `station_code` NCHAR(32), `station_name` NCHAR(256), `enter_station_time` TIMESTAMP, `notice` NCHAR(1), `collect_status` NCHAR(3)) tags (`card_id` NCHAR(32), `person_name` NCHAR(50), `job` NCHAR(32), `work_kind` NCHAR(50), `leader` NCHAR(1), `special_person` NCHAR(1));

drop stable if exists mineims.position_overman_realtime;
create stable if not exists mineims.position_overman_realtime (`time` TIMESTAMP, `begin_time` TIMESTAMP, `end_time` TIMESTAMP, `alarm_during` INT, `type` NCHAR(32), `current_person_total` INT, `personnel_quota` INT, `area_person` NCHAR(256), `class_set_name` NCHAR(256), `class_set_code` NCHAR(256), `notice` INT, `collect_status` INT) tags (`area_code` NCHAR(32), `area_name` NCHAR(256));

drop stable if exists mineims.position_area_count;
create stable if not exists mineims.position_area_count (`time` TIMESTAMP, `count` INT, `updated_at` TIMESTAMP) tags (`area_code` NCHAR(32), `area_name` NCHAR(256));

drop stable if exists mineims.safety_realtime;
create stable if not exists mineims.safety_realtime (`time` TIMESTAMP, `alarm_status_code` NCHAR(4), `alarm_status_name` NCHAR(16), `value` FLOAT, `value_int` INT, `value_text` NCHAR(64), `unit_code` NCHAR(32), `data_time` TIMESTAMP, `collect_status` INT) tags (`point_id` NCHAR(128), `location` NCHAR(256), `sensor_type_code` NCHAR(16), `sensor_type_name` NCHAR(32), `point_value_type_code` TINYINT);

drop stable if exists mineims.safety_alarm_realtime;
create stable if not exists mineims.safety_alarm_realtime (`time` TIMESTAMP, `begin_time` TIMESTAMP, `end_time` TIMESTAMP, `alarm_during` INT, `unit_code` NCHAR(32), `notice` NCHAR(1), `measures` NCHAR(256), `data_time` TIMESTAMP, `collect_status` INT) tags (`point_id` NCHAR(128), `location` NCHAR(256), `alarm_type_code` NCHAR(16), `alarm_type_name` NCHAR(50), `sensor_type_code` NCHAR(16), `sensor_type_name` NCHAR(32), `point_value_type_code` NCHAR(4));

drop stable if exists mineims.safety_substation_status_realtime;
create stable if not exists mineims.safety_substation_status_realtime (`time` TIMESTAMP, `power_status` NCHAR(2), `run_status` NCHAR(2), `updated_at` TIMESTAMP) tags (`substation_code` NCHAR(64), `substation_name` NCHAR(256));

drop stable if exists mineims.water_observation_realtime;
create stable if not exists mineims.water_observation_realtime (`time` TIMESTAMP, `state` NCHAR(16), `value` FLOAT, `value_int` INT, `value_text` NCHAR(64), `updated_at` TIMESTAMP) tags (`point_id` NCHAR(128), `data_type` NCHAR(8));

drop stable if exists mineims.tube_monitor_history;
create stable if not exists mineims.tube_monitor_history (`time` TIMESTAMP, `value` FLOAT, `value_int` INT, `value_text` NCHAR(64), `state` NCHAR(16), `updated_at` TIMESTAMP) tags (`point_id` NCHAR(128), `location` NCHAR(255), `data_type` NCHAR(8));

drop stable if exists mineims.auxiliary_transport_history;
create stable if not exists mineims.auxiliary_transport_history (`time` TIMESTAMP, `driver_id` NCHAR(16), `driver_name` NCHAR(64), `location_number` NCHAR(16), `x` DOUBLE, `y` DOUBLE, `updated_at` TIMESTAMP) tags (`car_number_name` NCHAR(64));

drop stable if exists mineims.ground_sound_history;
create stable if not exists mineims.ground_sound_history (`time` TIMESTAMP, `value` FLOAT, `value_int` INT, `value_text` NCHAR(64), `status` NCHAR(16), `updated_at` TIMESTAMP) tags (`passageway_number` NCHAR(32), `relation_tunnel` NCHAR(64), `mine_code` NCHAR(8));

drop stable if exists mineims.stress_monitor_realtime;
create stable if not exists mineims.stress_monitor_realtime (`time` TIMESTAMP, `area_id` NCHAR(32), `value` FLOAT, `value_int` INT, `value_text` NCHAR(64), `specific_status` NCHAR(8), `updated_at` TIMESTAMP) tags (`point_id` NCHAR(128), `data_type` NCHAR(8));

drop stable if exists mineims.gas_drainage_realtime;
create stable if not exists mineims.gas_drainage_realtime (`time` TIMESTAMP, `state` NCHAR(8), `value` FLOAT, `value_int` INT, `value_text` NCHAR(64), `alarm` NCHAR(1), `specific_status` NCHAR(8), `updated_at` TIMESTAMP) tags (`point_id` NCHAR(128), `data_type` NCHAR(16));

drop stable if exists mineims.gas_patrol_realtime;
create stable if not exists mineims.gas_patrol_realtime (`time` TIMESTAMP, `state` NCHAR(8), `value` FLOAT, `value_int` INT, `value_text` NCHAR(64), `serial` NCHAR(128), `updated_at` TIMESTAMP) tags (`point_id` NCHAR(128), `data_type` NCHAR(16));

drop stable if exists mineims.automation_warn;
create stable if not exists mineims.automation_warn (`time` TIMESTAMP, `value` FLOAT, `value_int` INT, `value_text` NCHAR(64), `type` INT, `level` NCHAR(2), `state` NCHAR(8), `condition` NCHAR(500)) tags (`point_id` NCHAR(128), `system_id` NCHAR(8), `point_type` NCHAR(8));

drop stable if exists mineims.ground_sound_realtime;
create stable if not exists mineims.ground_sound_realtime (`time` TIMESTAMP, `relation_tunnel` NCHAR(128), `value` FLOAT, `value_int` INT, `value_text` NCHAR(64), `passageway_number` NCHAR(128)) tags (`mine_code` NCHAR(128), `data_type` NCHAR(16));

drop stable if exists mineims.safety_supervision_realtime;
create stable if not exists mineims.safety_supervision_realtime (`time` TIMESTAMP, `state` NCHAR(8), `value` FLOAT, `value_int` INT, `value_text` NCHAR(64), `alarm` BOOL, `alarm_level` NCHAR(8), `specific_status` NCHAR(8), `updated_at` TIMESTAMP) tags (`point_id` NCHAR(128));

drop stable if exists mineims.car_position_realtime;
create stable if not exists mineims.car_position_realtime (`time` TIMESTAMP, `l` INT, `lat` DOUBLE, `lon` DOUBLE, `alt` DOUBLE, `s` DOUBLE, `c` DOUBLE, `h` DOUBLE, `n` INT, `d` INT, `id_del` NCHAR(1), `system_id` NCHAR(8), `data_time` TIMESTAMP, `car_category` NCHAR(16), `car_type` NCHAR(16), `car_specification` NCHAR(16), `driver_code` NCHAR(32), `driver_name` NCHAR(50), `updated_at` TIMESTAMP) tags (`e` NCHAR(128));

drop stable if exists mineims.location_identifiers_position;
create stable if not exists mineims.location_identifiers_position (`time` TIMESTAMP, `point_id` NCHAR(128), `prev_point_id` NCHAR(128), `class_id` NCHAR(128), `state` NCHAR(8), `prev_timestamp` TIMESTAMP, `downhole_time` TIMESTAMP, `downhole_time_long` BIGINT, `geometry_point` NCHAR(128), `updated_at` TIMESTAMP) tags (`card_id` NCHAR(128));

drop stable if exists mineims.vehicle_position;
create stable if not exists mineims.vehicle_position (`time` TIMESTAMP, `x` FLOAT, `y` FLOAT, `map_id` INT, `area_name` NCHAR(64), `state_card` NCHAR(64), `state_object` INT, `state_bz` NCHAR(16), `speed` FLOAT, `mark_name` NCHAR(64), `mark_direction` NCHAR(32), `mark_distance` FLOAT, `td_vehicle` INT, `set_move` INT, `updated_at` TIMESTAMP) tags (`card_id` NCHAR(128));

drop stable if exists mineims.water_observation_realtime_five;
create stable if not exists mineims.water_observation_realtime_five (`time` TIMESTAMP, `temperature` FLOAT, `pressure` FLOAT, `elevation` FLOAT, `depth` FLOAT, `flow` FLOAT, `water_sample_code` NCHAR(64), `state` NCHAR(8), `specific_status` NCHAR(8), `updated_at` TIMESTAMP) tags (`point_id` NCHAR(128));

drop stable if exists mineims.anju_car_location_identifiers_position;
create stable if not exists mineims.anju_car_location_identifiers_position (`time` TIMESTAMP, `point_id` NCHAR(128), `prev_point_id` NCHAR(128), `class_id` NCHAR(128), `state` NCHAR(8), `prev_timestamp` TIMESTAMP, `downhole_time` TIMESTAMP, `downhole_time_long` BIGINT, `geometry_point` NCHAR(128), `updated_at` TIMESTAMP) tags (`card_id` NCHAR(128));

drop stable if exists mineims.information_release_realtime;
create stable if not exists mineims.information_release_realtime (`time` TIMESTAMP, `publisher` NCHAR(128), `data_resource` NCHAR(128), `state` NCHAR(8), `value` FLOAT, `value_int` INT, `value_text` NCHAR(64), `updated_at` TIMESTAMP) tags (`point_id` NCHAR(128), `data_type` NCHAR(16));

drop stable if exists mineims.intelligence_endcup_realtime;
create stable if not exists mineims.intelligence_endcup_realtime (`time` TIMESTAMP, `state` NCHAR(8), `value` FLOAT, `value_int` INT, `value_text` NCHAR(64), `updated_at` TIMESTAMP) tags (`point_id` NCHAR(128), `data_type` NCHAR(16));

drop stable if exists mineims.inverter_realtime;
create stable if not exists mineims.inverter_realtime (`time` TIMESTAMP, `state` NCHAR(32), `value` FLOAT, `value_int` INT, `value_text` NCHAR(64), `updated_at` TIMESTAMP) tags (`point_id` NCHAR(128));

drop stable if exists mineims.vehicle_tbcar_driver_definition;
create stable if not exists mineims.vehicle_tbcar_driver_definition (`time` TIMESTAMP, `driver_name` NCHAR(255), `driver_code` NCHAR(64), `id_del` NCHAR(64), `updated_at` TIMESTAMP) tags (`device_id` NCHAR(64), `system_id` NCHAR(12));

drop stable if exists mineims.belt_weigher_realtime;
create stable if not exists mineims.belt_weigher_realtime (`time` TIMESTAMP, `state` NCHAR(32), `value` FLOAT, `value_int` INT, `value_text` NCHAR(64), `updated_at` TIMESTAMP) tags (`point_id` NCHAR(128), `data_type` NCHAR(16));

drop stable if exists mineims.position_restricted_area_realtime;
create stable if not exists mineims.position_restricted_area_realtime (`time` TIMESTAMP, `begin_time` TIMESTAMP, `end_time` TIMESTAMP, `alarm_during` INT, `card_id` NCHAR(64), `person_name` NCHAR(64), `job` NCHAR(8), `work_kind` NCHAR(8), `leader` BOOL, `special_person` BOOL, `enter_area_time` TIMESTAMP, `station_code` NCHAR(32), `station_name` NCHAR(255), `enter_station_time` TIMESTAMP, `down_time` TIMESTAMP, `class_set_name` NCHAR(32), `class_set_code` NCHAR(32), `collect_status` INT, `notice` BOOL) tags (`area_code` NCHAR(32), `area_name` NCHAR(256));

drop stable if exists mineims.location_overman;
create stable if not exists mineims.location_overman (`time` TIMESTAMP, `area_limit` INT, `count` INT) tags (`area_id` NCHAR(128));

drop stable if exists mineims.gas_early_alarm_realtime;
create stable if not exists mineims.gas_early_alarm_realtime (`time` TIMESTAMP, `state` NCHAR(32), `value` FLOAT, `value_int` INT, `value_text` NCHAR(64), `alarm` BOOL, `alarm_level` NCHAR(8), `updated_at` TIMESTAMP) tags (`point_id` NCHAR(128), `data_type` NCHAR(16));

drop stable if exists mineims.vibration_realtime;
create stable if not exists mineims.vibration_realtime (`time` TIMESTAMP, `state` NCHAR(32), `value` FLOAT, `value_int` INT, `value_text` NCHAR(64), `alarm` BOOL, `alarm_level` NCHAR(8), `updated_at` TIMESTAMP) tags (`point_id` NCHAR(128), `data_type` NCHAR(16));

drop stable if exists mineims.auxiliary_transportation_history;
create stable if not exists mineims.auxiliary_transportation_history (`time` TIMESTAMP, `area_code` NCHAR(32), `area_name` NCHAR(100), `in_area_time` TIMESTAMP, `station_code` NCHAR(100), `station_name` NCHAR(32), `in_station_time` TIMESTAMP, `in_mine_time` TIMESTAMP, `out_mine_time` TIMESTAMP, `car_department` NCHAR(100), `tunnel_code` NCHAR(100), `tunnel_name` NCHAR(100), `tunnel_loc` NCHAR(100), `distance` NCHAR(32), `direction` NCHAR(1), `real_speed` NCHAR(32), `x` FLOAT, `y` FLOAT, `z` FLOAT, `water_temperature` NCHAR(32), `oil_temperature` NCHAR(32), `bearing_temperature` NCHAR(32), `revolution_speed` NCHAR(32), `mileage` NCHAR(32), `run_time` NCHAR(32), `driver_id` NCHAR(32)) tags (`sender_id` NCHAR(32));

drop stable if exists mineims.auxiliary_transportation_alarm_history;
create stable if not exists mineims.auxiliary_transportation_alarm_history (`time` TIMESTAMP, `car_number` NCHAR(100), `car_type` NCHAR(100), `car_department` NCHAR(100), `alarm_id` NCHAR(100), `begin_time` TIMESTAMP, `end_time` TIMESTAMP, `alarm_type_code` NCHAR(100), `alarm_type_name` NCHAR(100), `alarm_sense` NCHAR(100), `alarm_value` NCHAR(100), `is_handle` NCHAR(100), `handled_by` NCHAR(100), `processing_results` NCHAR(100), `data_type` NCHAR(32), `section_name` NCHAR(100), `limit_speed` NCHAR(100), `speed` NCHAR(100), `speed_percentage` NCHAR(100)) tags (`sender_id` NCHAR(32));

drop stable if exists mineims.anemometer;
create stable if not exists mineims.anemometer (`time` TIMESTAMP, `wind_speed` FLOAT, `temperature` FLOAT, `transverse_area` FLOAT, `wind_amount` FLOAT, `humidity` FLOAT, `barometric` FLOAT, `online_status` NCHAR(8), `online_status_text` NCHAR(64), `collect_time` TIMESTAMP, `updated_at` TIMESTAMP) tags (`device_code` NCHAR(32));

drop stable if exists mineims.workface_spray;
create stable if not exists mineims.workface_spray (`time` TIMESTAMP, `work_mode` NCHAR(8), `work_mode_text` NCHAR(16), `connect_mode` NCHAR(8), `connect_mode_text` NCHAR(16), `connect_status` NCHAR(8), `connect_status_text` NCHAR(16), `water_status` NCHAR(8), `water_status_text` NCHAR(16), `air_status` NCHAR(8), `air_status_text` NCHAR(16), `temperature` FLOAT, `humidity` FLOAT, `dust_concentration` FLOAT, `water_pressure` FLOAT, `air_pressure` FLOAT, `holder_count` INT, `holder_status` NCHAR(256), `holder_delayed` INT, `holder_link_count` INT, `holder_location` NCHAR(8), `app_scene` NCHAR(8), `app_scene_text` NCHAR(32), `updated_at` TIMESTAMP) tags (`point_id` NCHAR(64));

drop stable if exists mineims.charging_cabinet;
create stable if not exists mineims.charging_cabinet (`time` TIMESTAMP, `door_state` NCHAR(8), `door_state_text` NCHAR(64), `lamp_state` NCHAR(8), `lamp_state_text` NCHAR(64), `lamp_on_time` TIMESTAMP, `lamp_off_time` TIMESTAMP, `lamp_change_time` TIMESTAMP, `self_rescuer_state` NCHAR(4), `self_rescuer_state_text` NCHAR(64), `charge_count` INT, `updated_at` TIMESTAMP) tags (`point_id` NCHAR(128), `stand_code` NCHAR(64), `door_code` NCHAR(32), `person_card` NCHAR(32));

drop table if exists mineims.position_count;
create table if not exists mineims.position_count (`time` TIMESTAMP, `down_count` INT, `total_count` INT, `up_count` INT, `well_gate_count` INT, `updated_at` TIMESTAMP);

drop table if exists mineims.slight_shock_history;
create table if not exists mineims.slight_shock_history (`time` TIMESTAMP, `event_id` NCHAR(32), `location` NCHAR(64), `level` NCHAR(32), `analysis_results` NCHAR(32), `avg_amplitude` DOUBLE, `max_amplitude` DOUBLE, `amplitude_unit` NCHAR(16), `dominant_frequency` DOUBLE, `dominant_frequency_unit` NCHAR(16), `energy` DOUBLE, `passageway_amount` NCHAR(32), `x` NCHAR(32), `y` NCHAR(32), `z` NCHAR(32), `updated_at` TIMESTAMP);

drop table if exists mineims.gis_warning_alarm_info_history;
create table if not exists mineims.gis_warning_alarm_info_history (`time` TIMESTAMP, `create_at` TIMESTAMP, `warning_alarm_id` INT, `warning_alarm_info_create_time` TIMESTAMP, `warning_alarm_info_param` NCHAR(256), `warning_alarm_info_status` NCHAR(2));

drop table if exists mineims.location_total_employee;
create table if not exists mineims.location_total_employee (`time` TIMESTAMP, `count` INT);

alter user root pass 'bdtdtd@123';
